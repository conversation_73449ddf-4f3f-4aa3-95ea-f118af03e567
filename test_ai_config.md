# AI配置系统测试指南

## 测试步骤

### 1. 基本功能测试

#### 启动设置窗口
```bash
qs -p quickshell/settings.qml
```

#### 验证AI页面
- [ ] 设置窗口启动后自动导航到AI页面
- [ ] 左侧导航栏显示"AI"选项（带神经网络图标）
- [ ] AI页面正确加载，显示所有配置部分

### 2. 模型配置测试

#### 当前模型选择
- [ ] 下拉选择器显示所有可用模型
- [ ] 当前选择的模型正确高亮
- [ ] 模型信息区域显示正确的图标、名称、描述
- [ ] 端点URL和密钥要求状态正确显示

#### 模型切换
- [ ] 选择不同模型时界面立即更新
- [ ] 模型信息区域反映新选择的模型
- [ ] API密钥管理部分根据模型要求显示/隐藏

### 3. API密钥管理测试

#### 密钥输入
- [ ] 密钥输入框默认为密码模式（隐藏文本）
- [ ] "显示"开关可以切换密钥可见性
- [ ] 输入密钥后"保存密钥"按钮变为可用

#### 密钥操作
- [ ] 保存密钥功能正常工作
- [ ] 清除密钥功能正常工作
- [ ] 获取密钥按钮（如果模型支持）正确跳转到外部链接

### 4. 自定义模型测试

#### 添加自定义模型
- [ ] 所有输入字段正常工作
- [ ] API格式选择器（OpenAI/Gemini）正常工作
- [ ] "需要API密钥"开关正常工作
- [ ] 填写必要信息后"添加模型"按钮变为可用
- [ ] 成功添加模型后输入字段自动清空

#### 模型管理
- [ ] 模型管理列表显示所有模型
- [ ] 自定义模型显示"自定义"标签
- [ ] 当前模型正确高亮显示
- [ ] "选择"按钮可以切换模型
- [ ] "删除"按钮仅对自定义模型显示

#### 删除确认
- [ ] 点击删除按钮显示确认对话框
- [ ] 确认对话框显示正确的模型名称
- [ ] 确认删除后模型从列表中移除
- [ ] 取消删除操作正常工作

### 5. 模型参数测试

#### 温度设置
- [ ] 温度滑块正常工作（0-2.0范围）
- [ ] 当前值实时显示
- [ ] 设置值立即保存并生效

#### 系统提示
- [ ] 多行文本输入框正常工作
- [ ] 文本自动换行
- [ ] 输入内容自动保存

### 6. 高级设置测试

#### 请求配置
- [ ] 请求超时设置正常工作
- [ ] 最大重试次数设置正常工作
- [ ] 流式响应开关正常工作
- [ ] 保存聊天历史开关正常工作

#### 安全设置
- [ ] "仅允许本地模型"开关正常工作
- [ ] "验证SSL证书"开关正常工作

### 7. 聊天界面集成测试

#### 模型信息显示
- [ ] AI聊天界面显示当前模型信息
- [ ] 模型图标、名称正确显示
- [ ] 配置状态（自定义、密钥状态）正确显示

#### 快速模型选择
- [ ] 点击模型信息区域打开模型选择器
- [ ] 模型选择器显示所有可用模型
- [ ] 当前模型正确标识
- [ ] 点击模型可以快速切换
- [ ] "管理模型"按钮打开设置窗口

#### 设置访问
- [ ] 设置按钮正确显示
- [ ] 点击设置按钮打开设置窗口
- [ ] 设置窗口自动导航到AI页面

### 8. 配置持久化测试

#### 设置保存
- [ ] 所有配置更改自动保存
- [ ] 重启应用后配置保持不变
- [ ] 自定义模型在重启后仍然可用

#### 导入/导出
- [ ] 导出配置功能正常工作
- [ ] 重置为默认功能正常工作
- [ ] 重置确认对话框正常显示

### 9. 错误处理测试

#### 输入验证
- [ ] 空的模型名称无法添加
- [ ] 空的端点URL无法添加
- [ ] 无效的配置显示适当的错误信息

#### 边界情况
- [ ] 删除当前使用的自定义模型时自动切换到其他模型
- [ ] 没有可用模型时界面正确处理
- [ ] 网络错误时API密钥验证正确处理

### 10. 用户体验测试

#### 界面响应
- [ ] 所有操作响应迅速
- [ ] 动画和过渡效果流畅
- [ ] 工具提示正确显示

#### 可用性
- [ ] 界面布局清晰易懂
- [ ] 操作流程符合直觉
- [ ] 错误信息清晰有用

## 已知问题

1. ~~`QtGraphicalEffects` 模块不可用~~ ✅ 已修复
2. ~~`Qt.openUrlExternally()` 不能用于内部导航~~ ✅ 已修复

## 测试结果

- [ ] 所有基本功能正常工作
- [ ] 模型管理功能完整
- [ ] API密钥管理安全可靠
- [ ] 配置持久化正常
- [ ] 用户界面友好直观

## 备注

测试时请确保：
1. QuickShell环境正确配置
2. AI服务正常运行
3. 配置文件权限正确
4. 网络连接正常（用于API密钥验证）
