2025-06-23 21:54:53.383: [INFO][Sync] Reset engine, reason: 8
2025-06-23 21:54:53.383: [INFO][Sync] Stopped: Bookmarks
2025-06-23 21:54:53.383: [INFO][Sync] Stopped: Preferences
2025-06-23 21:54:53.383: [INFO][Sync] Stopped: Passwords
2025-06-23 21:54:53.383: [INFO][Sync] Stopped: Autofill Profiles
2025-06-23 21:54:53.383: [INFO][Sync] Stopped: Autofill
2025-06-23 21:54:53.383: [INFO][Sync] Stopped: Extensions
2025-06-23 21:54:53.383: [INFO][Sync] Stopped: Sessions
2025-06-23 21:54:53.383: [INFO][Sync] Stopped: Extension settings
2025-06-23 21:54:53.383: [INFO][Sync] Stopped: History Delete Directives
2025-06-23 21:54:53.383: [INFO][Sync] Stopped: Device Info
2025-06-23 21:54:53.383: [INFO][Sync] Stopped: User Consents
2025-06-23 21:54:53.383: [INFO][Sync] Stopped: Send Tab To Self
2025-06-23 21:54:53.383: [INFO][Sync] Stopped: Web Apps
2025-06-23 21:54:53.383: [INFO][Sync] Stopped: History
2025-06-23 21:54:53.383: [INFO][Sync] Stopped: Saved Tab Group
2025-06-23 21:54:53.383: [INFO][Sync] Stopped: WebAuthn Credentials
2025-06-23 21:54:53.383: [INFO][Sync] Stopped: Collection
2025-06-23 21:54:53.383: [INFO][Sync] Stopped: Edge E Drop
2025-06-23 21:54:53.383: [INFO][Sync] Stopped: Edge Hub App Usage
2025-06-23 21:54:53.383: [INFO][Sync] Stopped: Edge Wallet
2025-06-23 21:54:53.383: [INFO][Sync] SyncState after authenticated was: NotSignedIn
2025-06-23 21:54:53.763: [INFO][Sync] Reset engine, reason: 8
2025-06-23 21:54:53.763: [INFO][Sync] Stopped: Bookmarks
2025-06-23 21:54:53.763: [INFO][Sync] Stopped: Preferences
2025-06-23 21:54:53.763: [INFO][Sync] Stopped: Passwords
2025-06-23 21:54:53.763: [INFO][Sync] Stopped: Autofill Profiles
2025-06-23 21:54:53.763: [INFO][Sync] Stopped: Autofill
2025-06-23 21:54:53.763: [INFO][Sync] Stopped: Extensions
2025-06-23 21:54:53.763: [INFO][Sync] Stopped: Sessions
2025-06-23 21:54:53.763: [INFO][Sync] Stopped: Extension settings
2025-06-23 21:54:53.763: [INFO][Sync] Stopped: History Delete Directives
2025-06-23 21:54:53.763: [INFO][Sync] Stopped: Device Info
2025-06-23 21:54:53.763: [INFO][Sync] Stopped: User Consents
2025-06-23 21:54:53.763: [INFO][Sync] Stopped: Send Tab To Self
2025-06-23 21:54:53.763: [INFO][Sync] Stopped: Web Apps
2025-06-23 21:54:53.763: [INFO][Sync] Stopped: History
2025-06-23 21:54:53.763: [INFO][Sync] Stopped: Saved Tab Group
2025-06-23 21:54:53.763: [INFO][Sync] Stopped: WebAuthn Credentials
2025-06-23 21:54:53.763: [INFO][Sync] Stopped: Collection
2025-06-23 21:54:53.763: [INFO][Sync] Stopped: Edge E Drop
2025-06-23 21:54:53.763: [INFO][Sync] Stopped: Edge Hub App Usage
2025-06-23 21:54:53.763: [INFO][Sync] Stopped: Edge Wallet
2025-06-23 21:55:39.491: [INFO][Sync] Try to start sync engine
2025-06-23 21:55:39.491: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Encryption Keys
2025-06-23 21:55:39.491: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-06-23 21:55:39.491: [INFO][SyncEngineBackend::DoInitialize] Control Types added: Encryption Keys
2025-06-23 21:55:39.491: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Encryption Keys with reason: 3
2025-06-23 21:55:39.491: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Configure job was blocked
2025-06-23 21:55:40.436: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-23 21:55:40.436: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-23 21:55:40.436: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-06-23 21:55:40.436: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-23 21:55:40.443: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-23 21:55:40.443: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-23 21:55:40.443: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-06-23 21:55:40.443: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-06-23 21:55:40.443: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Encryption Keys
2025-06-23 21:55:41.657: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-23 21:55:41.660: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-06-23 21:55:41.660: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 3, current state: 0
2025-06-23 21:55:41.660: [WARN][Sync] Crypto error data types: Passwords, Autofill Profiles, Autofill
2025-06-23 21:55:41.660: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys; Allow fail: Passwords, Autofill Profiles, Autofill
2025-06-23 21:55:41.660: [INFO][Sync] Loading: Bookmarks
2025-06-23 21:55:41.660: [INFO][Sync] Loading: Preferences
2025-06-23 21:55:41.660: [INFO][Sync] Loading: Extensions
2025-06-23 21:55:41.660: [INFO][Sync] Loading: Sessions
2025-06-23 21:55:41.660: [INFO][Sync] Loading: Extension settings
2025-06-23 21:55:41.660: [INFO][Sync] Loading: History Delete Directives
2025-06-23 21:55:41.660: [INFO][Sync] Loading: Device Info
2025-06-23 21:55:41.660: [INFO][Sync] Loading: User Consents
2025-06-23 21:55:41.660: [INFO][Sync] Loading: Send Tab To Self
2025-06-23 21:55:41.660: [INFO][Sync] Loading: Web Apps
2025-06-23 21:55:41.660: [INFO][Sync] Loading: History
2025-06-23 21:55:41.660: [INFO][Sync] Loading: Saved Tab Group
2025-06-23 21:55:41.660: [INFO][Sync] Loading: WebAuthn Credentials
2025-06-23 21:55:41.660: [INFO][Sync] Loading: Collection
2025-06-23 21:55:41.660: [INFO][Sync] Loading: Edge E Drop
2025-06-23 21:55:41.660: [INFO][Sync] Loading: Edge Hub App Usage
2025-06-23 21:55:41.660: [INFO][Sync] Loading: Edge Wallet
2025-06-23 21:55:41.662: [INFO][Sync] All data types are ready for configure.
2025-06-23 21:55:42.261: [INFO][Sync] Started DataTypeManager configuration, reason: 5
2025-06-23 21:55:42.261: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 5, current state: 1
2025-06-23 21:55:42.264: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Bookmarks
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Preferences
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Extensions
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Sessions
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Extension settings
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for History Delete Directives
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Device Info
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Send Tab To Self
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Web Apps
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for History
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Saved Tab Group
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for WebAuthn Credentials
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Collection
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Edge E Drop
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Edge Hub App Usage
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Edge Wallet
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 3
2025-06-23 21:55:42.264: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-23 21:55:42.264: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-06-23 21:55:42.264: [WARN][Sync]     Reconfigure requested while configuration ongoing.
2025-06-23 21:55:42.264: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 5, current state: 2
2025-06-23 21:55:42.264: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys; Allow fail: 
2025-06-23 21:55:42.264: [INFO][Sync] Loading: Passwords
2025-06-23 21:55:42.264: [INFO][Sync] Loading: Autofill Profiles
2025-06-23 21:55:42.264: [INFO][Sync] Loading: Autofill
2025-06-23 21:55:42.264: [INFO][Sync] All data types are ready for configure.
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Passwords
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Autofill Profiles
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Autofill
2025-06-23 21:55:42.264: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 5
2025-06-23 21:55:42.264: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-23 21:55:42.265: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-06-23 21:55:42.265: [INFO][Sync] Prepare to configure types: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys
2025-06-23 21:55:42.265: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys with reason: 5
2025-06-23 21:55:42.265: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys
2025-06-23 21:55:52.837: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys
2025-06-23 21:55:52.846: [INFO][Sync] ConfigurationDone, failed: , succeeded: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys, remaining count: 3
2025-06-23 21:55:52.846: [INFO][Sync] Prepare to configure types: Bookmarks, Encryption Keys
2025-06-23 21:55:52.846: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Bookmarks, Encryption Keys with reason: 5
2025-06-23 21:55:52.846: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Bookmarks, Encryption Keys
2025-06-23 21:56:11.450: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Bookmarks, Encryption Keys
2025-06-23 21:56:11.563: [INFO][Sync] ConfigurationDone, failed: , succeeded: Bookmarks, Encryption Keys, remaining count: 2
2025-06-23 21:56:11.563: [INFO][Sync] Prepare to configure types: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, WebAuthn Credentials, Collection, Edge Hub App Usage, Edge Wallet, Encryption Keys
2025-06-23 21:56:11.563: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, WebAuthn Credentials, Collection, Edge Hub App Usage, Edge Wallet, Encryption Keys with reason: 5
2025-06-23 21:56:11.563: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, WebAuthn Credentials, Collection, Edge Hub App Usage, Edge Wallet, Encryption Keys
2025-06-23 21:57:46.853: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, WebAuthn Credentials, Collection, Edge Hub App Usage, Edge Wallet, Encryption Keys
2025-06-23 21:57:46.855: [INFO][Sync] ConfigurationDone, failed: , succeeded: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, WebAuthn Credentials, Collection, Edge Hub App Usage, Edge Wallet, Encryption Keys, remaining count: 1
2025-06-23 21:57:46.855: [INFO][Sync] Prepare to configure types: History, Encryption Keys
2025-06-23 21:57:46.855: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: History, Encryption Keys with reason: 5
2025-06-23 21:57:46.855: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: History, Encryption Keys
2025-06-23 14:02:19.632: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: History, Encryption Keys
2025-06-23 14:02:19.661: [INFO][Sync] ConfigurationDone, failed: , succeeded: History, Encryption Keys, remaining count: 0
2025-06-23 14:02:19.686: [INFO][Sync]     Configuration completed, state: 7
2025-06-23 14:02:19.686: [INFO][Sync] Configured DataTypeManager: Ok
2025-06-23 14:02:19.714: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-06-23 15:18:38.057: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-06-23 15:18:39.225: [INFO][Sync] Try to start sync engine
2025-06-23 15:18:39.230: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-06-23 15:18:39.230: [INFO][SyncEngineBackend::DoInitialize] Control Types added: 
2025-06-23 15:18:39.230: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-23 15:18:39.263: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-06-23 15:18:39.263: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-06-23 15:18:39.263: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-06-23 15:18:39.284: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys; Allow fail: 
2025-06-23 15:18:39.284: [INFO][Sync] Loading: Bookmarks
2025-06-23 15:18:39.284: [INFO][Sync] Loading: Preferences
2025-06-23 15:18:39.284: [INFO][Sync] Loading: Passwords
2025-06-23 15:18:39.284: [INFO][Sync] Loading: Autofill Profiles
2025-06-23 15:18:39.284: [INFO][Sync] Loading: Autofill
2025-06-23 15:18:39.284: [INFO][Sync] Loading: Extensions
2025-06-23 15:18:39.284: [INFO][Sync] Loading: Sessions
2025-06-23 15:18:39.284: [INFO][Sync] Loading: Extension settings
2025-06-23 15:18:39.284: [INFO][Sync] Loading: History Delete Directives
2025-06-23 15:18:39.284: [INFO][Sync] Loading: Device Info
2025-06-23 15:18:39.284: [INFO][Sync] Loading: User Consents
2025-06-23 15:18:39.284: [INFO][Sync] Loading: Send Tab To Self
2025-06-23 15:18:39.284: [INFO][Sync] Loading: Web Apps
2025-06-23 15:18:39.284: [INFO][Sync] Loading: History
2025-06-23 15:18:39.284: [INFO][Sync] Loading: Saved Tab Group
2025-06-23 15:18:39.284: [INFO][Sync] Loading: WebAuthn Credentials
2025-06-23 15:18:39.284: [INFO][Sync] Loading: Collection
2025-06-23 15:18:39.284: [INFO][Sync] Loading: Edge E Drop
2025-06-23 15:18:39.284: [INFO][Sync] Loading: Edge Hub App Usage
2025-06-23 15:18:39.284: [INFO][Sync] Loading: Edge Wallet
2025-06-23 15:18:39.327: [INFO][Sync] All data types are ready for configure.
2025-06-23 15:18:39.327: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-23 15:18:39.327: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-23 15:18:39.329: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-06-23 15:18:39.329: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-23 15:18:39.329: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-23 15:18:39.330: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 3
2025-06-23 15:18:39.330: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-23 15:18:39.330: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-23 15:18:39.333: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-06-23 15:18:39.333: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-23 15:18:39.333: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-23 15:18:39.334: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-06-23 15:18:39.349: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-23 15:18:39.349: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-23 15:18:39.351: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-06-23 15:18:39.365: [INFO][Sync]     Configuration completed, state: 3
2025-06-23 15:18:39.365: [INFO][Sync] Configured DataTypeManager: Ok
2025-06-23 15:18:39.378: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-06-23 15:18:39.816: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-23 15:18:39.816: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-23 15:18:39.816: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-06-23 15:18:39.868: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-23 15:18:39.868: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-23 15:18:39.869: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-06-23 15:18:39.869: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-23 15:18:39.869: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-06-23 15:18:39.870: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-23 15:18:39.870: [INFO][SyncSchedulerImpl::DoNudgeSyncCycleJob] Syncer error: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-23 15:18:39.886: [WARN][Sync] NetworkError: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-23 15:23:24.435: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-06-23 15:23:25.588: [INFO][Sync] Try to start sync engine
2025-06-23 15:23:25.590: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-23 15:23:25.590: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-23 15:23:25.590: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-06-23 15:23:25.593: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-06-23 15:23:25.593: [INFO][SyncEngineBackend::DoInitialize] Control Types added: 
2025-06-23 15:23:25.593: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-23 15:23:25.636: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-06-23 15:23:25.636: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-06-23 15:23:25.636: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-06-23 15:23:25.653: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys; Allow fail: 
2025-06-23 15:23:25.653: [INFO][Sync] Loading: Bookmarks
2025-06-23 15:23:25.653: [INFO][Sync] Loading: Preferences
2025-06-23 15:23:25.653: [INFO][Sync] Loading: Passwords
2025-06-23 15:23:25.653: [INFO][Sync] Loading: Autofill Profiles
2025-06-23 15:23:25.653: [INFO][Sync] Loading: Autofill
2025-06-23 15:23:25.653: [INFO][Sync] Loading: Extensions
2025-06-23 15:23:25.653: [INFO][Sync] Loading: Sessions
2025-06-23 15:23:25.653: [INFO][Sync] Loading: Extension settings
2025-06-23 15:23:25.653: [INFO][Sync] Loading: History Delete Directives
2025-06-23 15:23:25.653: [INFO][Sync] Loading: Device Info
2025-06-23 15:23:25.653: [INFO][Sync] Loading: User Consents
2025-06-23 15:23:25.653: [INFO][Sync] Loading: Send Tab To Self
2025-06-23 15:23:25.653: [INFO][Sync] Loading: Web Apps
2025-06-23 15:23:25.653: [INFO][Sync] Loading: History
2025-06-23 15:23:25.653: [INFO][Sync] Loading: Saved Tab Group
2025-06-23 15:23:25.653: [INFO][Sync] Loading: WebAuthn Credentials
2025-06-23 15:23:25.653: [INFO][Sync] Loading: Collection
2025-06-23 15:23:25.653: [INFO][Sync] Loading: Edge E Drop
2025-06-23 15:23:25.653: [INFO][Sync] Loading: Edge Hub App Usage
2025-06-23 15:23:25.653: [INFO][Sync] Loading: Edge Wallet
2025-06-23 15:23:25.697: [INFO][Sync] All data types are ready for configure.
2025-06-23 15:23:25.697: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-23 15:23:25.697: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-23 15:23:25.699: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-06-23 15:23:25.699: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-23 15:23:25.699: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-23 15:23:25.700: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 3
2025-06-23 15:23:25.700: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-23 15:23:25.700: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-23 15:23:25.700: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-06-23 15:23:25.700: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-23 15:23:25.700: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-23 15:23:25.701: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-06-23 15:23:25.720: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-23 15:23:25.720: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-23 15:23:25.721: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-06-23 15:23:25.735: [INFO][Sync]     Configuration completed, state: 3
2025-06-23 15:23:25.735: [INFO][Sync] Configured DataTypeManager: Ok
2025-06-23 15:23:25.749: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-06-23 15:23:26.176: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-23 15:23:26.176: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-23 15:23:26.176: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-06-23 15:23:26.176: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-23 15:23:26.176: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-06-23 15:33:18.688: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-06-23 15:33:20.823: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-23 15:33:20.823: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-23 15:33:20.823: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-06-23 15:33:21.355: [INFO][Sync] Try to start sync engine
2025-06-23 15:33:21.357: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-23 15:33:21.357: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-23 15:33:21.357: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-06-23 15:33:21.360: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-06-23 15:33:21.360: [INFO][SyncEngineBackend::DoInitialize] Control Types added: 
2025-06-23 15:33:21.360: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-23 15:33:21.360: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-23 15:33:21.360: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-06-23 15:33:21.360: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-23 15:33:21.400: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-06-23 15:33:21.400: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-06-23 15:33:21.400: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-06-23 15:33:21.413: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys; Allow fail: 
2025-06-23 15:33:21.413: [INFO][Sync] Loading: Bookmarks
2025-06-23 15:33:21.413: [INFO][Sync] Loading: Preferences
2025-06-23 15:33:21.413: [INFO][Sync] Loading: Passwords
2025-06-23 15:33:21.413: [INFO][Sync] Loading: Autofill Profiles
2025-06-23 15:33:21.413: [INFO][Sync] Loading: Autofill
2025-06-23 15:33:21.413: [INFO][Sync] Loading: Extensions
2025-06-23 15:33:21.413: [INFO][Sync] Loading: Sessions
2025-06-23 15:33:21.413: [INFO][Sync] Loading: Extension settings
2025-06-23 15:33:21.413: [INFO][Sync] Loading: History Delete Directives
2025-06-23 15:33:21.413: [INFO][Sync] Loading: Device Info
2025-06-23 15:33:21.413: [INFO][Sync] Loading: User Consents
2025-06-23 15:33:21.413: [INFO][Sync] Loading: Send Tab To Self
2025-06-23 15:33:21.413: [INFO][Sync] Loading: Web Apps
2025-06-23 15:33:21.413: [INFO][Sync] Loading: History
2025-06-23 15:33:21.413: [INFO][Sync] Loading: Saved Tab Group
2025-06-23 15:33:21.413: [INFO][Sync] Loading: WebAuthn Credentials
2025-06-23 15:33:21.413: [INFO][Sync] Loading: Collection
2025-06-23 15:33:21.413: [INFO][Sync] Loading: Edge E Drop
2025-06-23 15:33:21.413: [INFO][Sync] Loading: Edge Hub App Usage
2025-06-23 15:33:21.413: [INFO][Sync] Loading: Edge Wallet
2025-06-23 15:33:21.438: [INFO][Sync] All data types are ready for configure.
2025-06-23 15:33:21.438: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-23 15:33:21.438: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-23 15:33:21.439: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-06-23 15:33:21.439: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-23 15:33:21.439: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-23 15:33:21.439: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 3
2025-06-23 15:33:21.440: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-23 15:33:21.440: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-23 15:33:21.442: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-06-23 15:33:21.442: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-23 15:33:21.442: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-23 15:33:21.442: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-06-23 15:33:21.457: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-23 15:33:21.457: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-23 15:33:21.459: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-06-23 15:33:21.473: [INFO][Sync]     Configuration completed, state: 3
2025-06-23 15:33:21.473: [INFO][Sync] Configured DataTypeManager: Ok
2025-06-23 15:33:21.487: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-06-23 15:33:21.506: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-23 15:33:21.506: [INFO][SyncSchedulerImpl::DoNudgeSyncCycleJob] Syncer error: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-23 15:33:21.521: [WARN][Sync] NetworkError: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-24 00:28:04.212: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-06-24 00:28:05.062: [INFO][Sync] Try to start sync engine
2025-06-24 00:28:05.067: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-06-24 00:28:05.067: [INFO][SyncEngineBackend::DoInitialize] Control Types added: 
2025-06-24 00:28:05.067: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 00:28:05.068: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-06-24 00:28:05.068: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-06-24 00:28:05.068: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-06-24 00:28:05.068: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys; Allow fail: 
2025-06-24 00:28:05.068: [INFO][Sync] Loading: Bookmarks
2025-06-24 00:28:05.068: [INFO][Sync] Loading: Preferences
2025-06-24 00:28:05.068: [INFO][Sync] Loading: Passwords
2025-06-24 00:28:05.068: [INFO][Sync] Loading: Autofill Profiles
2025-06-24 00:28:05.068: [INFO][Sync] Loading: Autofill
2025-06-24 00:28:05.068: [INFO][Sync] Loading: Extensions
2025-06-24 00:28:05.068: [INFO][Sync] Loading: Sessions
2025-06-24 00:28:05.068: [INFO][Sync] Loading: Extension settings
2025-06-24 00:28:05.068: [INFO][Sync] Loading: History Delete Directives
2025-06-24 00:28:05.068: [INFO][Sync] Loading: Device Info
2025-06-24 00:28:05.068: [INFO][Sync] Loading: User Consents
2025-06-24 00:28:05.068: [INFO][Sync] Loading: Send Tab To Self
2025-06-24 00:28:05.068: [INFO][Sync] Loading: Web Apps
2025-06-24 00:28:05.068: [INFO][Sync] Loading: History
2025-06-24 00:28:05.068: [INFO][Sync] Loading: Saved Tab Group
2025-06-24 00:28:05.068: [INFO][Sync] Loading: WebAuthn Credentials
2025-06-24 00:28:05.068: [INFO][Sync] Loading: Collection
2025-06-24 00:28:05.068: [INFO][Sync] Loading: Edge E Drop
2025-06-24 00:28:05.068: [INFO][Sync] Loading: Edge Hub App Usage
2025-06-24 00:28:05.068: [INFO][Sync] Loading: Edge Wallet
2025-06-24 00:28:05.794: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 00:28:05.794: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 00:28:05.794: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-06-24 00:28:05.795: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-24 00:28:05.795: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 00:28:05.795: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 00:28:05.795: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-06-24 00:28:05.795: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-06-24 00:28:05.795: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-24 00:28:06.230: [INFO][Sync] All data types are ready for configure.
2025-06-24 00:28:06.230: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 00:28:06.230: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 00:28:06.231: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-06-24 00:28:06.231: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 00:28:06.231: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 00:28:06.231: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 3
2025-06-24 00:28:06.231: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 00:28:06.231: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 00:28:06.231: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-06-24 00:28:06.231: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 00:28:06.231: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 00:28:06.232: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-06-24 00:28:06.246: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 00:28:06.246: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 00:28:06.247: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-06-24 00:28:06.262: [INFO][Sync]     Configuration completed, state: 3
2025-06-24 00:28:06.262: [INFO][Sync] Configured DataTypeManager: Ok
2025-06-24 00:28:06.276: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-06-24 00:28:06.295: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-24 00:28:06.295: [INFO][SyncSchedulerImpl::DoNudgeSyncCycleJob] Syncer error: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-24 00:28:06.310: [WARN][Sync] NetworkError: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-24 01:00:50.014: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-06-24 01:00:50.678: [INFO][Sync] Try to start sync engine
2025-06-24 01:00:50.682: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 01:00:50.682: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 01:00:50.682: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-06-24 01:00:50.684: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-06-24 01:00:50.684: [INFO][SyncEngineBackend::DoInitialize] Control Types added: 
2025-06-24 01:00:50.684: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 01:00:50.737: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-06-24 01:00:50.737: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-06-24 01:00:50.737: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-06-24 01:00:50.753: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys; Allow fail: 
2025-06-24 01:00:50.753: [INFO][Sync] Loading: Bookmarks
2025-06-24 01:00:50.753: [INFO][Sync] Loading: Preferences
2025-06-24 01:00:50.753: [INFO][Sync] Loading: Passwords
2025-06-24 01:00:50.753: [INFO][Sync] Loading: Autofill Profiles
2025-06-24 01:00:50.753: [INFO][Sync] Loading: Autofill
2025-06-24 01:00:50.753: [INFO][Sync] Loading: Extensions
2025-06-24 01:00:50.753: [INFO][Sync] Loading: Sessions
2025-06-24 01:00:50.753: [INFO][Sync] Loading: Extension settings
2025-06-24 01:00:50.753: [INFO][Sync] Loading: History Delete Directives
2025-06-24 01:00:50.753: [INFO][Sync] Loading: Device Info
2025-06-24 01:00:50.753: [INFO][Sync] Loading: User Consents
2025-06-24 01:00:50.753: [INFO][Sync] Loading: Send Tab To Self
2025-06-24 01:00:50.753: [INFO][Sync] Loading: Web Apps
2025-06-24 01:00:50.753: [INFO][Sync] Loading: History
2025-06-24 01:00:50.753: [INFO][Sync] Loading: Saved Tab Group
2025-06-24 01:00:50.753: [INFO][Sync] Loading: WebAuthn Credentials
2025-06-24 01:00:50.753: [INFO][Sync] Loading: Collection
2025-06-24 01:00:50.753: [INFO][Sync] Loading: Edge E Drop
2025-06-24 01:00:50.753: [INFO][Sync] Loading: Edge Hub App Usage
2025-06-24 01:00:50.753: [INFO][Sync] Loading: Edge Wallet
2025-06-24 01:00:50.812: [INFO][Sync] All data types are ready for configure.
2025-06-24 01:00:50.814: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 01:00:50.814: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 01:00:50.822: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-06-24 01:00:50.822: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 01:00:50.822: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 01:00:50.832: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 3
2025-06-24 01:00:50.832: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 01:00:50.832: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 01:00:50.835: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-06-24 01:00:50.836: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 01:00:50.836: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 01:00:50.837: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-06-24 01:00:50.874: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 01:00:50.874: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 01:00:50.878: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-06-24 01:00:50.928: [INFO][Sync]     Configuration completed, state: 3
2025-06-24 01:00:50.929: [INFO][Sync] Configured DataTypeManager: Ok
2025-06-24 01:00:50.977: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-06-24 01:00:51.421: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 01:00:51.421: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 01:00:51.421: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-06-24 01:00:51.421: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-24 01:00:51.421: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-06-24 01:00:53.539: [INFO][Sync] Reset engine, reason: 0
2025-06-24 01:00:53.539: [INFO][Sync] Reset engine with reason: 0
2025-06-24 01:03:07.021: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-06-24 01:03:07.407: [INFO][Sync] Try to start sync engine
2025-06-24 01:03:07.410: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 01:03:07.410: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 01:03:07.410: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-06-24 01:03:07.412: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-06-24 01:03:07.412: [INFO][SyncEngineBackend::DoInitialize] Control Types added: 
2025-06-24 01:03:07.412: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 01:03:07.463: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-06-24 01:03:07.463: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-06-24 01:03:07.463: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-06-24 01:03:07.478: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys; Allow fail: 
2025-06-24 01:03:07.478: [INFO][Sync] Loading: Bookmarks
2025-06-24 01:03:07.479: [INFO][Sync] Loading: Preferences
2025-06-24 01:03:07.479: [INFO][Sync] Loading: Passwords
2025-06-24 01:03:07.479: [INFO][Sync] Loading: Autofill Profiles
2025-06-24 01:03:07.479: [INFO][Sync] Loading: Autofill
2025-06-24 01:03:07.479: [INFO][Sync] Loading: Extensions
2025-06-24 01:03:07.479: [INFO][Sync] Loading: Sessions
2025-06-24 01:03:07.479: [INFO][Sync] Loading: Extension settings
2025-06-24 01:03:07.479: [INFO][Sync] Loading: History Delete Directives
2025-06-24 01:03:07.479: [INFO][Sync] Loading: Device Info
2025-06-24 01:03:07.479: [INFO][Sync] Loading: User Consents
2025-06-24 01:03:07.479: [INFO][Sync] Loading: Send Tab To Self
2025-06-24 01:03:07.479: [INFO][Sync] Loading: Web Apps
2025-06-24 01:03:07.479: [INFO][Sync] Loading: History
2025-06-24 01:03:07.479: [INFO][Sync] Loading: Saved Tab Group
2025-06-24 01:03:07.479: [INFO][Sync] Loading: WebAuthn Credentials
2025-06-24 01:03:07.479: [INFO][Sync] Loading: Collection
2025-06-24 01:03:07.479: [INFO][Sync] Loading: Edge E Drop
2025-06-24 01:03:07.479: [INFO][Sync] Loading: Edge Hub App Usage
2025-06-24 01:03:07.479: [INFO][Sync] Loading: Edge Wallet
2025-06-24 01:03:07.526: [INFO][Sync] All data types are ready for configure.
2025-06-24 01:03:07.526: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 01:03:07.526: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 01:03:07.528: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-06-24 01:03:07.529: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 01:03:07.529: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 01:03:07.529: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 3
2025-06-24 01:03:07.530: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 01:03:07.530: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 01:03:07.533: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-06-24 01:03:07.533: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 01:03:07.533: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 01:03:07.534: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-06-24 01:03:07.550: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 01:03:07.550: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 01:03:07.551: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-06-24 01:03:07.566: [INFO][Sync]     Configuration completed, state: 3
2025-06-24 01:03:07.566: [INFO][Sync] Configured DataTypeManager: Ok
2025-06-24 01:03:07.581: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-06-24 01:03:08.487: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 01:03:08.487: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 01:03:08.487: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-06-24 01:03:08.487: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-24 01:03:08.487: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-06-24 01:31:18.336: [INFO][Sync] Reset engine, reason: 0
2025-06-24 01:31:18.336: [INFO][Sync] Reset engine with reason: 0
2025-06-24 01:54:30.207: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-06-24 01:54:31.919: [INFO][Sync] Try to start sync engine
2025-06-24 01:54:31.920: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 01:54:31.920: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 01:54:31.920: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-06-24 01:54:32.056: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-06-24 01:54:32.056: [INFO][SyncEngineBackend::DoInitialize] Control Types added: 
2025-06-24 01:54:32.056: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 01:54:32.090: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-06-24 01:54:32.090: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-06-24 01:54:32.090: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-06-24 01:54:32.102: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys; Allow fail: 
2025-06-24 01:54:32.102: [INFO][Sync] Loading: Bookmarks
2025-06-24 01:54:32.102: [INFO][Sync] Loading: Preferences
2025-06-24 01:54:32.102: [INFO][Sync] Loading: Passwords
2025-06-24 01:54:32.102: [INFO][Sync] Loading: Autofill Profiles
2025-06-24 01:54:32.102: [INFO][Sync] Loading: Autofill
2025-06-24 01:54:32.102: [INFO][Sync] Loading: Extensions
2025-06-24 01:54:32.102: [INFO][Sync] Loading: Sessions
2025-06-24 01:54:32.102: [INFO][Sync] Loading: Extension settings
2025-06-24 01:54:32.102: [INFO][Sync] Loading: History Delete Directives
2025-06-24 01:54:32.102: [INFO][Sync] Loading: Device Info
2025-06-24 01:54:32.102: [INFO][Sync] Loading: User Consents
2025-06-24 01:54:32.102: [INFO][Sync] Loading: Send Tab To Self
2025-06-24 01:54:32.102: [INFO][Sync] Loading: Web Apps
2025-06-24 01:54:32.102: [INFO][Sync] Loading: History
2025-06-24 01:54:32.102: [INFO][Sync] Loading: Saved Tab Group
2025-06-24 01:54:32.102: [INFO][Sync] Loading: WebAuthn Credentials
2025-06-24 01:54:32.102: [INFO][Sync] Loading: Collection
2025-06-24 01:54:32.102: [INFO][Sync] Loading: Edge E Drop
2025-06-24 01:54:32.102: [INFO][Sync] Loading: Edge Hub App Usage
2025-06-24 01:54:32.102: [INFO][Sync] Loading: Edge Wallet
2025-06-24 01:54:32.393: [INFO][Sync] All data types are ready for configure.
2025-06-24 01:54:32.393: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 01:54:32.393: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 01:54:32.394: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-06-24 01:54:32.394: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 01:54:32.394: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 01:54:32.395: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 3
2025-06-24 01:54:32.395: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 01:54:32.395: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 01:54:32.395: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-06-24 01:54:32.395: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 01:54:32.395: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 01:54:32.395: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-06-24 01:54:32.409: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 01:54:32.409: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 01:54:32.415: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-06-24 01:54:32.430: [INFO][Sync]     Configuration completed, state: 3
2025-06-24 01:54:32.430: [INFO][Sync] Configured DataTypeManager: Ok
2025-06-24 01:54:32.444: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-06-24 01:54:32.719: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 01:54:32.719: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 01:54:32.719: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-06-24 01:54:32.719: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-24 01:54:32.719: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-06-24 01:59:38.530: [INFO][Sync] Reset engine, reason: 0
2025-06-24 01:59:38.530: [INFO][Sync] Reset engine with reason: 0
2025-06-24 02:09:17.105: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-06-24 02:09:17.961: [INFO][Sync] Try to start sync engine
2025-06-24 02:09:17.963: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 02:09:17.963: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 02:09:17.963: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-06-24 02:09:17.964: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-06-24 02:09:17.964: [INFO][SyncEngineBackend::DoInitialize] Control Types added: 
2025-06-24 02:09:17.964: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 02:09:18.007: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-06-24 02:09:18.007: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-06-24 02:09:18.007: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-06-24 02:09:18.022: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys; Allow fail: 
2025-06-24 02:09:18.022: [INFO][Sync] Loading: Bookmarks
2025-06-24 02:09:18.022: [INFO][Sync] Loading: Preferences
2025-06-24 02:09:18.023: [INFO][Sync] Loading: Passwords
2025-06-24 02:09:18.023: [INFO][Sync] Loading: Autofill Profiles
2025-06-24 02:09:18.023: [INFO][Sync] Loading: Autofill
2025-06-24 02:09:18.023: [INFO][Sync] Loading: Extensions
2025-06-24 02:09:18.023: [INFO][Sync] Loading: Sessions
2025-06-24 02:09:18.023: [INFO][Sync] Loading: Extension settings
2025-06-24 02:09:18.023: [INFO][Sync] Loading: History Delete Directives
2025-06-24 02:09:18.023: [INFO][Sync] Loading: Device Info
2025-06-24 02:09:18.023: [INFO][Sync] Loading: User Consents
2025-06-24 02:09:18.023: [INFO][Sync] Loading: Send Tab To Self
2025-06-24 02:09:18.023: [INFO][Sync] Loading: Web Apps
2025-06-24 02:09:18.023: [INFO][Sync] Loading: History
2025-06-24 02:09:18.023: [INFO][Sync] Loading: Saved Tab Group
2025-06-24 02:09:18.023: [INFO][Sync] Loading: WebAuthn Credentials
2025-06-24 02:09:18.023: [INFO][Sync] Loading: Collection
2025-06-24 02:09:18.023: [INFO][Sync] Loading: Edge E Drop
2025-06-24 02:09:18.023: [INFO][Sync] Loading: Edge Hub App Usage
2025-06-24 02:09:18.023: [INFO][Sync] Loading: Edge Wallet
2025-06-24 02:09:18.250: [INFO][Sync] All data types are ready for configure.
2025-06-24 02:09:18.250: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 02:09:18.250: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 02:09:18.250: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-06-24 02:09:18.250: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 02:09:18.250: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 02:09:18.251: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 3
2025-06-24 02:09:18.251: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 02:09:18.251: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 02:09:18.252: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-06-24 02:09:18.252: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 02:09:18.252: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 02:09:18.252: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-06-24 02:09:18.266: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 02:09:18.266: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 02:09:18.267: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-06-24 02:09:18.280: [INFO][Sync]     Configuration completed, state: 3
2025-06-24 02:09:18.281: [INFO][Sync] Configured DataTypeManager: Ok
2025-06-24 02:09:18.297: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-06-24 02:09:18.545: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 02:09:18.545: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 02:09:18.545: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-06-24 02:09:18.546: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-24 02:09:18.546: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-06-24 02:13:06.083: [INFO][Sync] Reset engine, reason: 0
2025-06-24 02:13:06.083: [INFO][Sync] Reset engine with reason: 0
2025-06-24 02:17:41.755: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-06-24 02:17:42.256: [INFO][Sync] Try to start sync engine
2025-06-24 02:17:42.259: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 02:17:42.259: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 02:17:42.259: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-06-24 02:17:42.261: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-06-24 02:17:42.261: [INFO][SyncEngineBackend::DoInitialize] Control Types added: 
2025-06-24 02:17:42.261: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 02:17:42.306: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-06-24 02:17:42.306: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-06-24 02:17:42.306: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-06-24 02:17:42.323: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys; Allow fail: 
2025-06-24 02:17:42.323: [INFO][Sync] Loading: Bookmarks
2025-06-24 02:17:42.323: [INFO][Sync] Loading: Preferences
2025-06-24 02:17:42.323: [INFO][Sync] Loading: Passwords
2025-06-24 02:17:42.323: [INFO][Sync] Loading: Autofill Profiles
2025-06-24 02:17:42.323: [INFO][Sync] Loading: Autofill
2025-06-24 02:17:42.323: [INFO][Sync] Loading: Extensions
2025-06-24 02:17:42.323: [INFO][Sync] Loading: Sessions
2025-06-24 02:17:42.323: [INFO][Sync] Loading: Extension settings
2025-06-24 02:17:42.323: [INFO][Sync] Loading: History Delete Directives
2025-06-24 02:17:42.323: [INFO][Sync] Loading: Device Info
2025-06-24 02:17:42.323: [INFO][Sync] Loading: User Consents
2025-06-24 02:17:42.323: [INFO][Sync] Loading: Send Tab To Self
2025-06-24 02:17:42.323: [INFO][Sync] Loading: Web Apps
2025-06-24 02:17:42.323: [INFO][Sync] Loading: History
2025-06-24 02:17:42.323: [INFO][Sync] Loading: Saved Tab Group
2025-06-24 02:17:42.323: [INFO][Sync] Loading: WebAuthn Credentials
2025-06-24 02:17:42.323: [INFO][Sync] Loading: Collection
2025-06-24 02:17:42.323: [INFO][Sync] Loading: Edge E Drop
2025-06-24 02:17:42.323: [INFO][Sync] Loading: Edge Hub App Usage
2025-06-24 02:17:42.323: [INFO][Sync] Loading: Edge Wallet
2025-06-24 02:17:42.354: [INFO][Sync] All data types are ready for configure.
2025-06-24 02:17:42.354: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 02:17:42.354: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 02:17:42.355: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-06-24 02:17:42.355: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 02:17:42.356: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 02:17:42.356: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 3
2025-06-24 02:17:42.356: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 02:17:42.356: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 02:17:42.357: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-06-24 02:17:42.357: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 02:17:42.357: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 02:17:42.358: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-06-24 02:17:42.373: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 02:17:42.373: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 02:17:42.376: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-06-24 02:17:42.391: [INFO][Sync]     Configuration completed, state: 3
2025-06-24 02:17:42.391: [INFO][Sync] Configured DataTypeManager: Ok
2025-06-24 02:17:42.405: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-06-24 02:17:43.080: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 02:17:43.080: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 02:17:43.080: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-06-24 02:17:43.080: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-24 02:17:43.080: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-06-24 02:17:43.081: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-24 02:17:43.081: [INFO][SyncSchedulerImpl::DoNudgeSyncCycleJob] Syncer error: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-24 02:17:43.095: [WARN][Sync] NetworkError: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-24 04:14:24.745: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-06-24 04:14:25.318: [INFO][Sync] Try to start sync engine
2025-06-24 04:14:25.321: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 04:14:25.321: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 04:14:25.321: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-06-24 04:14:25.323: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-06-24 04:14:25.323: [INFO][SyncEngineBackend::DoInitialize] Control Types added: 
2025-06-24 04:14:25.323: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 04:14:25.380: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-06-24 04:14:25.380: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-06-24 04:14:25.380: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-06-24 04:14:25.394: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys; Allow fail: 
2025-06-24 04:14:25.394: [INFO][Sync] Loading: Bookmarks
2025-06-24 04:14:25.394: [INFO][Sync] Loading: Preferences
2025-06-24 04:14:25.394: [INFO][Sync] Loading: Passwords
2025-06-24 04:14:25.394: [INFO][Sync] Loading: Autofill Profiles
2025-06-24 04:14:25.394: [INFO][Sync] Loading: Autofill
2025-06-24 04:14:25.395: [INFO][Sync] Loading: Extensions
2025-06-24 04:14:25.395: [INFO][Sync] Loading: Sessions
2025-06-24 04:14:25.395: [INFO][Sync] Loading: Extension settings
2025-06-24 04:14:25.395: [INFO][Sync] Loading: History Delete Directives
2025-06-24 04:14:25.395: [INFO][Sync] Loading: Device Info
2025-06-24 04:14:25.395: [INFO][Sync] Loading: User Consents
2025-06-24 04:14:25.395: [INFO][Sync] Loading: Send Tab To Self
2025-06-24 04:14:25.395: [INFO][Sync] Loading: Web Apps
2025-06-24 04:14:25.395: [INFO][Sync] Loading: History
2025-06-24 04:14:25.395: [INFO][Sync] Loading: Saved Tab Group
2025-06-24 04:14:25.395: [INFO][Sync] Loading: WebAuthn Credentials
2025-06-24 04:14:25.395: [INFO][Sync] Loading: Collection
2025-06-24 04:14:25.395: [INFO][Sync] Loading: Edge E Drop
2025-06-24 04:14:25.395: [INFO][Sync] Loading: Edge Hub App Usage
2025-06-24 04:14:25.395: [INFO][Sync] Loading: Edge Wallet
2025-06-24 04:14:25.428: [INFO][Sync] All data types are ready for configure.
2025-06-24 04:14:25.429: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 04:14:25.429: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 04:14:25.429: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-06-24 04:14:25.429: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 04:14:25.429: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 04:14:25.431: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 3
2025-06-24 04:14:25.431: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 04:14:25.431: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 04:14:25.434: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-06-24 04:14:25.434: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 04:14:25.434: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 04:14:25.435: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-06-24 04:14:25.450: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 04:14:25.450: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 04:14:25.451: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-06-24 04:14:25.465: [INFO][Sync]     Configuration completed, state: 3
2025-06-24 04:14:25.465: [INFO][Sync] Configured DataTypeManager: Ok
2025-06-24 04:14:25.479: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-06-24 04:14:26.288: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 04:14:26.288: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 04:14:26.288: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-06-24 04:14:26.288: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-24 04:14:26.288: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-06-24 04:14:26.290: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-24 04:14:26.290: [INFO][SyncSchedulerImpl::DoNudgeSyncCycleJob] Syncer error: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-24 04:14:26.304: [WARN][Sync] NetworkError: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-24 07:18:38.551: [INFO][Sync] Reset engine, reason: 0
2025-06-24 07:18:38.551: [INFO][Sync] Reset engine with reason: 0
2025-06-24 07:21:30.955: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-06-24 07:21:31.462: [INFO][Sync] Try to start sync engine
2025-06-24 07:21:31.464: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 07:21:31.464: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 07:21:31.464: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-06-24 07:21:31.470: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-06-24 07:21:31.470: [INFO][SyncEngineBackend::DoInitialize] Control Types added: 
2025-06-24 07:21:31.470: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 07:21:31.555: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-06-24 07:21:31.555: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-06-24 07:21:31.555: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-06-24 07:21:31.570: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys; Allow fail: 
2025-06-24 07:21:31.570: [INFO][Sync] Loading: Bookmarks
2025-06-24 07:21:31.570: [INFO][Sync] Loading: Preferences
2025-06-24 07:21:31.570: [INFO][Sync] Loading: Passwords
2025-06-24 07:21:31.570: [INFO][Sync] Loading: Autofill Profiles
2025-06-24 07:21:31.570: [INFO][Sync] Loading: Autofill
2025-06-24 07:21:31.570: [INFO][Sync] Loading: Extensions
2025-06-24 07:21:31.570: [INFO][Sync] Loading: Sessions
2025-06-24 07:21:31.570: [INFO][Sync] Loading: Extension settings
2025-06-24 07:21:31.570: [INFO][Sync] Loading: History Delete Directives
2025-06-24 07:21:31.571: [INFO][Sync] Loading: Device Info
2025-06-24 07:21:31.571: [INFO][Sync] Loading: User Consents
2025-06-24 07:21:31.571: [INFO][Sync] Loading: Send Tab To Self
2025-06-24 07:21:31.571: [INFO][Sync] Loading: Web Apps
2025-06-24 07:21:31.571: [INFO][Sync] Loading: History
2025-06-24 07:21:31.571: [INFO][Sync] Loading: Saved Tab Group
2025-06-24 07:21:31.571: [INFO][Sync] Loading: WebAuthn Credentials
2025-06-24 07:21:31.571: [INFO][Sync] Loading: Collection
2025-06-24 07:21:31.571: [INFO][Sync] Loading: Edge E Drop
2025-06-24 07:21:31.571: [INFO][Sync] Loading: Edge Hub App Usage
2025-06-24 07:21:31.571: [INFO][Sync] Loading: Edge Wallet
2025-06-24 07:21:31.615: [INFO][Sync] All data types are ready for configure.
2025-06-24 07:21:31.615: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 07:21:31.615: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 07:21:31.618: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-06-24 07:21:31.618: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 07:21:31.618: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 07:21:31.619: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 3
2025-06-24 07:21:31.619: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 07:21:31.619: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 07:21:31.622: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-06-24 07:21:31.622: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 07:21:31.622: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 07:21:31.623: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-06-24 07:21:31.638: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 07:21:31.638: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 07:21:31.652: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-06-24 07:21:31.668: [INFO][Sync]     Configuration completed, state: 3
2025-06-24 07:21:31.674: [INFO][Sync] Configured DataTypeManager: Ok
2025-06-24 07:21:31.689: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-06-24 07:21:32.060: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 07:21:32.060: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 07:21:32.060: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-06-24 07:21:32.060: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-24 07:21:32.060: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-06-24 07:25:09.381: [INFO][Sync] Reset engine, reason: 0
2025-06-24 07:25:09.381: [INFO][Sync] Reset engine with reason: 0
2025-06-24 07:25:19.353: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-06-24 07:25:19.783: [INFO][Sync] Try to start sync engine
2025-06-24 07:25:19.785: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 07:25:19.785: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 07:25:19.785: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-06-24 07:25:19.787: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-06-24 07:25:19.787: [INFO][SyncEngineBackend::DoInitialize] Control Types added: 
2025-06-24 07:25:19.787: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 07:25:19.832: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-06-24 07:25:19.832: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-06-24 07:25:19.832: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-06-24 07:25:19.847: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys; Allow fail: 
2025-06-24 07:25:19.847: [INFO][Sync] Loading: Bookmarks
2025-06-24 07:25:19.847: [INFO][Sync] Loading: Preferences
2025-06-24 07:25:19.847: [INFO][Sync] Loading: Passwords
2025-06-24 07:25:19.847: [INFO][Sync] Loading: Autofill Profiles
2025-06-24 07:25:19.847: [INFO][Sync] Loading: Autofill
2025-06-24 07:25:19.847: [INFO][Sync] Loading: Extensions
2025-06-24 07:25:19.847: [INFO][Sync] Loading: Sessions
2025-06-24 07:25:19.847: [INFO][Sync] Loading: Extension settings
2025-06-24 07:25:19.847: [INFO][Sync] Loading: History Delete Directives
2025-06-24 07:25:19.847: [INFO][Sync] Loading: Device Info
2025-06-24 07:25:19.847: [INFO][Sync] Loading: User Consents
2025-06-24 07:25:19.847: [INFO][Sync] Loading: Send Tab To Self
2025-06-24 07:25:19.847: [INFO][Sync] Loading: Web Apps
2025-06-24 07:25:19.847: [INFO][Sync] Loading: History
2025-06-24 07:25:19.847: [INFO][Sync] Loading: Saved Tab Group
2025-06-24 07:25:19.847: [INFO][Sync] Loading: WebAuthn Credentials
2025-06-24 07:25:19.847: [INFO][Sync] Loading: Collection
2025-06-24 07:25:19.847: [INFO][Sync] Loading: Edge E Drop
2025-06-24 07:25:19.847: [INFO][Sync] Loading: Edge Hub App Usage
2025-06-24 07:25:19.847: [INFO][Sync] Loading: Edge Wallet
2025-06-24 07:25:19.887: [INFO][Sync] All data types are ready for configure.
2025-06-24 07:25:19.887: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 07:25:19.887: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 07:25:19.893: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-06-24 07:25:19.893: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 07:25:19.893: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 07:25:19.894: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 3
2025-06-24 07:25:19.894: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 07:25:19.894: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 07:25:19.894: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-06-24 07:25:19.894: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 07:25:19.894: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 07:25:19.894: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-06-24 07:25:19.910: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 07:25:19.910: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 07:25:19.911: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-06-24 07:25:19.928: [INFO][Sync]     Configuration completed, state: 3
2025-06-24 07:25:19.929: [INFO][Sync] Configured DataTypeManager: Ok
2025-06-24 07:25:19.945: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-06-24 07:25:20.397: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 07:25:20.397: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 07:25:20.397: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-06-24 07:25:20.397: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-24 07:25:20.397: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-06-24 08:12:35.972: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_CONNECTION_CLOSED)
2025-06-24 08:12:35.972: [INFO][Commit::ReportFullCommitFailure] Commit Sessions, History failed with error: NetworkError
2025-06-24 08:12:35.972: [INFO][SyncSchedulerImpl::DoNudgeSyncCycleJob] Syncer error: Network error (ERR_CONNECTION_CLOSED)
2025-06-24 08:12:35.987: [WARN][Sync] NetworkError: Network error (ERR_CONNECTION_CLOSED)
2025-06-24 08:34:08.806: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-06-24 08:34:09.781: [INFO][Sync] Try to start sync engine
2025-06-24 08:34:09.786: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-06-24 08:34:09.786: [INFO][SyncEngineBackend::DoInitialize] Control Types added: 
2025-06-24 08:34:09.786: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 08:34:09.823: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-06-24 08:34:09.823: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-06-24 08:34:09.823: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-06-24 08:34:09.837: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys; Allow fail: 
2025-06-24 08:34:09.837: [INFO][Sync] Loading: Bookmarks
2025-06-24 08:34:09.837: [INFO][Sync] Loading: Preferences
2025-06-24 08:34:09.837: [INFO][Sync] Loading: Passwords
2025-06-24 08:34:09.837: [INFO][Sync] Loading: Autofill Profiles
2025-06-24 08:34:09.837: [INFO][Sync] Loading: Autofill
2025-06-24 08:34:09.837: [INFO][Sync] Loading: Extensions
2025-06-24 08:34:09.837: [INFO][Sync] Loading: Sessions
2025-06-24 08:34:09.837: [INFO][Sync] Loading: Extension settings
2025-06-24 08:34:09.837: [INFO][Sync] Loading: History Delete Directives
2025-06-24 08:34:09.837: [INFO][Sync] Loading: Device Info
2025-06-24 08:34:09.837: [INFO][Sync] Loading: User Consents
2025-06-24 08:34:09.837: [INFO][Sync] Loading: Send Tab To Self
2025-06-24 08:34:09.837: [INFO][Sync] Loading: Web Apps
2025-06-24 08:34:09.837: [INFO][Sync] Loading: History
2025-06-24 08:34:09.837: [INFO][Sync] Loading: Saved Tab Group
2025-06-24 08:34:09.837: [INFO][Sync] Loading: WebAuthn Credentials
2025-06-24 08:34:09.837: [INFO][Sync] Loading: Collection
2025-06-24 08:34:09.837: [INFO][Sync] Loading: Edge E Drop
2025-06-24 08:34:09.837: [INFO][Sync] Loading: Edge Hub App Usage
2025-06-24 08:34:09.837: [INFO][Sync] Loading: Edge Wallet
2025-06-24 08:34:09.875: [INFO][Sync] All data types are ready for configure.
2025-06-24 08:34:09.875: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 08:34:09.875: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 08:34:09.881: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-06-24 08:34:09.881: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 08:34:09.881: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 08:34:09.884: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 3
2025-06-24 08:34:09.884: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 08:34:09.884: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 08:34:09.885: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-06-24 08:34:09.885: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 08:34:09.885: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 08:34:09.886: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-06-24 08:34:09.901: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 08:34:09.901: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 08:34:09.903: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-06-24 08:34:09.918: [INFO][Sync]     Configuration completed, state: 3
2025-06-24 08:34:09.918: [INFO][Sync] Configured DataTypeManager: Ok
2025-06-24 08:34:09.932: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-06-24 08:34:10.224: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 08:34:10.224: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 08:34:10.224: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-06-24 08:34:10.224: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-24 08:34:10.422: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 08:34:10.422: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 08:34:10.422: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-06-24 08:34:10.422: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-06-24 08:34:10.422: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-24 08:34:10.441: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-24 08:34:10.441: [INFO][SyncSchedulerImpl::DoNudgeSyncCycleJob] Syncer error: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-24 08:34:10.455: [WARN][Sync] NetworkError: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-24 08:52:37.747: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-06-24 08:52:39.977: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 08:52:39.977: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 08:52:39.977: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-06-24 08:52:40.030: [INFO][Sync] Try to start sync engine
2025-06-24 08:52:40.033: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 08:52:40.033: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 08:52:40.033: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-06-24 08:52:40.035: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-06-24 08:52:40.035: [INFO][SyncEngineBackend::DoInitialize] Control Types added: 
2025-06-24 08:52:40.035: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 08:52:40.035: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-24 08:52:40.035: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-06-24 08:52:40.035: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-24 08:52:40.091: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-06-24 08:52:40.091: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-06-24 08:52:40.091: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-06-24 08:52:40.109: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys; Allow fail: 
2025-06-24 08:52:40.109: [INFO][Sync] Loading: Bookmarks
2025-06-24 08:52:40.109: [INFO][Sync] Loading: Preferences
2025-06-24 08:52:40.109: [INFO][Sync] Loading: Passwords
2025-06-24 08:52:40.109: [INFO][Sync] Loading: Autofill Profiles
2025-06-24 08:52:40.109: [INFO][Sync] Loading: Autofill
2025-06-24 08:52:40.109: [INFO][Sync] Loading: Extensions
2025-06-24 08:52:40.109: [INFO][Sync] Loading: Sessions
2025-06-24 08:52:40.109: [INFO][Sync] Loading: Extension settings
2025-06-24 08:52:40.109: [INFO][Sync] Loading: History Delete Directives
2025-06-24 08:52:40.109: [INFO][Sync] Loading: Device Info
2025-06-24 08:52:40.109: [INFO][Sync] Loading: User Consents
2025-06-24 08:52:40.109: [INFO][Sync] Loading: Send Tab To Self
2025-06-24 08:52:40.109: [INFO][Sync] Loading: Web Apps
2025-06-24 08:52:40.109: [INFO][Sync] Loading: History
2025-06-24 08:52:40.109: [INFO][Sync] Loading: Saved Tab Group
2025-06-24 08:52:40.109: [INFO][Sync] Loading: WebAuthn Credentials
2025-06-24 08:52:40.109: [INFO][Sync] Loading: Collection
2025-06-24 08:52:40.109: [INFO][Sync] Loading: Edge E Drop
2025-06-24 08:52:40.109: [INFO][Sync] Loading: Edge Hub App Usage
2025-06-24 08:52:40.109: [INFO][Sync] Loading: Edge Wallet
2025-06-24 08:52:40.157: [INFO][Sync] All data types are ready for configure.
2025-06-24 08:52:40.158: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 08:52:40.158: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 08:52:40.158: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-06-24 08:52:40.158: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 08:52:40.158: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 08:52:40.159: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 3
2025-06-24 08:52:40.159: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 08:52:40.159: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 08:52:40.159: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-06-24 08:52:40.159: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 08:52:40.159: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 08:52:40.159: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-06-24 08:52:40.176: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 08:52:40.176: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 08:52:40.177: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-06-24 08:52:40.192: [INFO][Sync]     Configuration completed, state: 3
2025-06-24 08:52:40.193: [INFO][Sync] Configured DataTypeManager: Ok
2025-06-24 08:52:40.209: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-06-24 08:52:40.241: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-24 08:52:40.241: [INFO][SyncSchedulerImpl::DoNudgeSyncCycleJob] Syncer error: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-24 08:52:40.261: [WARN][Sync] NetworkError: Network error (ERR_PROXY_CONNECTION_FAILED)
2025-06-24 10:14:43.078: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-06-24 10:14:43.603: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: PERSISTENT_ERROR, EDGE_AUTH_ERROR: 3, 24, 4b0
2025-06-24 10:14:43.603: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-06-24 10:14:43.603: [ERROR][Sync]     Engine is not allowed to run, reason: 000000
2025-06-24 10:14:43.609: [INFO][Sync] Reset engine, reason: 6
2025-06-24 10:14:43.609: [INFO][Sync] Stopped: Bookmarks
2025-06-24 10:14:43.609: [INFO][Sync] Stopped: Preferences
2025-06-24 10:14:43.609: [INFO][Sync] Stopped: Passwords
2025-06-24 10:14:43.609: [INFO][Sync] Stopped: Autofill Profiles
2025-06-24 10:14:43.609: [INFO][Sync] Stopped: Autofill
2025-06-24 10:14:43.609: [INFO][Sync] Stopped: Extensions
2025-06-24 10:14:43.609: [INFO][Sync] Stopped: Sessions
2025-06-24 10:14:43.609: [INFO][Sync] Stopped: Extension settings
2025-06-24 10:14:43.609: [INFO][Sync] Stopped: History Delete Directives
2025-06-24 10:14:43.609: [INFO][Sync] Stopped: Device Info
2025-06-24 10:14:43.609: [INFO][Sync] Stopped: User Consents
2025-06-24 10:14:43.609: [INFO][Sync] Stopped: Send Tab To Self
2025-06-24 10:14:43.609: [INFO][Sync] Stopped: Web Apps
2025-06-24 10:14:43.609: [INFO][Sync] Stopped: History
2025-06-24 10:14:43.609: [INFO][Sync] Stopped: Saved Tab Group
2025-06-24 10:14:43.609: [INFO][Sync] Stopped: WebAuthn Credentials
2025-06-24 10:14:43.609: [INFO][Sync] Stopped: Collection
2025-06-24 10:14:43.609: [INFO][Sync] Stopped: Edge E Drop
2025-06-24 10:14:43.609: [INFO][Sync] Stopped: Edge Hub App Usage
2025-06-24 10:14:43.609: [INFO][Sync] Stopped: Edge Wallet
2025-06-24 10:14:43.609: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: PERSISTENT_ERROR, EDGE_AUTH_ERROR: 3, 24, 4b0
2025-06-24 10:14:43.609: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-06-24 10:15:08.781: [INFO][Sync] Reset engine, reason: 0
2025-06-24 10:16:21.363: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-06-24 10:16:21.662: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: PERSISTENT_ERROR, EDGE_AUTH_ERROR: 3, 24, 4b0
2025-06-24 10:16:21.662: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-06-24 10:16:21.662: [ERROR][Sync]     Engine is not allowed to run, reason: 000000
2025-06-24 10:16:21.668: [INFO][Sync] Reset engine, reason: 6
2025-06-24 10:16:21.668: [INFO][Sync] Stopped: Bookmarks
2025-06-24 10:16:21.668: [INFO][Sync] Stopped: Preferences
2025-06-24 10:16:21.668: [INFO][Sync] Stopped: Passwords
2025-06-24 10:16:21.668: [INFO][Sync] Stopped: Autofill Profiles
2025-06-24 10:16:21.668: [INFO][Sync] Stopped: Autofill
2025-06-24 10:16:21.668: [INFO][Sync] Stopped: Extensions
2025-06-24 10:16:21.668: [INFO][Sync] Stopped: Sessions
2025-06-24 10:16:21.668: [INFO][Sync] Stopped: Extension settings
2025-06-24 10:16:21.668: [INFO][Sync] Stopped: History Delete Directives
2025-06-24 10:16:21.668: [INFO][Sync] Stopped: Device Info
2025-06-24 10:16:21.668: [INFO][Sync] Stopped: User Consents
2025-06-24 10:16:21.668: [INFO][Sync] Stopped: Send Tab To Self
2025-06-24 10:16:21.668: [INFO][Sync] Stopped: Web Apps
2025-06-24 10:16:21.668: [INFO][Sync] Stopped: History
2025-06-24 10:16:21.668: [INFO][Sync] Stopped: Saved Tab Group
2025-06-24 10:16:21.668: [INFO][Sync] Stopped: WebAuthn Credentials
2025-06-24 10:16:21.668: [INFO][Sync] Stopped: Collection
2025-06-24 10:16:21.668: [INFO][Sync] Stopped: Edge E Drop
2025-06-24 10:16:21.668: [INFO][Sync] Stopped: Edge Hub App Usage
2025-06-24 10:16:21.668: [INFO][Sync] Stopped: Edge Wallet
2025-06-24 10:16:21.668: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: PERSISTENT_ERROR, EDGE_AUTH_ERROR: 3, 24, 4b0
2025-06-24 10:16:21.668: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-06-24 10:26:07.930: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-06-24 10:26:08.308: [INFO][Sync] Try to start sync engine
2025-06-24 10:26:08.312: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-06-24 10:26:08.312: [INFO][SyncEngineBackend::DoInitialize] Control Types added: 
2025-06-24 10:26:08.312: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 10:26:08.314: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 10:26:08.314: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 10:26:08.314: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-06-24 10:26:08.365: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-06-24 10:26:08.365: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-06-24 10:26:08.365: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-06-24 10:26:08.378: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys; Allow fail: 
2025-06-24 10:26:08.378: [INFO][Sync] Loading: Bookmarks
2025-06-24 10:26:08.378: [INFO][Sync] Loading: Preferences
2025-06-24 10:26:08.378: [INFO][Sync] Loading: Passwords
2025-06-24 10:26:08.378: [INFO][Sync] Loading: Autofill Profiles
2025-06-24 10:26:08.378: [INFO][Sync] Loading: Autofill
2025-06-24 10:26:08.378: [INFO][Sync] Loading: Extensions
2025-06-24 10:26:08.378: [INFO][Sync] Loading: Sessions
2025-06-24 10:26:08.378: [INFO][Sync] Loading: Extension settings
2025-06-24 10:26:08.378: [INFO][Sync] Loading: History Delete Directives
2025-06-24 10:26:08.378: [INFO][Sync] Loading: Device Info
2025-06-24 10:26:08.378: [INFO][Sync] Loading: User Consents
2025-06-24 10:26:08.378: [INFO][Sync] Loading: Send Tab To Self
2025-06-24 10:26:08.378: [INFO][Sync] Loading: Web Apps
2025-06-24 10:26:08.378: [INFO][Sync] Loading: History
2025-06-24 10:26:08.378: [INFO][Sync] Loading: Saved Tab Group
2025-06-24 10:26:08.378: [INFO][Sync] Loading: WebAuthn Credentials
2025-06-24 10:26:08.378: [INFO][Sync] Loading: Collection
2025-06-24 10:26:08.378: [INFO][Sync] Loading: Edge E Drop
2025-06-24 10:26:08.378: [INFO][Sync] Loading: Edge Hub App Usage
2025-06-24 10:26:08.378: [INFO][Sync] Loading: Edge Wallet
2025-06-24 10:26:08.407: [INFO][Sync] All data types are ready for configure.
2025-06-24 10:26:08.407: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Passwords
2025-06-24 10:26:08.407: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Send Tab To Self
2025-06-24 10:26:08.407: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 10:26:08.407: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 10:26:08.411: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-06-24 10:26:08.411: [INFO][Sync] Prepare to configure types: Passwords, Send Tab To Self, Encryption Keys
2025-06-24 10:26:08.411: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Passwords, Send Tab To Self, Encryption Keys with reason: 4
2025-06-24 10:26:08.411: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Configure job was blocked
2025-06-24 10:26:09.241: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 10:26:09.241: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 10:26:09.241: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-06-24 10:26:09.241: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-24 10:26:09.241: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-06-24 10:26:09.241: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Passwords, Send Tab To Self, Encryption Keys
2025-06-24 10:26:26.580: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Passwords, Send Tab To Self, Encryption Keys
2025-06-24 10:26:26.595: [INFO][Sync] ConfigurationDone, failed: , succeeded: Passwords, Send Tab To Self, Encryption Keys, remaining count: 3
2025-06-24 10:26:26.595: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 10:26:26.595: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 10:26:26.597: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-06-24 10:26:26.597: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 10:26:26.597: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 10:26:26.597: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-06-24 10:26:26.612: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 10:26:26.612: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 10:26:26.612: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-06-24 10:26:26.625: [INFO][Sync]     Configuration completed, state: 3
2025-06-24 10:26:26.626: [INFO][Sync] Configured DataTypeManager: Ok
2025-06-24 10:26:26.639: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-06-24 10:29:08.473: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-06-24 10:29:09.781: [INFO][Sync] Try to start sync engine
2025-06-24 10:29:09.783: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 10:29:09.783: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 10:29:09.783: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-06-24 10:29:09.785: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-06-24 10:29:09.785: [INFO][SyncEngineBackend::DoInitialize] Control Types added: 
2025-06-24 10:29:09.785: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 10:29:09.828: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-06-24 10:29:09.828: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-06-24 10:29:09.828: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-06-24 10:29:09.842: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys; Allow fail: 
2025-06-24 10:29:09.842: [INFO][Sync] Loading: Bookmarks
2025-06-24 10:29:09.842: [INFO][Sync] Loading: Preferences
2025-06-24 10:29:09.842: [INFO][Sync] Loading: Passwords
2025-06-24 10:29:09.842: [INFO][Sync] Loading: Autofill Profiles
2025-06-24 10:29:09.842: [INFO][Sync] Loading: Autofill
2025-06-24 10:29:09.842: [INFO][Sync] Loading: Extensions
2025-06-24 10:29:09.842: [INFO][Sync] Loading: Sessions
2025-06-24 10:29:09.842: [INFO][Sync] Loading: Extension settings
2025-06-24 10:29:09.842: [INFO][Sync] Loading: History Delete Directives
2025-06-24 10:29:09.842: [INFO][Sync] Loading: Device Info
2025-06-24 10:29:09.842: [INFO][Sync] Loading: User Consents
2025-06-24 10:29:09.842: [INFO][Sync] Loading: Send Tab To Self
2025-06-24 10:29:09.842: [INFO][Sync] Loading: Web Apps
2025-06-24 10:29:09.842: [INFO][Sync] Loading: History
2025-06-24 10:29:09.842: [INFO][Sync] Loading: Saved Tab Group
2025-06-24 10:29:09.842: [INFO][Sync] Loading: WebAuthn Credentials
2025-06-24 10:29:09.842: [INFO][Sync] Loading: Collection
2025-06-24 10:29:09.842: [INFO][Sync] Loading: Edge E Drop
2025-06-24 10:29:09.842: [INFO][Sync] Loading: Edge Hub App Usage
2025-06-24 10:29:09.842: [INFO][Sync] Loading: Edge Wallet
2025-06-24 10:29:10.061: [INFO][Sync] All data types are ready for configure.
2025-06-24 10:29:10.061: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 10:29:10.061: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 10:29:10.062: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-06-24 10:29:10.062: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 10:29:10.062: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 10:29:10.062: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 3
2025-06-24 10:29:10.062: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 10:29:10.062: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 10:29:10.062: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-06-24 10:29:10.062: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 10:29:10.062: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 10:29:10.062: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-06-24 10:29:10.079: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-06-24 10:29:10.079: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-06-24 10:29:10.079: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-06-24 10:29:10.096: [INFO][Sync]     Configuration completed, state: 3
2025-06-24 10:29:10.096: [INFO][Sync] Configured DataTypeManager: Ok
2025-06-24 10:29:10.110: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-06-24 10:29:10.144: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-06-24 10:29:10.144: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-06-24 10:29:10.144: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-06-24 10:29:10.144: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-06-24 10:29:10.144: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-06-24 11:06:04.692: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-06-24 11:06:05.605: [INFO][Sync] Try to start sync engine
2025-06-24 11:06:05.606: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Encryption Keys
2025-06-24 11:06:05.606: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-06-24 11:06:05.606: [INFO][SyncEngineBackend::DoInitialize] Control Types added: Encryption Keys
2025-06-24 11:06:05.606: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Encryption Keys with reason: 3
2025-06-24 11:06:05.606: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Configure job was blocked
2025-06-24 11:06:19.328: [INFO][Sync] Reset engine, reason: 0
2025-06-24 11:06:19.328: [INFO][Sync] Reset engine with reason: 0
