0:12:38 Tab2 FinishNav173
0:12:38 Tab2 PageLoad #failure
0:13:18 Tab3 StartNav174 #renderer-user #link
0:13:19 Tab3 FinishNav174
0:13:19 Browser1 Switch from Tab3 to Tab2 at 1
0:13:19 Widget Closed: StatusBubble
0:13:19 SwitchTab_Click
0:13:19 Tab3 StartNav175 #renderer-script #auto_subframe
0:13:19 Tab3 FinishNav175
0:13:19 Tab3 StartNav176 #renderer-script #auto_subframe
0:13:19 Tab3 FinishNav176
0:13:19 Tab3 PageLoad
0:13:19 Tab3 PageLoad
0:13:20 Tab3 PageLoad
0:13:21 Tab2 StartNav177 #renderer-script #link
0:13:21 Tab2 FinishNav177
0:13:21 Browser1 Switch from Tab2 to Tab3 at 2
0:13:21 Widget Closed: StatusBubble
0:13:21 SwitchTab_Click
0:13:21 Tab3 StartNav178 #renderer-script #auto_subframe
0:13:22 Tab3 FinishNav178
0:13:22 Tab3 PageLoad
0:13:22 Tab2 StartNav179 #renderer-user #link
0:13:22 Tab2 FinishNav179
0:13:25 Browser1 Insert active Tab4 at 3
0:13:25 Widget Closed: StatusBubble
0:13:25 Tab4 StartNav180 #renderer-user #link
0:13:25 Media.Hidden
0:13:26 Tab4 FinishNav180
0:13:26 Tab4 PageLoad
0:13:27 Tab4 StartNav181 #renderer-script #auto_subframe
0:13:27 Tab4 FinishNav181
0:13:27 Tab4 PageLoad
0:13:27 Tab4 StartNav182 #renderer-script #link
0:13:27 Tab4 FinishNav182
0:13:27 Tab4 StartNav183 #renderer-script #auto_subframe
0:13:27 Tab4 FinishNav183
0:13:28 Tab4 PageLoad
0:13:28 Widget Closed: NativeWidgetAura
0:13:30 Tab4 StartNav184 #renderer-user #auto_subframe
0:13:30 Tab4 FinishNav184
0:13:30 Tab4 PageLoad
0:15:19 Widget Closed: NativeWidgetAura
0:15:22 Widget Closed: TabHoverCardBubbleView
0:15:23 Widget Closed: NativeWidgetAura
0:15:32 Tab4 StartNav185 #renderer-script #auto_subframe
0:15:32 Tab4 FinishNav185
0:15:32 Tab4 PageLoad
0:15:43 Widget Closed: StatusBubble
