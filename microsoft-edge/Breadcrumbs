0:18:30 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl
0:18:30 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_TabClosable
0:18:30 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_NotClosingAll
0:18:30 Microsoft.Shutdown.TabStripModel.CloseWebContentses
0:18:30 Microsoft.Shutdown.TabStripModel.CloseWebContentses_FastShutdown
0:18:30 Microsoft.Shutdown.TabStripModel.ShouldRunUnloadListenerBeforeClosing_true
0:18:30 Microsoft.Shutdown.TabStripModel.RunUnloadListenerBeforeClosing_true
0:18:30 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_NotClosedAll
0:18:30 Microsoft.CloseTab_MiddleMouse
0:18:30 Tab5 BeforeUnload proceed:true
0:18:30 Microsoft.Shutdown.TabStripModel.CloseWebContentsAt
0:18:30 Microsoft.Shutdown.TabStripModel.CloseTabs
0:18:30 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl
0:18:30 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_TabClosable
0:18:30 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_NotClosingAll
0:18:30 Microsoft.Shutdown.TabStripModel.CloseWebContentses
0:18:30 Microsoft.Shutdown.TabStripModel.CloseWebContentses_FastShutdown
0:18:30 Microsoft.Shutdown.TabStripModel.ShouldRunUnloadListenerBeforeClosing_false
0:18:30 Microsoft.Shutdown.TabStripModel.RunUnloadListenerBeforeClosing_false
0:18:30 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosedAll
0:18:30 Microsoft.Shutdown.TabStripModel.SendDetachWebContentsNotifications
0:18:30 Browser1 Close Tab5 at 2
0:18:30 Tab5 WebContentsDestroyed
0:18:34 Tab2 StartNav223 #renderer-user #link
0:18:34 Tab2 FinishNav223
0:18:52 Tab2 StartNav224 #renderer-user #link
0:18:52 Tab2 FinishNav224
0:18:54 Tab2 StartNav225 #renderer-user #link
0:18:54 Tab2 FinishNav225
0:18:59 Widget Closed: TooltipAura
0:19:01 Widget Closed: TooltipAura
0:19:02 Tab2 StartNav226 #renderer-user #link
0:19:02 Tab2 FinishNav226
0:19:05 Tab2 StartNav227 #renderer-user #link
0:19:05 Tab2 FinishNav227
0:19:06 Tab2 StartNav228 #renderer-user #link
0:19:06 Tab2 FinishNav228
0:19:10 Tab2 StartNav229 #renderer-user #link
0:19:10 Tab2 FinishNav229
0:19:11 Tab2 StartNav230 #renderer-user #link
0:19:11 Tab2 FinishNav230
0:19:13 Tab2 StartNav231 #renderer-user #link
0:19:13 Tab2 FinishNav231
0:19:16 Tab2 StartNav232 #renderer-user #link
0:19:16 Tab2 FinishNav232
0:19:17 Tab2 StartNav233 #renderer-user #link
0:19:17 Tab2 FinishNav233
0:19:19 Tab2 StartNav234 #renderer-user #link
0:19:19 Tab2 FinishNav234
0:19:21 Tab2 StartNav235 #renderer-script #link
0:19:21 Tab2 FinishNav235
0:19:28 Tab2 StartNav236 #renderer-user #link
0:19:28 Tab2 FinishNav236
0:19:40 Widget Closed: StatusBubble
