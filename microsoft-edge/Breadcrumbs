0:00:10 Tab1 FinishNav8
0:00:11 Tab1 StartNav9 #renderer-script #link
0:00:11 Tab1 FinishNav9
0:00:11 Tab1 StartNav10 #renderer-script #auto_subframe
0:00:11 Microsoft.Autofill.Password.SignIn.HasAutofillSuggestions
0:00:12 Tab1 FinishNav10
0:00:12 Tab1 PageLoad
0:00:12 Tab1 StartNav11 #renderer-script #auto_subframe
0:00:12 Tab1 FinishNav11
0:00:12 Tab1 PageLoad #failure
0:00:12 Tab1 StartNav12 #renderer-script #auto_subframe
0:00:12 Tab1 FinishNav12
0:00:12 Tab1 PageLoad #failure
0:00:12 Microsoft.Autofill.Password.SignIn.HasAutofillSuggestions
0:00:12 Tab1 PageLoad
0:00:13 NewTab_Button
0:00:13 Tab2 StartNav13 #typed
0:00:13 Browser1 Insert active Tab2 at 1
0:00:13 Tab2 FinishNav13
0:00:13 Tab2 PageLoad
0:00:13 Tab2 StartNav14 #renderer-script #auto_subframe
0:00:14 OmniboxInputInProgress
0:00:14 LoadURL
0:00:14 Tab2 StartNav15 #typed
0:00:14 Tab2 FinishNav14
0:00:14 Widget Closed: RoundedOmniboxResultsFrameWindow
0:00:14 Tab2 PageLoad
0:00:14 Browser1 Switch from Tab2 to Tab1 at 0
0:00:14 SwitchTab_Click
0:00:14 Media.Hidden
0:00:15 Tab2 FinishNav15
0:00:15 Tab2 StartNav16 #renderer-script #link
0:00:15 Tab2 FinishNav16
0:00:16 Tab2 StartNav17 #renderer-script #link
0:00:16 Tab2 FinishNav17
0:00:16 Tab2 StartNav18 #renderer-script #auto_subframe
0:00:16 Tab2 FinishNav18
0:00:16 Tab2 PageLoad #failure
0:00:16 Tab2 PageLoad
0:00:16 Widget Closed: PopupBaseView
0:00:17 Tab2 StartNav19 #renderer-script #auto_subframe
0:00:18 Tab2 FinishNav19
0:00:18 Browser1 Switch from Tab1 to Tab2 at 1
0:00:18 SwitchTab_Click
0:00:18 Tab1 StartNav20 #renderer-user #link
0:00:18 Tab1 FinishNav20
0:00:19 Tab2 PageLoad
0:00:19 Tab2 StartNav21 #renderer-script #auto_subframe
0:00:19 Tab2 FinishNav21
0:00:19 Tab2 PageLoad #failure
0:00:19 Browser1 Switch from Tab2 to Tab1 at 0
0:00:19 SwitchTab_Click
0:00:20 Microsoft.Autofill.Password.SignIn.HasAutofillSuggestions
0:00:20 PasswordManager_Autofilled
0:00:20 Microsoft.Autofill.Password.SignIn.AutofilledWithoutAuth
0:00:20 PasswordManager_LoginPassed
0:00:20 Microsoft.Autofill.Password.SignIn.HasAutofillSuggestions
0:00:20 PasswordManager_Autofilled
0:00:20 Microsoft.Autofill.Password.SignIn.AutofilledWithoutAuth
0:00:20 Microsoft.Autofill.Password.SignIn.HasAutofillSuggestions
0:00:20 PasswordManager_Autofilled
0:00:20 Microsoft.Autofill.Password.SignIn.AutofilledWithoutAuth
0:00:20 Microsoft.Autofill.Password.SignIn.HasAutofillSuggestions
0:00:20 PasswordManager_Autofilled
0:00:20 Microsoft.Autofill.Password.SignIn.AutofilledWithoutAuth
0:00:20 Tab1 StartNav22 #renderer-script #auto_subframe
0:00:20 Tab1 FinishNav22
0:00:20 Tab1 PageLoad #failure
0:00:21 Tab2 StartNav23 #renderer-script #auto_subframe
0:00:21 Tab2 StartNav24 #renderer-script #auto_subframe
0:00:21 Tab2 FinishNav24
0:00:21 Tab2 PageLoad #failure
0:00:21 Tab2 FinishNav23
0:00:21 Tab2 PageLoad
0:00:21 Tab2 StartNav25 #renderer-script #auto_subframe
0:00:21 Tab2 FinishNav25
0:00:21 Tab2 PageLoad #failure
0:00:21 Tab2 StartNav26 #renderer-script #auto_subframe
0:00:21 Tab2 FinishNav26
0:00:21 Tab2 PageLoad #failure
0:00:21 Tab2 StartNav27 #renderer-script #auto_subframe
0:00:21 Tab2 FinishNav27
0:00:22 Tab2 PageLoad #failure
0:00:22 Tab2 StartNav28 #renderer-script #auto_subframe
0:00:22 Tab2 FinishNav28
0:00:22 Tab2 PageLoad #failure
0:00:22 Tab2 StartNav29 #renderer-script #auto_subframe
0:00:22 Tab2 FinishNav29
0:00:22 Tab2 PageLoad #failure
0:00:22 Tab2 StartNav30 #renderer-script #auto_subframe
0:00:22 Tab2 FinishNav30
0:00:23 Tab2 PageLoad #failure
0:00:23 Tab2 StartNav31 #renderer-script #auto_subframe
0:00:23 Tab2 FinishNav31
0:00:23 Tab2 StartNav32 #renderer-script #auto_subframe
0:00:23 Tab2 FinishNav32
0:00:23 Tab2 StartNav33 #renderer-script #auto_subframe
0:00:23 Tab2 FinishNav33
0:00:23 Tab2 StartNav34 #renderer-script #auto_subframe
0:00:23 Tab2 FinishNav34
0:00:23 Tab2 StartNav35 #renderer-script #auto_subframe
0:00:23 Tab2 FinishNav35
0:00:23 Tab2 StartNav36 #renderer-script #auto_subframe
0:00:23 Tab2 FinishNav36
0:00:23 Tab2 StartNav37 #renderer-script #auto_subframe
0:00:23 Tab2 FinishNav37
0:00:23 Tab2 StartNav38 #renderer-script #auto_subframe
0:00:23 Tab2 FinishNav38
0:00:23 Tab2 StartNav39 #renderer-script #auto_subframe
0:00:23 Tab2 FinishNav39
0:00:23 Tab2 StartNav40 #renderer-script #auto_subframe
0:00:23 Tab2 FinishNav40
0:00:23 Tab2 PageLoad #failure
0:00:23 Tab2 PageLoad #failure
0:00:23 Tab2 PageLoad #failure
0:00:23 Tab2 PageLoad #failure
0:00:23 Tab2 PageLoad #failure
0:00:23 Tab2 PageLoad #failure
0:00:23 Tab2 PageLoad #failure
0:00:23 Tab2 PageLoad #failure
0:00:23 Tab2 PageLoad #failure
0:00:23 Tab2 PageLoad #failure
0:00:23 Tab2 StartNav41 #renderer-script #auto_subframe
0:00:23 Tab2 FinishNav41
0:00:23 Tab2 PageLoad #failure
0:00:23 Tab2 StartNav42 #renderer-script #auto_subframe
0:00:23 Tab2 FinishNav42
0:00:23 Tab2 PageLoad #failure
0:00:23 Tab2 StartNav43 #renderer-script #auto_subframe
0:00:23 Tab2 FinishNav43
0:00:23 Tab2 PageLoad #failure
0:00:23 Tab2 StartNav44 #renderer-script #auto_subframe
0:00:23 Tab2 FinishNav44
0:00:23 Tab2 PageLoad #failure
0:00:23 Tab1 StartNav45 #renderer-user #link
0:00:23 Tab1 FinishNav45
0:00:23 Tab2 StartNav46 #renderer-script #auto_subframe
0:00:23 Tab2 FinishNav46
0:00:24 Tab2 PageLoad #failure
0:00:24 Tab2 StartNav47 #renderer-script #auto_subframe
0:00:24 Tab2 FinishNav47
0:00:24 Tab2 PageLoad #failure
0:00:24 Tab2 StartNav48 #renderer-script #auto_subframe
0:00:24 Tab2 FinishNav48
0:00:24 Tab2 PageLoad #failure
0:00:35 Tab1 StartNav49 #renderer-script #link
0:00:36 Tab1 FinishNav49
0:00:37 Tab1 StartNav50 #renderer-script #link
0:00:37 Tab1 FinishNav50
0:00:37 Tab1 PageLoad
0:00:38 Tab1 StartNav51 #renderer-script #auto_subframe
0:00:38 Tab1 FinishNav51
0:00:38 Tab1 PageLoad
0:00:38 Tab1 StartNav52 #renderer-script #auto_subframe
0:00:38 Tab1 FinishNav52
0:00:38 Tab1 PageLoad
