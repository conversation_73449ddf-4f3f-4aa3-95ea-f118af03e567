0:00:14 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_TabClosable
0:00:14 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosingAll
0:00:14 Microsoft.Shutdown.TabStripModel.CloseWebContentses
0:00:14 Microsoft.Shutdown.TabStripModel.RunUnloadListenerBeforeClosing_false
0:00:14 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosedAll
0:00:14 Microsoft.Shutdown.TabStripModel.SendDetachWebContentsNotifications
0:00:14 Microsoft.UnloadController.ClearUnloadState
0:00:14 Browser1 Close Tab1 at 0
0:00:14 Tab1 WebContentsDestroyed
0:00:14 Microsoft.UnloadController.TabStripEmpty
0:00:14 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:00:14 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:00:14 Microsoft.Shutdown.OnWindowClosing
0:00:14 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:00:14 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:00:14 Microsoft.Shutdown.OnWindowClosingPostClearBrowsingData_NoBrowsingDataCleared
0:00:14 Widget Closed: BrowserFrame
0:00:14 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:00:14 Microsoft.Shutdown.OnWindowClosing
0:00:14 Microsoft.Last_Browser_Removed
0:00:14 Microsoft.Shutdown.ShutdownIfNoBrowsers
0:00:14 Microsoft.Shutdown.SetTryingToQuit_Quitting
0:00:14 Microsoft.Shutdown.NotifyAppTerminating
0:00:14 Microsoft.Shutdown.OnAppExiting
0:00:14 Microsoft.Shutdown.HandleAppExitingForPlatform_Aura
0:00:14 Microsoft.Shutdown.HandleAppExitingForPlatform_Aura.NotificationUIManager_StartShutdown
0:00:14 Microsoft.Shutdown.HandleAppExitingForPlatform_Aura.CloseAllSecondaryWidgets
0:00:14 Microsoft.Shutdown.NotifyAppTerminating
0:00:14 Microsoft.Shutdown.OnAppExiting
0:01:15 Shutdown
