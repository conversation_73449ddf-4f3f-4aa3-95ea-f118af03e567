pragma Singleton
pragma ComponentBehavior: Bound

import QtQuick
import Quickshell
import Quickshell.Io
import Quickshell.Wayland
import Quickshell.Hyprland

/**
 * Provides access to some Hyprland data not available in Quickshell.Hyprland.
 */
Singleton {
    id: root
    property var windowList: []
    property var addresses: []
    property var windowByAddress: ({})
    property var monitors: []

    function updateWindowList() {
        getClients.running = true
        getMonitors.running = true
    }

    Component.onCompleted: {
        updateWindowList()
    }

    Connections {
        target: Hyprland

        function onRawEvent(event) {
            // Filter out redundant old v1 events for the same thing
            if(event.name in [
                "activewindow", "focusedmon", "monitoradded", 
                "createworkspace", "destroyworkspace", "moveworkspace", 
                "activespecial", "movewindow", "windowtitle"
            ]) return ;
            updateWindowList()
        }
    }

    Process {
        id: getClients
        command: ["bash", "-c", "hyprctl clients -j | jq -c"]
        stdout: SplitParser {
            onRead: (data) => {
                try {
                    if (!data || data.trim() === "") {
                        console.warn("[HyprlandData] Empty window data received")
                        return
                    }
                    root.windowList = JSON.parse(data)
                    let tempWinByAddress = {}
                    for (var i = 0; i < root.windowList.length; ++i) {
                        var win = root.windowList[i]
                        tempWinByAddress[win.address] = win
                    }
                    root.windowByAddress = tempWinByAddress
                    root.addresses = root.windowList.map((win) => win.address)
                } catch (error) {
                    console.error("[HyprlandData] Failed to parse window data:", error.message)
                    console.error("[HyprlandData] Data was:", data)
                }
            }
        }
    }
    Process {
        id: getMonitors
        command: ["bash", "-c", "hyprctl monitors -j | jq -c"]
        stdout: SplitParser {
            onRead: (data) => {
                try {
                    if (!data || data.trim() === "") {
                        console.warn("[HyprlandData] Empty monitor data received")
                        return
                    }
                    root.monitors = JSON.parse(data)
                } catch (error) {
                    console.error("[HyprlandData] Failed to parse monitor data:", error.message)
                    console.error("[HyprlandData] Data was:", data)
                }
            }
        }
    }
}

