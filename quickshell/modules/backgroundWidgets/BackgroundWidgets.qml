import "root:/"
import "root:/modules/common"
import "root:/modules/common/widgets"
import "root:/services"
import "root:/modules/common/functions/color_utils.js" as ColorUtils
import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import Quickshell
import Quickshell.Io
import Quickshell.Wayland
import Quickshell.Hyprland
import Quickshell.Services.UPower

Scope {
    id: root
    property string filePath: `${Directories.state}/user/generated/wallpaper/least_busy_region.json`
    property real defaultX: (ConfigOptions?.background.clockX ?? -500)
    property real defaultY: (ConfigOptions?.background.clockY ?? -500)
    property real centerX: defaultX
    property real centerY: defaultY
    property real effectiveCenterX: ConfigOptions?.background.fixedClockPosition ? defaultX : centerX
    property real effectiveCenterY: ConfigOptions?.background.fixedClockPosition ? defaultY : centerY
    property color dominantColor: Appearance.colors.colPrimary
    property bool dominantColorIsDark: dominantColor.hslLightness < 0.5
    property color colBackground: ColorUtils.transparentize(ColorUtils.mix(Appearance.colors.colPrimary, Appearance.colors.colSecondaryContainer), 1)
    property color colText: ColorUtils.colorWithLightness(Appearance.colors.colPrimary, (root.dominantColorIsDark ? 0.8 : 0.12))

    function updateWidgetPosition(fileContent) {
        // console.log("[BackgroundWidgets] Updating widget position with content:", fileContent)
        try {
            if (!fileContent || fileContent.trim() === "") {
                console.warn("[BackgroundWidgets] File content is empty, using default values")
                return
            }

            const parsedContent = JSON.parse(fileContent)

            if (parsedContent.center_x !== undefined) {
                root.centerX = parsedContent.center_x
            }
            if (parsedContent.center_y !== undefined) {
                root.centerY = parsedContent.center_y
            }
            if (parsedContent.dominant_color) {
                root.dominantColor = parsedContent.dominant_color
            }
        } catch (error) {
            console.error("[BackgroundWidgets] Failed to parse JSON:", error.message)
            console.error("[BackgroundWidgets] File content:", fileContent)
            // 使用默认值，不更新位置
        }
    }
    
    Timer {
        id: delayedFileRead
        interval: ConfigOptions.hacks.arbitraryRaceConditionDelay
        running: false
        onTriggered: {
            root.updateWidgetPosition(leastBusyRegionFileView.text())
        }
    }

    FileView { 
        id: leastBusyRegionFileView
        path: Qt.resolvedUrl(root.filePath)
        watchChanges: !ConfigOptions?.background.fixedClockPosition
        onFileChanged: {
            this.reload()
            delayedFileRead.start()
        }
        onLoadedChanged: {
            if (loaded) {
                const fileContent = leastBusyRegionFileView.text()
                root.updateWidgetPosition(fileContent)
            }
        }
    }

    Variants { // For each monitor
        model: Quickshell.screens

        LazyLoader {
            required property var modelData
            readonly property HyprlandMonitor monitor: Hyprland.monitorFor(modelData)
            activeAsync: !ToplevelManager.activeToplevel?.activated
            component: PanelWindow { // Window
                id: windowRoot
                screen: modelData
                property var textHorizontalAlignment: root.effectiveCenterX / monitor.scale < windowRoot.width / 3 ? Text.AlignLeft :
                    (root.effectiveCenterX / monitor.scale > windowRoot.width * 2 / 3 ? Text.AlignRight : Text.AlignHCenter)

                WlrLayershell.layer: WlrLayer.Bottom
                WlrLayershell.namespace: "quickshell:backgroundWidgets"
                
                anchors {
                    top: true
                    bottom:true
                    left: true
                    right: true
                }
                color: "transparent"
                HyprlandWindow.visibleMask: Region {
                    item: widgetBackground
                }

                Rectangle {
                    id: widgetBackground
                    property real verticalPadding: 20
                    property real horizontalPadding: 30
                    radius: 40
                    color: root.colBackground
                    implicitHeight: columnLayout.implicitHeight + verticalPadding * 2
                    implicitWidth: columnLayout.implicitWidth + horizontalPadding * 2
                    anchors {
                        left: parent.left
                        top: parent.top
                        leftMargin: (root.effectiveCenterX / monitor.scale - implicitWidth / 2)
                        topMargin: (root.effectiveCenterY / monitor.scale - implicitHeight / 2)
                        Behavior on leftMargin {
                            animation: Appearance.animation.elementMove.numberAnimation.createObject(this)
                        }
                        Behavior on topMargin {
                            animation: Appearance.animation.elementMove.numberAnimation.createObject(this)
                        }
                    }

                    ColumnLayout {
                        id: columnLayout
                        anchors.centerIn: parent
                        spacing: -5

                        StyledText {
                            Layout.fillWidth: true
                            horizontalAlignment: windowRoot.textHorizontalAlignment
                            font.pixelSize: 95
                            color: root.colText
                            style: Text.Raised
                            styleColor: Appearance.colors.colShadow
                            text: DateTime.time
                        }
                        StyledText {
                            Layout.fillWidth: true
                            horizontalAlignment: windowRoot.textHorizontalAlignment
                            font.pixelSize: 25
                            color: root.colText
                            style: Text.Raised
                            styleColor: Appearance.colors.colShadow
                            text: DateTime.date
                        }
                    }
                }

            }
        }

    }

}
