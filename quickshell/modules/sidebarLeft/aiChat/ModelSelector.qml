import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import QtGraphicalEffects
import "root:/modules/common/"
import "root:/modules/common/widgets/"
import "root:/services/"

/**
 * Quick model selector popup
 */
Popup {
    id: root
    
    modal: true
    focus: true
    closePolicy: Popup.CloseOnEscape | Popup.CloseOnPressOutside
    
    background: Rectangle {
        color: Appearance.colors.colLayer1
        radius: Appearance.rounding.normal
        border.color: Appearance.colors.colOutlineVariant
        border.width: 1
        
        layer.enabled: true
        layer.effect: DropShadow {
            horizontalOffset: 0
            verticalOffset: 4
            radius: 8
            samples: 16
            color: Qt.rgba(0, 0, 0, 0.2)
        }
    }
    
    contentItem: ColumnLayout {
        spacing: 8
        implicitWidth: 300
        implicitHeight: Math.min(400, modelListView.contentHeight + headerLayout.implicitHeight + 20)
        
        RowLayout {
            id: headerLayout
            Layout.fillWidth: true
            Layout.margins: 10
            
            MaterialSymbol {
                text: "smart_toy"
                iconSize: Appearance.font.pixelSize.normal
                color: Appearance.colors.colPrimary
            }
            
            StyledText {
                Layout.fillWidth: true
                text: qsTr("选择模型")
                font.pixelSize: Appearance.font.pixelSize.normal
                font.weight: Font.Medium
                color: Appearance.colors.colOnLayer1
            }
            
            AiMessageControlButton {
                buttonIcon: "close"
                onClicked: root.close()
            }
        }
        
        Rectangle {
            Layout.fillWidth: true
            height: 1
            color: Appearance.colors.colOutlineVariant
        }
        
        ScrollView {
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.margins: 5
            clip: true
            
            ListView {
                id: modelListView
                spacing: 2
                
                model: Ai.modelList
                delegate: ItemDelegate {
                    required property string modelData
                    width: modelListView.width
                    implicitHeight: modelItemLayout.implicitHeight + 16
                    
                    background: Rectangle {
                        color: parent.hovered ? Appearance.colors.colLayer2Hover : 
                               modelData === Ai.currentModelId ? Appearance.colors.colLayer2 : "transparent"
                        radius: Appearance.rounding.small
                        border.color: modelData === Ai.currentModelId ? Appearance.colors.colPrimary : "transparent"
                        border.width: 1
                        
                        Behavior on color {
                            animation: Appearance.animation.elementMoveFast.colorAnimation.createObject(this)
                        }
                    }
                    
                    onClicked: {
                        if (modelData !== Ai.currentModelId) {
                            Ai.setModel(modelData, true);
                        }
                        root.close();
                    }
                    
                    RowLayout {
                        id: modelItemLayout
                        anchors.fill: parent
                        anchors.margins: 8
                        spacing: 10
                        
                        MaterialSymbol {
                            text: Ai.models[modelData]?.icon || "smart_toy"
                            iconSize: Appearance.font.pixelSize.normal
                            color: modelData === Ai.currentModelId ? Appearance.colors.colPrimary : Appearance.colors.colOnLayer1
                        }
                        
                        ColumnLayout {
                            Layout.fillWidth: true
                            spacing: 2
                            
                            StyledText {
                                text: Ai.models[modelData]?.name || modelData
                                font.pixelSize: Appearance.font.pixelSize.small
                                font.weight: modelData === Ai.currentModelId ? Font.Medium : Font.Normal
                                color: Appearance.colors.colOnLayer1
                                elide: Text.ElideRight
                            }
                            
                            StyledText {
                                text: {
                                    const model = Ai.models[modelData];
                                    if (!model) return "";
                                    
                                    let info = [];
                                    if (model.custom) info.push(qsTr("自定义"));
                                    if (model.requires_key) {
                                        const hasKey = Ai.apiKeysLoaded && Ai.apiKeys[model.key_id];
                                        info.push(hasKey ? qsTr("已配置密钥") : qsTr("需要密钥"));
                                    }
                                    if (model.api_format) info.push(model.api_format.toUpperCase());
                                    
                                    return info.join(" • ");
                                }
                                font.pixelSize: Appearance.font.pixelSize.smaller
                                color: Appearance.colors.colSubtext
                                visible: text.length > 0
                                elide: Text.ElideRight
                            }
                        }
                        
                        MaterialSymbol {
                            text: "check"
                            iconSize: Appearance.font.pixelSize.small
                            color: Appearance.colors.colPrimary
                            visible: modelData === Ai.currentModelId
                        }
                    }
                }
            }
        }
        
        Rectangle {
            Layout.fillWidth: true
            height: 1
            color: Appearance.colors.colOutlineVariant
        }
        
        RowLayout {
            Layout.fillWidth: true
            Layout.margins: 10
            
            DialogButton {
                Layout.fillWidth: true
                buttonText: qsTr("管理模型")
                onClicked: {
                    Qt.openUrlExternally("quickshell://settings?page=ai");
                    root.close();
                }
            }
        }
    }
}
