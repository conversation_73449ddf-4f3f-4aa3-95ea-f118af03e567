import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import "root:/modules/common/"

/**
 * Simple confirmation dialog
 */
Dialog {
    id: root

    property string dialogTitle: qsTr("确认")
    property string message: qsTr("您确定要执行此操作吗？")
    property string confirmText: qsTr("确认")
    property string cancelText: qsTr("取消")
    
    signal confirmed()
    signal cancelled()
    
    modal: true
    anchors.centerIn: parent
    
    background: Rectangle {
        color: Appearance.colors.colLayer1
        radius: Appearance.rounding.normal
        border.color: Appearance.colors.colOutlineVariant
        border.width: 1
    }
    
    contentItem: ColumnLayout {
        spacing: 20
        
        StyledText {
            Layout.fillWidth: true
            text: root.dialogTitle
            font.pixelSize: Appearance.font.pixelSize.normal
            font.weight: Font.Medium
            color: Appearance.colors.colOnLayer1
            horizontalAlignment: Text.AlignHCenter
        }
        
        StyledText {
            Layout.fillWidth: true
            text: root.message
            font.pixelSize: Appearance.font.pixelSize.small
            color: Appearance.colors.colOnLayer1
            wrapMode: Text.Wrap
            horizontalAlignment: Text.AlignHCenter
        }
        
        RowLayout {
            Layout.fillWidth: true
            spacing: 10
            
            DialogButton {
                Layout.fillWidth: true
                buttonText: root.cancelText
                onClicked: {
                    root.cancelled();
                    root.close();
                }
            }
            
            DialogButton {
                Layout.fillWidth: true
                buttonText: root.confirmText
                onClicked: {
                    root.confirmed();
                    root.close();
                }
            }
        }
    }
}
