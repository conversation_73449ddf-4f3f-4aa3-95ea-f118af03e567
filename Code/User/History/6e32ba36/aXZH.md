# AI配置系统 - 图形化界面实现

## 概述

为QuickShell桌面环境创建了一个完整的AI配置图形化界面，允许用户通过直观的界面管理AI模型、API密钥和各种设置。

## 新增文件

### 1. `quickshell/modules/settings/AiConfig.qml`
主要的AI配置页面，包含以下功能模块：

#### 模型配置
- **当前模型选择**: 下拉选择器显示所有可用模型
- **模型信息显示**: 显示当前选择模型的详细信息（图标、名称、描述、端点、密钥要求）

#### API密钥管理
- **密钥输入**: 安全的密码输入框，支持显示/隐藏切换
- **密钥操作**: 保存、清除密钥功能
- **获取密钥**: 直接链接到API提供商的密钥获取页面
- **帮助信息**: 显示获取密钥的说明文档

#### 模型参数
- **温度设置**: 滑块控制模型输出随机性（0-2.0）
- **系统提示**: 多行文本输入框设置AI行为

#### 自定义模型
- **添加模型**: 表单输入自定义模型的所有参数
  - 模型名称和显示名称
  - API端点URL和模型ID
  - API格式选择（OpenAI/Gemini）
  - 密钥要求设置
  - 模型描述
- **模型管理**: 列表显示所有模型，支持选择和删除自定义模型

#### 高级设置
- **请求配置**: 超时时间、重试次数、流式响应等
- **安全设置**: 本地模型限制、SSL验证等

#### 聊天管理
- **基本操作**: 清除聊天记录、显示当前设置
- **导入/导出**: 配置备份和恢复功能
- **重置**: 恢复默认设置

### 2. `quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml`
快速模型选择弹窗组件：

- **模型列表**: 显示所有可用模型，包含图标、名称、状态信息
- **当前模型标识**: 高亮显示当前选择的模型
- **快速切换**: 点击即可切换模型
- **状态显示**: 显示模型类型（自定义/官方）和密钥配置状态
- **管理入口**: 直接跳转到完整的AI设置页面

### 3. `quickshell/modules/common/widgets/ConfirmDialog.qml`
通用确认对话框组件：

- **可定制内容**: 标题、消息、按钮文本
- **回调支持**: 确认和取消事件处理
- **Material Design**: 符合系统设计风格

## 修改的文件

### 1. `quickshell/settings.qml`
- 在设置页面列表中添加了AI配置页面
- 使用条件渲染，仅在AI功能启用时显示

### 2. `quickshell/services/Ai.qml`
增强了AI服务的功能：

#### 新增函数
- `addCustomModel(modelConfig)`: 添加自定义模型
- `removeCustomModel(modelId)`: 删除自定义模型
- 自动加载保存的自定义模型配置

#### 配置持久化
- 自定义模型保存到 `ConfigOptions.ai.customModels`
- 与现有配置系统集成

### 3. `quickshell/modules/sidebarLeft/AiChat.qml`
增强了AI聊天界面：

#### 新增功能
- **设置按钮**: 快速访问AI配置页面
- **模型选择器**: 可点击的模型信息区域，打开快速选择弹窗
- **状态显示**: 显示当前模型图标、名称和配置状态

#### 界面改进
- 更直观的模型信息显示
- 集成的设置访问入口

## 功能特性

### 用户体验
1. **直观操作**: 图形化界面替代命令行配置
2. **实时反馈**: 配置更改立即生效并显示
3. **状态可视化**: 清晰显示模型状态和配置要求
4. **快速访问**: 从聊天界面直接访问设置

### 安全性
1. **密钥保护**: 密码输入框隐藏敏感信息
2. **确认操作**: 删除和重置操作需要确认
3. **配置验证**: 输入验证确保配置正确性

### 扩展性
1. **模块化设计**: 组件可复用和扩展
2. **配置系统**: 与现有配置框架完全集成
3. **多语言支持**: 使用qsTr()函数支持国际化

### 兼容性
1. **向后兼容**: 不影响现有AI功能
2. **渐进增强**: 在现有命令行功能基础上添加GUI
3. **配置迁移**: 自动加载现有配置

## 使用方法

### 访问AI配置
1. 打开设置窗口
2. 点击左侧导航的"AI"选项
3. 或从AI聊天界面点击设置按钮

### 快速切换模型
1. 在AI聊天界面点击模型信息区域
2. 从弹出的列表中选择新模型
3. 模型立即切换并更新界面

### 添加自定义模型
1. 进入AI配置页面
2. 滚动到"自定义模型"部分
3. 填写模型信息表单
4. 点击"添加模型"按钮

### 管理API密钥
1. 选择需要密钥的模型
2. 在"API密钥管理"部分输入密钥
3. 点击"保存密钥"确认

## 技术实现

### 架构设计
- **MVC模式**: 视图(QML) + 模型(Ai.qml) + 控制器(配置系统)
- **组件化**: 可复用的UI组件
- **事件驱动**: 响应式更新机制

### 数据流
1. 用户操作 → UI组件
2. UI组件 → Ai服务函数
3. Ai服务 → 配置系统
4. 配置系统 → 持久化存储
5. 状态更新 → UI刷新

### 配置存储
```javascript
ConfigOptions.ai: {
    systemPrompt: "...",
    customModels: {
        "model-id": {
            name: "...",
            endpoint: "...",
            // ... 其他配置
        }
    },
    requestTimeout: 60,
    maxRetries: 3,
    // ... 其他设置
}
```

这个实现提供了完整的AI配置图形化界面，满足了用户"允许用户以图形化的形式进行配置"的需求，同时保持了系统的一致性和可扩展性。

## 问题修复

### 1. QtGraphicalEffects 模块问题 ✅
**问题**: `module "QtGraphicalEffects" is not installed`
**解决方案**: 移除了对 QtGraphicalEffects 的依赖，使用简单的 Rectangle 背景替代阴影效果

### 2. 内部导航问题 ✅
**问题**: `Qt.openUrlExternally("quickshell://settings?page=ai")` 会跳转到外部浏览器
**解决方案**:
- 使用 `Quickshell.execDetached(["qs", "-p", settingsPath])` 启动设置窗口
- 在设置窗口的 `Component.onCompleted` 中添加自动导航到AI页面的逻辑
- 确保从AI聊天界面访问设置时直接显示AI配置页面

### 3. 模块导入问题 ✅
**问题**: `Type ModelSelector unavailable`
**解决方案**:
- 确保正确的导入路径 `import "aiChat/"`
- 添加必要的依赖导入（FileUtils, Quickshell等）
- 验证所有组件的导入链完整性

## 测试验证

系统已通过以下测试：
- ✅ 基本功能测试
- ✅ 模型配置和切换
- ✅ API密钥管理
- ✅ 自定义模型添加/删除
- ✅ 配置持久化
- ✅ 界面集成和导航

详细测试指南请参考 `test_ai_config.md` 文件。
