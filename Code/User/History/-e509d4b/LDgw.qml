import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import "root:/services/"
import "root:/modules/common/"
import "root:/modules/common/widgets/"

ContentPage {
    id: root
    forceWidth: true

    ContentSection {
        title: qsTr("模型配置")

        ContentSubsection {
            title: qsTr("当前模型")
            tooltip: qsTr("选择要使用的AI模型")

            ConfigSelectionArray {
                currentValue: Ai.currentModelId
                configOptionName: "ai.model"
                onSelected: (newValue) => {
                    Ai.setModel(newValue, true);
                }
                options: Ai.modelList.map(modelId => {
                    const model = Ai.models[modelId];
                    return {
                        displayName: model.name || modelId,
                        value: modelId
                    };
                })
            }
        }

        ContentSubsection {
            title: qsTr("模型信息")
            visible: Ai.currentModelId && Ai.models[Ai.currentModelId]

            Rectangle {
                Layout.fillWidth: true
                implicitHeight: modelInfoLayout.implicitHeight + 20
                color: Appearance.colors.colLayer2
                radius: Appearance.rounding.small
                border.color: Appearance.colors.colOutlineVariant
                border.width: 1

                ColumnLayout {
                    id: modelInfoLayout
                    anchors.fill: parent
                    anchors.margins: 10
                    spacing: 8

                    RowLayout {
                        Layout.fillWidth: true
                        MaterialSymbol {
                            text: Ai.models[Ai.currentModelId]?.icon || "smart_toy"
                            iconSize: Appearance.font.pixelSize.larger
                            color: Appearance.colors.colPrimary
                        }
                        StyledText {
                            Layout.fillWidth: true
                            text: Ai.models[Ai.currentModelId]?.name || ""
                            font.pixelSize: Appearance.font.pixelSize.normal
                            font.weight: Font.Medium
                            color: Appearance.colors.colOnLayer2
                        }
                    }

                    StyledText {
                        Layout.fillWidth: true
                        text: Ai.models[Ai.currentModelId]?.description || ""
                        font.pixelSize: Appearance.font.pixelSize.small
                        color: Appearance.colors.colSubtext
                        wrapMode: Text.Wrap
                    }

                    RowLayout {
                        Layout.fillWidth: true
                        visible: Ai.models[Ai.currentModelId]?.endpoint

                        StyledText {
                            text: qsTr("端点:")
                            font.pixelSize: Appearance.font.pixelSize.smaller
                            color: Appearance.colors.colSubtext
                        }
                        StyledText {
                            Layout.fillWidth: true
                            text: Ai.models[Ai.currentModelId]?.endpoint || ""
                            font.pixelSize: Appearance.font.pixelSize.smaller
                            color: Appearance.colors.colOnLayer2
                            elide: Text.ElideMiddle
                        }
                    }

                    RowLayout {
                        Layout.fillWidth: true
                        visible: Ai.models[Ai.currentModelId]?.requires_key

                        MaterialSymbol {
                            text: "key"
                            iconSize: Appearance.font.pixelSize.small
                            color: Appearance.colors.colSubtext
                        }
                        StyledText {
                            text: qsTr("需要API密钥")
                            font.pixelSize: Appearance.font.pixelSize.smaller
                            color: Appearance.colors.colSubtext
                        }
                    }
                }
            }
        }
    }

    ContentSection {
        title: qsTr("API密钥管理")
        visible: Ai.models[Ai.currentModelId]?.requires_key

        ContentSubsection {
            title: qsTr("当前模型密钥")
            tooltip: qsTr("为当前选择的模型设置API密钥")

            ColumnLayout {
                Layout.fillWidth: true
                spacing: 8

                RowLayout {
                    Layout.fillWidth: true
                    MaterialTextField {
                        id: apiKeyField
                        Layout.fillWidth: true
                        placeholderText: qsTr("输入API密钥...")
                        echoMode: showApiKey.checked ? TextInput.Normal : TextInput.Password
                        text: ""
                        
                        Component.onCompleted: {
                            if (Ai.apiKeysLoaded && Ai.models[Ai.currentModelId]?.key_id) {
                                text = Ai.apiKeys[Ai.models[Ai.currentModelId].key_id] || "";
                            }
                        }

                        Connections {
                            target: Ai
                            function onCurrentModelIdChanged() {
                                if (Ai.apiKeysLoaded && Ai.models[Ai.currentModelId]?.key_id) {
                                    apiKeyField.text = Ai.apiKeys[Ai.models[Ai.currentModelId].key_id] || "";
                                }
                            }
                        }
                    }

                    ConfigSwitch {
                        id: showApiKey
                        text: qsTr("显示")
                        checked: false
                        Layout.fillWidth: false
                    }
                }

                RowLayout {
                    Layout.fillWidth: true
                    spacing: 8

                    DialogButton {
                        buttonText: qsTr("保存密钥")
                        enabled: apiKeyField.text.length > 0
                        onClicked: {
                            Ai.setApiKey(apiKeyField.text);
                        }
                    }

                    DialogButton {
                        buttonText: qsTr("清除密钥")
                        enabled: apiKeyField.text.length > 0
                        onClicked: {
                            Ai.setApiKey("");
                            apiKeyField.text = "";
                        }
                    }

                    DialogButton {
                        buttonText: qsTr("获取密钥")
                        visible: Ai.models[Ai.currentModelId]?.key_get_link
                        onClicked: {
                            Qt.openUrlExternally(Ai.models[Ai.currentModelId].key_get_link);
                        }
                    }
                }

                Rectangle {
                    Layout.fillWidth: true
                    visible: Ai.models[Ai.currentModelId]?.key_get_description
                    implicitHeight: keyHelpText.implicitHeight + 16
                    color: Appearance.colors.colLayer2
                    radius: Appearance.rounding.small
                    border.color: Appearance.colors.colOutlineVariant
                    border.width: 1

                    StyledText {
                        id: keyHelpText
                        anchors.fill: parent
                        anchors.margins: 8
                        text: Ai.models[Ai.currentModelId]?.key_get_description || ""
                        font.pixelSize: Appearance.font.pixelSize.smaller
                        color: Appearance.colors.colOnLayer2
                        wrapMode: Text.Wrap
                        textFormat: Text.MarkdownText
                    }
                }
            }
        }
    }

    ContentSection {
        title: qsTr("模型参数")

        ContentSubsection {
            title: qsTr("温度设置")
            tooltip: qsTr("控制模型输出的随机性，值越高越随机")

            ConfigRow {
                ConfigSpinBox {
                    text: qsTr("温度")
                    value: Math.round(Ai.temperature * 100) / 100
                    from: 0
                    to: 200
                    stepSize: 5
                    onValueChanged: {
                        Ai.setTemperature(value / 100);
                    }
                }

                StyledText {
                    Layout.fillWidth: true
                    text: qsTr("当前值: %1").arg(Ai.temperature.toFixed(2))
                    font.pixelSize: Appearance.font.pixelSize.smaller
                    color: Appearance.colors.colSubtext
                    horizontalAlignment: Text.AlignRight
                }
            }
        }

        ContentSubsection {
            title: qsTr("系统提示")
            tooltip: qsTr("设置AI助手的行为和角色")

            MaterialTextField {
                Layout.fillWidth: true
                placeholderText: qsTr("输入系统提示...")
                text: ConfigOptions.ai?.systemPrompt || ""
                wrapMode: TextEdit.Wrap
                onTextChanged: {
                    ConfigLoader.setConfigValueAndSave("ai.systemPrompt", text);
                }
            }
        }
    }

    ContentSection {
        title: qsTr("自定义模型")

        ContentSubsection {
            title: qsTr("添加自定义模型")
            tooltip: qsTr("添加自己的AI模型配置")

            ColumnLayout {
                Layout.fillWidth: true
                spacing: 8

                ConfigRow {
                    MaterialTextField {
                        id: customModelName
                        Layout.fillWidth: true
                        placeholderText: qsTr("模型名称")
                    }
                    MaterialTextField {
                        id: customModelDisplayName
                        Layout.fillWidth: true
                        placeholderText: qsTr("显示名称")
                    }
                }

                ConfigRow {
                    MaterialTextField {
                        id: customModelEndpoint
                        Layout.fillWidth: true
                        placeholderText: qsTr("API端点URL")
                    }
                    MaterialTextField {
                        id: customModelId
                        Layout.fillWidth: true
                        placeholderText: qsTr("模型ID")
                    }
                }

                ConfigRow {
                    ConfigSelectionArray {
                        id: customApiFormat
                        Layout.fillWidth: true
                        currentValue: "openai"
                        options: [
                            { displayName: "OpenAI", value: "openai" },
                            { displayName: "Gemini", value: "gemini" }
                        ]
                    }
                    ConfigSwitch {
                        id: customRequiresKey
                        text: qsTr("需要API密钥")
                        checked: true
                    }
                }

                MaterialTextField {
                    id: customModelDescription
                    Layout.fillWidth: true
                    placeholderText: qsTr("模型描述")
                    wrapMode: TextEdit.Wrap
                }

                DialogButton {
                    buttonText: qsTr("添加模型")
                    enabled: customModelName.text.length > 0 && customModelEndpoint.text.length > 0
                    onClicked: {
                        // 这里需要在Ai.qml中添加addCustomModel函数
                        console.log("添加自定义模型:", customModelName.text);
                        // 清空输入字段
                        customModelName.text = "";
                        customModelDisplayName.text = "";
                        customModelEndpoint.text = "";
                        customModelId.text = "";
                        customModelDescription.text = "";
                    }
                }
            }
        }
    }

    ContentSection {
        title: qsTr("聊天管理")

        ConfigRow {
            uniform: true

            DialogButton {
                buttonText: qsTr("清除聊天记录")
                onClicked: {
                    Ai.clearMessages();
                }
            }

            DialogButton {
                buttonText: qsTr("显示当前温度")
                onClicked: {
                    Ai.printTemperature();
                }
            }

            DialogButton {
                buttonText: qsTr("显示API密钥")
                visible: Ai.models[Ai.currentModelId]?.requires_key
                onClicked: {
                    Ai.printApiKey();
                }
            }
        }
    }
}
