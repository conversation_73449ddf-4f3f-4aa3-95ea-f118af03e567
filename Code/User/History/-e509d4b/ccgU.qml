import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import "root:/services/"
import "root:/modules/common/"
import "root:/modules/common/widgets/"

ContentPage {
    id: root
    forceWidth: true

    ConfirmDialog {
        id: deleteConfirmDialog
        property string modelToDelete: ""
        dialogTitle: qsTr("删除模型")
        message: qsTr("确定要删除模型 '%1' 吗？此操作无法撤销。").arg(modelToDelete)
        confirmText: qsTr("删除")
        cancelText: qsTr("取消")

        onConfirmed: {
            if (modelToDelete === "RESET_ALL") {
                // 重置所有配置
                const customModels = Object.keys(Ai.models).filter(id => Ai.models[id].custom);
                customModels.forEach(id => Ai.removeCustomModel(id));

                // 重置设置
                ConfigLoader.setConfigValueAndSave("ai.systemPrompt", "");
                ConfigLoader.setConfigValueAndSave("ai.customModels", {});
                Ai.setTemperature(0.5);

                modelToDelete = "";
            } else if (modelToDelete.length > 0) {
                Ai.removeCustomModel(modelToDelete);
                modelToDelete = "";
            }
        }

        onCancelled: {
            modelToDelete = "";
        }
    }

    ContentSection {
        title: qsTr("模型配置")

        ContentSubsection {
            title: qsTr("当前模型")
            tooltip: qsTr("选择要使用的AI模型")

            ConfigSelectionArray {
                currentValue: Ai.currentModelId
                configOptionName: "ai.model"
                onSelected: (newValue) => {
                    Ai.setModel(newValue, true);
                }
                options: Ai.modelList.map(modelId => {
                    const model = Ai.models[modelId];
                    return {
                        displayName: model.name || modelId,
                        value: modelId
                    };
                })
            }
        }

        ContentSubsection {
            title: qsTr("模型信息")
            visible: Ai.currentModelId && Ai.models[Ai.currentModelId]

            Rectangle {
                Layout.fillWidth: true
                implicitHeight: modelInfoLayout.implicitHeight + 20
                color: Appearance.colors.colLayer2
                radius: Appearance.rounding.small
                border.color: Appearance.colors.colOutlineVariant
                border.width: 1

                ColumnLayout {
                    id: modelInfoLayout
                    anchors.fill: parent
                    anchors.margins: 10
                    spacing: 8

                    RowLayout {
                        Layout.fillWidth: true
                        MaterialSymbol {
                            text: Ai.models[Ai.currentModelId]?.icon || "smart_toy"
                            iconSize: Appearance.font.pixelSize.larger
                            color: Appearance.colors.colPrimary
                        }
                        StyledText {
                            Layout.fillWidth: true
                            text: Ai.models[Ai.currentModelId]?.name || ""
                            font.pixelSize: Appearance.font.pixelSize.normal
                            font.weight: Font.Medium
                            color: Appearance.colors.colOnLayer2
                        }
                    }

                    StyledText {
                        Layout.fillWidth: true
                        text: Ai.models[Ai.currentModelId]?.description || ""
                        font.pixelSize: Appearance.font.pixelSize.small
                        color: Appearance.colors.colSubtext
                        wrapMode: Text.Wrap
                    }

                    RowLayout {
                        Layout.fillWidth: true
                        visible: Ai.models[Ai.currentModelId]?.endpoint

                        StyledText {
                            text: qsTr("端点:")
                            font.pixelSize: Appearance.font.pixelSize.smaller
                            color: Appearance.colors.colSubtext
                        }
                        StyledText {
                            Layout.fillWidth: true
                            text: Ai.models[Ai.currentModelId]?.endpoint || ""
                            font.pixelSize: Appearance.font.pixelSize.smaller
                            color: Appearance.colors.colOnLayer2
                            elide: Text.ElideMiddle
                        }
                    }

                    RowLayout {
                        Layout.fillWidth: true
                        visible: Ai.models[Ai.currentModelId]?.requires_key

                        MaterialSymbol {
                            text: "key"
                            iconSize: Appearance.font.pixelSize.small
                            color: Appearance.colors.colSubtext
                        }
                        StyledText {
                            text: qsTr("需要API密钥")
                            font.pixelSize: Appearance.font.pixelSize.smaller
                            color: Appearance.colors.colSubtext
                        }
                    }
                }
            }
        }
    }

    ContentSection {
        title: qsTr("API密钥管理")
        visible: Ai.models[Ai.currentModelId]?.requires_key

        ContentSubsection {
            title: qsTr("当前模型密钥")
            tooltip: qsTr("为当前选择的模型设置API密钥")

            ColumnLayout {
                Layout.fillWidth: true
                spacing: 8

                RowLayout {
                    Layout.fillWidth: true

                    Rectangle {
                        Layout.fillWidth: true
                        implicitHeight: 56
                        color: Appearance.m3colors.m3surface
                        radius: 4
                        border.color: apiKeyInput.activeFocus ? Appearance.m3colors.m3primary :
                            apiKeyInput.hovered ? Appearance.m3colors.m3outline : Appearance.m3colors.m3outlineVariant
                        border.width: 1

                        StyledTextInput {
                            id: apiKeyInput
                            anchors.fill: parent
                            anchors.margins: 12
                            echoMode: showApiKey.checked ? TextInput.Normal : TextInput.Password
                            placeholderText: qsTr("输入API密钥...")
                            text: ""

                            Component.onCompleted: {
                                if (Ai.apiKeysLoaded && Ai.models[Ai.currentModelId]?.key_id) {
                                    text = Ai.apiKeys[Ai.models[Ai.currentModelId].key_id] || "";
                                }
                            }

                            Connections {
                                target: Ai
                                function onCurrentModelIdChanged() {
                                    if (Ai.apiKeysLoaded && Ai.models[Ai.currentModelId]?.key_id) {
                                        apiKeyInput.text = Ai.apiKeys[Ai.models[Ai.currentModelId].key_id] || "";
                                    }
                                }
                            }
                        }
                    }

                    ConfigSwitch {
                        id: showApiKey
                        text: qsTr("显示")
                        checked: false
                        Layout.fillWidth: false
                    }
                }

                RowLayout {
                    Layout.fillWidth: true
                    spacing: 8

                    DialogButton {
                        buttonText: qsTr("保存密钥")
                        enabled: apiKeyInput.text.length > 0
                        onClicked: {
                            Ai.setApiKey(apiKeyInput.text);
                        }
                    }

                    DialogButton {
                        buttonText: qsTr("清除密钥")
                        enabled: apiKeyInput.text.length > 0
                        onClicked: {
                            Ai.setApiKey("");
                            apiKeyInput.text = "";
                        }
                    }

                    DialogButton {
                        buttonText: qsTr("获取密钥")
                        visible: Ai.models[Ai.currentModelId]?.key_get_link
                        onClicked: {
                            Qt.openUrlExternally(Ai.models[Ai.currentModelId].key_get_link);
                        }
                    }
                }

                Rectangle {
                    Layout.fillWidth: true
                    visible: Ai.models[Ai.currentModelId]?.key_get_description
                    implicitHeight: keyHelpText.implicitHeight + 16
                    color: Appearance.colors.colLayer2
                    radius: Appearance.rounding.small
                    border.color: Appearance.colors.colOutlineVariant
                    border.width: 1

                    StyledText {
                        id: keyHelpText
                        anchors.fill: parent
                        anchors.margins: 8
                        text: Ai.models[Ai.currentModelId]?.key_get_description || ""
                        font.pixelSize: Appearance.font.pixelSize.smaller
                        color: Appearance.colors.colOnLayer2
                        wrapMode: Text.Wrap
                        textFormat: Text.MarkdownText
                    }
                }
            }
        }
    }

    ContentSection {
        title: qsTr("模型参数")

        ContentSubsection {
            title: qsTr("温度设置")
            tooltip: qsTr("控制模型输出的随机性，值越高越随机")

            ConfigRow {
                ConfigSpinBox {
                    text: qsTr("温度")
                    value: Math.round(Ai.temperature * 100) / 100
                    from: 0
                    to: 200
                    stepSize: 5
                    onValueChanged: {
                        Ai.setTemperature(value / 100);
                    }
                }

                StyledText {
                    Layout.fillWidth: true
                    text: qsTr("当前值: %1").arg(Ai.temperature.toFixed(2))
                    font.pixelSize: Appearance.font.pixelSize.smaller
                    color: Appearance.colors.colSubtext
                    horizontalAlignment: Text.AlignRight
                }
            }
        }

        ContentSubsection {
            title: qsTr("系统提示")
            tooltip: qsTr("设置AI助手的行为和角色")

            MaterialTextField {
                Layout.fillWidth: true
                placeholderText: qsTr("输入系统提示...")
                text: ConfigOptions.ai?.systemPrompt || ""
                wrapMode: TextEdit.Wrap
                onTextChanged: {
                    ConfigLoader.setConfigValueAndSave("ai.systemPrompt", text);
                }
            }
        }
    }

    ContentSection {
        title: qsTr("自定义模型")

        ContentSubsection {
            title: qsTr("添加自定义模型")
            tooltip: qsTr("添加自己的AI模型配置")

            ColumnLayout {
                Layout.fillWidth: true
                spacing: 8

                ConfigRow {
                    MaterialTextField {
                        id: customModelName
                        Layout.fillWidth: true
                        placeholderText: qsTr("模型名称")
                    }
                    MaterialTextField {
                        id: customModelDisplayName
                        Layout.fillWidth: true
                        placeholderText: qsTr("显示名称")
                    }
                }

                ConfigRow {
                    MaterialTextField {
                        id: customModelEndpoint
                        Layout.fillWidth: true
                        placeholderText: qsTr("API端点URL")
                    }
                    MaterialTextField {
                        id: customModelId
                        Layout.fillWidth: true
                        placeholderText: qsTr("模型ID")
                    }
                }

                ConfigRow {
                    ConfigSelectionArray {
                        id: customApiFormat
                        Layout.fillWidth: true
                        currentValue: "openai"
                        options: [
                            { displayName: "OpenAI", value: "openai" },
                            { displayName: "Gemini", value: "gemini" }
                        ]
                    }
                    ConfigSwitch {
                        id: customRequiresKey
                        text: qsTr("需要API密钥")
                        checked: true
                    }
                }

                MaterialTextField {
                    id: customModelDescription
                    Layout.fillWidth: true
                    placeholderText: qsTr("模型描述")
                    wrapMode: TextEdit.Wrap
                }

                DialogButton {
                    buttonText: qsTr("添加模型")
                    enabled: customModelName.text.length > 0 && customModelEndpoint.text.length > 0
                    onClicked: {
                        const modelConfig = {
                            name: customModelName.text,
                            displayName: customModelDisplayName.text || customModelName.text,
                            endpoint: customModelEndpoint.text,
                            modelId: customModelId.text || customModelName.text,
                            description: customModelDescription.text,
                            apiFormat: customApiFormat.currentValue,
                            requiresKey: customRequiresKey.checked,
                            keyId: "custom-" + customModelName.text.toLowerCase().replace(/[^a-z0-9]/g, '-')
                        };

                        Ai.addCustomModel(modelConfig);

                        // 清空输入字段
                        customModelName.text = "";
                        customModelDisplayName.text = "";
                        customModelEndpoint.text = "";
                        customModelId.text = "";
                        customModelDescription.text = "";
                    }
                }
            }
        }

        ContentSubsection {
            title: qsTr("模型管理")
            tooltip: qsTr("管理所有可用的模型")

            ColumnLayout {
                Layout.fillWidth: true
                spacing: 8

                Repeater {
                    model: Ai.modelList
                    delegate: Rectangle {
                        required property string modelData
                        Layout.fillWidth: true
                        implicitHeight: modelItemLayout.implicitHeight + 16
                        color: Appearance.colors.colLayer2
                        radius: Appearance.rounding.small
                        border.color: modelData === Ai.currentModelId ? Appearance.colors.colPrimary : Appearance.colors.colOutlineVariant
                        border.width: 1

                        RowLayout {
                            id: modelItemLayout
                            anchors.fill: parent
                            anchors.margins: 8
                            spacing: 10

                            MaterialSymbol {
                                text: Ai.models[modelData]?.icon || "smart_toy"
                                iconSize: Appearance.font.pixelSize.normal
                                color: modelData === Ai.currentModelId ? Appearance.colors.colPrimary : Appearance.colors.colOnLayer2
                            }

                            ColumnLayout {
                                Layout.fillWidth: true
                                spacing: 2

                                StyledText {
                                    text: Ai.models[modelData]?.name || modelData
                                    font.pixelSize: Appearance.font.pixelSize.small
                                    font.weight: Font.Medium
                                    color: Appearance.colors.colOnLayer2
                                }

                                StyledText {
                                    text: {
                                        const model = Ai.models[modelData];
                                        let info = [];
                                        if (model?.custom) info.push(qsTr("自定义"));
                                        if (model?.requires_key) info.push(qsTr("需要密钥"));
                                        if (model?.api_format) info.push(model.api_format.toUpperCase());
                                        return info.join(" • ");
                                    }
                                    font.pixelSize: Appearance.font.pixelSize.smaller
                                    color: Appearance.colors.colSubtext
                                    visible: text.length > 0
                                }
                            }

                            DialogButton {
                                buttonText: qsTr("选择")
                                visible: modelData !== Ai.currentModelId
                                onClicked: {
                                    Ai.setModel(modelData, true);
                                }
                            }

                            DialogButton {
                                buttonText: qsTr("删除")
                                visible: Ai.models[modelData]?.custom === true
                                onClicked: {
                                    deleteConfirmDialog.modelToDelete = modelData;
                                    deleteConfirmDialog.open();
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    ContentSection {
        title: qsTr("高级设置")

        ContentSubsection {
            title: qsTr("请求配置")
            tooltip: qsTr("配置API请求的高级选项")

            ColumnLayout {
                Layout.fillWidth: true
                spacing: 8

                ConfigRow {
                    ConfigSpinBox {
                        text: qsTr("请求超时 (秒)")
                        value: ConfigOptions.ai?.requestTimeout || 60
                        from: 10
                        to: 300
                        stepSize: 10
                        onValueChanged: {
                            ConfigLoader.setConfigValueAndSave("ai.requestTimeout", value);
                        }
                    }

                    ConfigSpinBox {
                        text: qsTr("最大重试次数")
                        value: ConfigOptions.ai?.maxRetries || 3
                        from: 0
                        to: 10
                        stepSize: 1
                        onValueChanged: {
                            ConfigLoader.setConfigValueAndSave("ai.maxRetries", value);
                        }
                    }
                }

                ConfigSwitch {
                    text: qsTr("启用流式响应")
                    checked: ConfigOptions.ai?.streamResponse !== false
                    onCheckedChanged: {
                        ConfigLoader.setConfigValueAndSave("ai.streamResponse", checked);
                    }
                }

                ConfigSwitch {
                    text: qsTr("保存聊天历史")
                    checked: ConfigOptions.ai?.saveHistory !== false
                    onCheckedChanged: {
                        ConfigLoader.setConfigValueAndSave("ai.saveHistory", checked);
                    }
                }
            }
        }

        ContentSubsection {
            title: qsTr("安全设置")
            tooltip: qsTr("配置AI使用的安全选项")

            ColumnLayout {
                Layout.fillWidth: true
                spacing: 8

                ConfigSwitch {
                    text: qsTr("仅允许本地模型")
                    checked: ConfigOptions.policies?.ai === 2
                    onCheckedChanged: {
                        ConfigLoader.setConfigValueAndSave("policies.ai", checked ? 2 : 1);
                    }
                }

                ConfigSwitch {
                    text: qsTr("验证SSL证书")
                    checked: ConfigOptions.ai?.verifySSL !== false
                    onCheckedChanged: {
                        ConfigLoader.setConfigValueAndSave("ai.verifySSL", checked);
                    }
                }
            }
        }
    }

    ContentSection {
        title: qsTr("聊天管理")

        ConfigRow {
            uniform: true

            DialogButton {
                buttonText: qsTr("清除聊天记录")
                onClicked: {
                    Ai.clearMessages();
                }
            }

            DialogButton {
                buttonText: qsTr("显示当前温度")
                onClicked: {
                    Ai.printTemperature();
                }
            }

            DialogButton {
                buttonText: qsTr("显示API密钥")
                visible: Ai.models[Ai.currentModelId]?.requires_key
                onClicked: {
                    Ai.printApiKey();
                }
            }
        }

        ContentSubsection {
            title: qsTr("导入/导出")
            tooltip: qsTr("备份和恢复AI配置")

            ConfigRow {
                uniform: true

                DialogButton {
                    buttonText: qsTr("导出配置")
                    onClicked: {
                        // 导出AI配置到文件
                        const config = {
                            models: Object.fromEntries(
                                Object.entries(Ai.models).filter(([key, model]) => model.custom)
                            ),
                            settings: {
                                temperature: Ai.temperature,
                                systemPrompt: ConfigOptions.ai?.systemPrompt || ""
                            }
                        };
                        console.log("导出配置:", JSON.stringify(config, null, 2));
                    }
                }

                DialogButton {
                    buttonText: qsTr("导入配置")
                    onClicked: {
                        // 这里可以添加文件选择对话框
                        console.log("导入配置功能待实现");
                    }
                }

                DialogButton {
                    buttonText: qsTr("重置为默认")
                    onClicked: {
                        deleteConfirmDialog.dialogTitle = qsTr("重置配置");
                        deleteConfirmDialog.message = qsTr("确定要重置所有AI配置为默认值吗？这将删除所有自定义模型和设置。");
                        deleteConfirmDialog.modelToDelete = "RESET_ALL";
                        deleteConfirmDialog.open();
                    }
                }
            }
        }
    }
}
