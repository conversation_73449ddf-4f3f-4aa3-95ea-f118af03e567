{"version": 1, "resource": "file:///home/<USER>/.config/quickshell/modules/backgroundWidgets/BackgroundWidgets.qml", "entries": [{"id": "Cl6R.qml", "source": "聊天编辑:“WARN scene: **/modules/backgroundWidgets/BackgroundWidgets.qml[31:-1]: SyntaxError: JSON.parse: Parse error”", "timestamp": 1750739700814}, {"id": "TJme.qml", "timestamp": 1750739830821}, {"id": "SHJe.qml", "source": "聊天编辑:“󰪢 0s 󰜥 󰉋    \n    quickshell \nDetected locale \"C\" with character encoding \"ANSI_X3.4-1968\", which is not UTF-8.\nQt depends on a UTF-8 locale, and has switched to \"C.UTF-8\" instead.\nIf this causes problems, reconfigure your locale. See the locale(1) manual\nfor more information.\n  INFO: Launching config: \"/home/<USER>/.config/quickshell/shell.qml\"\n  INFO: Shell ID: \"29e90fb69f5fbfd9384ddcac00dd6458\" Path ID \"29e90fb69f5fbfd9384ddcac00dd6458\"\n  INFO: Saving logs to \"/run/user/1000/quickshell/by-id/uf3efrcys/log.qslog\"\n  WARN: QSettings::value: Empty key passed\n  WARN: $HYPRLAND_INSTANCE_SIGNATURE is unset. Cannot connect to hyprland.\n  WARN: The active compositor does not support the hyprland_surface_v1 protocol. HyprlandWindow will not work.\n  WARN: The active compositor does not support the hyprland_surface_v1 protocol. HyprlandWindow will not work.\n  WARN: The active compositor does not support the hyprland_surface_v1 protocol. HyprlandWindow will not work.\n  WARN: The active compositor does not support the hyprland_surface_v1 protocol. HyprlandWindow will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  INFO quickshell.service.notifications: Starting notification server\n  WARN quickshell.service.notifications: Could not register notification server at org.freedesktop.Notifications, presumably because one is already registered.\n  WARN quickshell.service.notifications: Registration will be attempted again if the active service is unregistered.\n  INFO: Configuration Loaded\n  WARN scene: **/modules/backgroundWidgets/BackgroundWidgets.qml[31:-1]: SyntaxError: JSON.parse: Parse error\n DEBUG qml: [Notifications] File loaded\n  WARN: The active compositor does not support the hyprland_surface_v1 protocol. HyprlandWindow will not work.\n  WARN: The active compositor does not support the hyprland_surface_v1 protocol. HyprlandWindow will not work.\n\n”", "timestamp": 1750755354856}, {"id": "M0zw.qml", "source": "聊天编辑:“󰪢 0s 󰜥 󰉋    \n    quickshell \nDetected locale \"C\" with character encoding \"ANSI_X3.4-1968\", which is not UTF-8.\nQt depends on a UTF-8 locale, and has switched to \"C.UTF-8\" instead.\nIf this causes problems, reconfigure your locale. See the locale(1) manual\nfor more information.\n  INFO: Launching config: \"/home/<USER>/.config/quickshell/shell.qml\"\n  INFO: Shell ID: \"29e90fb69f5fbfd9384ddcac00dd6458\" Path ID \"29e90fb69f5fbfd9384ddcac00dd6458\"\n  INFO: Saving logs to \"/run/user/1000/quickshell/by-id/uf3efrcys/log.qslog\"\n  WARN: QSettings::value: Empty key passed\n  WARN: $HYPRLAND_INSTANCE_SIGNATURE is unset. Cannot connect to hyprland.\n  WARN: The active compositor does not support the hyprland_surface_v1 protocol. HyprlandWindow will not work.\n  WARN: The active compositor does not support the hyprland_surface_v1 protocol. HyprlandWindow will not work.\n  WARN: The active compositor does not support the hyprland_surface_v1 protocol. HyprlandWindow will not work.\n  WARN: The active compositor does not support the hyprland_surface_v1 protocol. HyprlandWindow will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  INFO quickshell.service.notifications: Starting notification server\n  WARN quickshell.service.notifications: Could not register notification server at org.freedesktop.Notifications, presumably because one is already registered.\n  WARN quickshell.service.notifications: Registration will be attempted again if the active service is unregistered.\n  INFO: Configuration Loaded\n  WARN scene: **/modules/backgroundWidgets/BackgroundWidgets.qml[31:-1]: SyntaxError: JSON.parse: Parse error\n DEBUG qml: [Notifications] File loaded\n  WARN: The active compositor does not support the hyprland_surface_v1 protocol. HyprlandWindow will not work.\n  WARN: The active compositor does not support the hyprland_surface_v1 protocol. HyprlandWindow will not work.\n\n”", "timestamp": 1750755402073}, {"id": "WDdI.qml", "source": "聊天编辑:“󰪢 0s 󰜥 󰉋    \n    quickshell \nDetected locale \"C\" with character encoding \"ANSI_X3.4-1968\", which is not UTF-8.\nQt depends on a UTF-8 locale, and has switched to \"C.UTF-8\" instead.\nIf this causes problems, reconfigure your locale. See the locale(1) manual\nfor more information.\n  INFO: Launching config: \"/home/<USER>/.config/quickshell/shell.qml\"\n  INFO: Shell ID: \"29e90fb69f5fbfd9384ddcac00dd6458\" Path ID \"29e90fb69f5fbfd9384ddcac00dd6458\"\n  INFO: Saving logs to \"/run/user/1000/quickshell/by-id/uf3efrcys/log.qslog\"\n  WARN: QSettings::value: Empty key passed\n  WARN: $HYPRLAND_INSTANCE_SIGNATURE is unset. Cannot connect to hyprland.\n  WARN: The active compositor does not support the hyprland_surface_v1 protocol. HyprlandWindow will not work.\n  WARN: The active compositor does not support the hyprland_surface_v1 protocol. HyprlandWindow will not work.\n  WARN: The active compositor does not support the hyprland_surface_v1 protocol. HyprlandWindow will not work.\n  WARN: The active compositor does not support the hyprland_surface_v1 protocol. HyprlandWindow will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  WARN: The active compositor does not support hyprland_global_shortcuts_v1.\n  WARN: GlobalShortcut will not work.\n  INFO quickshell.service.notifications: Starting notification server\n  WARN quickshell.service.notifications: Could not register notification server at org.freedesktop.Notifications, presumably because one is already registered.\n  WARN quickshell.service.notifications: Registration will be attempted again if the active service is unregistered.\n  INFO: Configuration Loaded\n  WARN scene: **/modules/backgroundWidgets/BackgroundWidgets.qml[31:-1]: SyntaxError: JSON.parse: Parse error\n DEBUG qml: [Notifications] File loaded\n  WARN: The active compositor does not support the hyprland_surface_v1 protocol. HyprlandWindow will not work.\n  WARN: The active compositor does not support the hyprland_surface_v1 protocol. HyprlandWindow will not work.\n\n”", "timestamp": 1750755428308}, {"id": "lJFP.qml", "timestamp": 1750758923834}, {"id": "WnOK.qml", "source": "工作区编辑", "timestamp": 1750762304548}, {"id": "IXlX.qml", "source": "工作区编辑", "timestamp": 1750762319766}]}