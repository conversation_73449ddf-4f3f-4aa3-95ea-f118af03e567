# User Preferences
- User prefers graphical configuration interfaces for AI system settings rather than command-line or text-based configuration.
- User wants AI configuration to support custom model IDs, base URIs, API key management, ability to add new models, and proper icon display in model selection buttons.

# QuickShell Specifics
- In QuickShell, Qt.openUrlExternally() opens URLs in external browser rather than handling internal navigation, and QtGraphicalEffects module may not be available by default.