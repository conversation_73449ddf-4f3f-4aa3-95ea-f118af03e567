{"path": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/bar/UtilButtons.qml"}, "originalCode": "import \"root:/modules/common\"\nimport \"root:/modules/common/widgets\"\nimport QtQuick\nimport QtQuick.Layouts\nimport Quickshell\nimport Quickshell.Io\nimport Quickshell.Hyprland\nimport Quickshell.Services.Pipewire\n\nItem {\n    id: root\n    property bool borderless: ConfigOptions.bar.borderless\n    implicitWidth: rowLayout.implicitWidth + rowLayout.spacing * 2\n    implicitHeight: rowLayout.implicitHeight\n\n    RowLayout {\n        id: rowLayout\n\n        spacing: 4\n        anchors.centerIn: parent\n\n        Loader {\n            active: ConfigOptions.bar.utilButtons.showScreenSnip\n            visible: ConfigOptions.bar.utilButtons.showScreenSnip\n            sourceComponent: CircleUtilButton {\n                Layout.alignment: Qt.AlignVCenter\n                onClicked: Hyprland.dispatch(\"exec hyprshot --freeze --clipboard-only --mode region --silent\")\n                MaterialSymbol {\n                    horizontalAlignment: Qt.AlignHCenter\n                    fill: 1\n                    text: \"screenshot_monitor\"\n                    iconSize: Appearance.font.pixelSize.large\n                    color: Appearance.colors.colOnLayer2\n                }\n            }\n        }\n\n        Loader {\n            active: ConfigOptions.bar.utilButtons.showColorPicker\n            visible: ConfigOptions.bar.utilButtons.showColorPicker\n            sourceComponent: CircleUtilButton {\n                Layout.alignment: Qt.AlignVCenter\n                onClicked: Hyprland.dispatch(\"exec hyprpicker -a\")\n                MaterialSymbol {\n                    horizontalAlignment: Qt.AlignHCenter\n                    fill: 1\n                    text: \"colorize\"\n                    iconSize: Appearance.font.pixelSize.large\n                    color: Appearance.colors.colOnLayer2\n                }\n            }\n        }\n\n        Loader {\n            active: ConfigOptions.bar.utilButtons.showKeyboardToggle\n            visible: ConfigOptions.bar.utilButtons.showKeyboardToggle\n            sourceComponent: CircleUtilButton {\n                Layout.alignment: Qt.AlignVCenter\n                onClicked: Hyprland.dispatch(\"global quickshell:oskToggle\")\n                MaterialSymbol {\n                    horizontalAlignment: Qt.AlignHCenter\n                    fill: 0\n                    text: \"keyboard\"\n                    iconSize: Appearance.font.pixelSize.large\n                    color: Appearance.colors.colOnLayer2\n                }\n            }\n        }\n\n        Loader {\n            active: ConfigOptions.bar.utilButtons.showMicToggle\n            visible: ConfigOptions.bar.utilButtons.showMicToggle\n            sourceComponent: CircleUtilButton {\n                Layout.alignment: Qt.AlignVCenter\n                onClicked: Hyprland.dispatch(\"exec wpctl set-mute @DEFAULT_SOURCE@ toggle\")\n                MaterialSymbol {\n                    horizontalAlignment: Qt.AlignHCenter\n                    fill: 0\n                    text: Pipewire.defaultAudioSource?.audio?.muted ? \"mic_off\" : \"mic\"\n                    iconSize: Appearance.font.pixelSize.large\n                    color: Appearance.colors.colOnLayer2\n                }\n            }\n        }\n\n        Loader {\n            active: ConfigOptions.bar.utilButtons.showDarkModeToggle\n            visible: ConfigOptions.bar.utilButtons.showDarkModeToggle\n            sourceComponent: CircleUtilButton {\n                Layout.alignment: Qt.AlignVCenter\n                onClicked: event => {\n                    if (Appearance.m3colors.darkmode) {\n                        Hyprland.dispatch(`exec ${Directories.wallpaperSwitchScriptPath} --mode light --noswitch`);\n                    } else {\n                        Hyprland.dispatch(`exec ${Directories.wallpaperSwitchScriptPath} --mode dark --noswitch`);\n                    }\n                }\n                MaterialSymbol {\n                    horizontalAlignment: Qt.AlignHCenter\n                    fill: 0\n                    text: Appearance.m3colors.darkmode ? \"light_mode\" : \"dark_mode\"\n                    iconSize: Appearance.font.pixelSize.large\n                    color: Appearance.colors.colOnLayer2\n                }\n            }\n        }\n    }\n}\n", "modifiedCode": "import \"root:/modules/common\"\nimport \"root:/modules/common/widgets\"\nimport QtQuick\nimport QtQuick.Layouts\nimport Quickshell\nimport Quickshell.Io\nimport Quickshell.Hyprland\nimport Quickshell.Services.Pipewire\n\nItem {\n    id: root\n    property bool borderless: ConfigOptions.bar.borderless\n    implicitWidth: rowLayout.implicitWidth + rowLayout.spacing * 2\n    implicitHeight: rowLayout.implicitHeight\n\n    RowLayout {\n        id: rowLayout\n\n        spacing: 4\n        anchors.centerIn: parent\n\n        Loader {\n            active: ConfigOptions.bar.utilButtons.showScreenSnip\n            visible: ConfigOptions.bar.utilButtons.showScreenSnip\n            sourceComponent: CircleUtilButton {\n                Layout.alignment: Qt.AlignVCenter\n                onClicked: Hyprland.dispatch(\"exec hyprshot --freeze --clipboard-only --mode region --silent\")\n                MaterialSymbol {\n                    horizontalAlignment: Qt.AlignHCenter\n                    fill: 1\n                    text: \"screenshot_region\"\n                    iconSize: Appearance.font.pixelSize.large\n                    color: Appearance.colors.colOnLayer2\n                }\n            }\n        }\n\n        Loader {\n            active: ConfigOptions.bar.utilButtons.showColorPicker\n            visible: ConfigOptions.bar.utilButtons.showColorPicker\n            sourceComponent: CircleUtilButton {\n                Layout.alignment: Qt.AlignVCenter\n                onClicked: Hyprland.dispatch(\"exec hyprpicker -a\")\n                MaterialSymbol {\n                    horizontalAlignment: Qt.AlignHCenter\n                    fill: 1\n                    text: \"colorize\"\n                    iconSize: Appearance.font.pixelSize.large\n                    color: Appearance.colors.colOnLayer2\n                }\n            }\n        }\n\n        Loader {\n            active: ConfigOptions.bar.utilButtons.showKeyboardToggle\n            visible: ConfigOptions.bar.utilButtons.showKeyboardToggle\n            sourceComponent: CircleUtilButton {\n                Layout.alignment: Qt.AlignVCenter\n                onClicked: Hyprland.dispatch(\"global quickshell:oskToggle\")\n                MaterialSymbol {\n                    horizontalAlignment: Qt.AlignHCenter\n                    fill: 0\n                    text: \"keyboard\"\n                    iconSize: Appearance.font.pixelSize.large\n                    color: Appearance.colors.colOnLayer2\n                }\n            }\n        }\n\n        Loader {\n            active: ConfigOptions.bar.utilButtons.showMicToggle\n            visible: ConfigOptions.bar.utilButtons.showMicToggle\n            sourceComponent: CircleUtilButton {\n                Layout.alignment: Qt.AlignVCenter\n                onClicked: Hyprland.dispatch(\"exec wpctl set-mute @DEFAULT_SOURCE@ toggle\")\n                MaterialSymbol {\n                    horizontalAlignment: Qt.AlignHCenter\n                    fill: 0\n                    text: Pipewire.defaultAudioSource?.audio?.muted ? \"mic_off\" : \"mic\"\n                    iconSize: Appearance.font.pixelSize.large\n                    color: Appearance.colors.colOnLayer2\n                }\n            }\n        }\n\n        Loader {\n            active: ConfigOptions.bar.utilButtons.showDarkModeToggle\n            visible: ConfigOptions.bar.utilButtons.showDarkModeToggle\n            sourceComponent: CircleUtilButton {\n                Layout.alignment: Qt.AlignVCenter\n                onClicked: event => {\n                    if (Appearance.m3colors.darkmode) {\n                        Hyprland.dispatch(`exec ${Directories.wallpaperSwitchScriptPath} --mode light --noswitch`);\n                    } else {\n                        Hyprland.dispatch(`exec ${Directories.wallpaperSwitchScriptPath} --mode dark --noswitch`);\n                    }\n                }\n                MaterialSymbol {\n                    horizontalAlignment: Qt.AlignHCenter\n                    fill: 0\n                    text: Appearance.m3colors.darkmode ? \"light_mode\" : \"dark_mode\"\n                    iconSize: Appearance.font.pixelSize.large\n                    color: Appearance.colors.colOnLayer2\n                }\n            }\n        }\n    }\n}\n"}