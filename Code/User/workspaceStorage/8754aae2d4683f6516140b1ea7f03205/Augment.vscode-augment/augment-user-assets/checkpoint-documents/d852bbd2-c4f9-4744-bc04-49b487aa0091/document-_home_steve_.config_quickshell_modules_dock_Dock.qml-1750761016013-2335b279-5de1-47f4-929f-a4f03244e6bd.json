{"path": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/dock/Dock.qml"}, "originalCode": "import \"root:/\"\nimport \"root:/services\"\nimport \"root:/modules/common\"\nimport \"root:/modules/common/widgets\"\nimport QtQuick\nimport QtQuick.Controls\nimport QtQuick.Effects\nimport QtQuick.Layouts\nimport Quickshell.Io\nimport Quickshell\nimport Quickshell.Widgets\nimport Quickshell.Wayland\nimport Quickshell.Hyprland\n\nScope { // Scope\n    id: root\n    property bool pinned: ConfigOptions?.dock.pinnedOnStartup ?? false\n\n    Variants { // For each monitor\n        model: Quickshell.screens\n\n        LazyLoader {\n            id: dockLoader\n            required property var modelData\n            activeAsync: ConfigOptions?.dock.hoverToReveal || (!ToplevelManager.activeToplevel?.activated)\n\n            component: PanelWindow { // Window\n                id: dockRoot\n                screen: dockLoader.modelData\n                \n                property bool reveal: root.pinned \n                    || (ConfigOptions?.dock.hoverToReveal && dockMouseArea.containsMouse) \n                    || dockApps.requestDockShow \n                    || (!ToplevelManager.activeToplevel?.activated)\n\n                anchors {\n                    bottom: true\n                    left: true\n                    right: true\n                }\n\n                exclusiveZone: root.pinned ? implicitHeight \n                    - (Appearance.sizes.hyprlandGapsOut) \n                    - (Appearance.sizes.elevationMargin - Appearance.sizes.hyprlandGapsOut) : 0\n\n                implicitWidth: dockBackground.implicitWidth\n                WlrLayershell.namespace: \"quickshell:dock\"\n                color: \"transparent\"\n\n                implicitHeight: (ConfigOptions?.dock.height ?? 70) + Appearance.sizes.elevationMargin + Appearance.sizes.hyprlandGapsOut\n\n                mask: Region {\n                    item: dockMouseArea\n                }\n\n                MouseArea {\n                    id: dockMouseArea\n                    anchors.top: parent.top\n                    height: parent.height\n                    anchors.topMargin: dockRoot.reveal ? 0 : \n                        ConfigOptions?.dock.hoverToReveal ? (dockRoot.implicitHeight - ConfigOptions.dock.hoverRegionHeight) :\n                        (dockRoot.implicitHeight + 1)\n                        \n                    anchors.left: parent.left\n                    anchors.right: parent.right\n                    hoverEnabled: true\n\n                    Behavior on anchors.topMargin {\n                        animation: Appearance.animation.elementMoveFast.numberAnimation.createObject(this)\n                    }\n\n                    Item {\n                        id: dockHoverRegion\n                        anchors.fill: parent\n\n                        Item { // Wrapper for the dock background\n                            id: dockBackground\n                            anchors {\n                                top: parent.top\n                                bottom: parent.bottom\n                                horizontalCenter: parent.horizontalCenter\n                            }\n\n                            implicitWidth: dockRow.implicitWidth + 5 * 2\n                            height: parent.height - Appearance.sizes.elevationMargin - Appearance.sizes.hyprlandGapsOut\n\n                            StyledRectangularShadow {\n                                target: dockVisualBackground\n                            }\n                            Rectangle { // The real rectangle that is visible\n                                id: dockVisualBackground\n                                property real margin: Appearance.sizes.elevationMargin\n                                anchors.fill: parent\n                                anchors.topMargin: Appearance.sizes.elevationMargin\n                                anchors.bottomMargin: Appearance.sizes.hyprlandGapsOut\n                                color: Appearance.colors.colLayer0\n                                radius: Appearance.rounding.large\n                            }\n\n                            RowLayout {\n                                id: dockRow\n                                anchors.top: parent.top\n                                anchors.bottom: parent.bottom\n                                anchors.horizontalCenter: parent.horizontalCenter\n                                spacing: 3\n                                property real padding: 5\n\n                                VerticalButtonGroup {\n                                    Layout.topMargin: Appearance.sizes.hyprlandGapsOut // why does this work\n                                    GroupButton { // Pin button\n                                        baseWidth: 35\n                                        baseHeight: 35\n                                        clickedWidth: baseWidth\n                                        clickedHeight: baseHeight + 20\n                                        buttonRadius: Appearance.rounding.normal\n                                        toggled: root.pinned\n                                        onClicked: root.pinned = !root.pinned\n                                        contentItem: MaterialSymbol {\n                                            text: \"push_pin\"\n                                            horizontalAlignment: Text.AlignHCenter\n                                            iconSize: Appearance.font.pixelSize.larger\n                                            color: root.pinned ? Appearance.m3colors.m3onPrimary : Appearance.colors.colOnLayer0\n                                        }\n                                    }\n                                }\n                                DockSeparator {}\n                                DockApps { id: dockApps; }\n                                DockSeparator {}\n                                DockButton {\n                                    Layout.fillHeight: true\n                                    onClicked: Hyprland.dispatch(\"global quickshell:overviewToggle\")\n                                    contentItem: MaterialSymbol {\n                                        anchors.fill: parent\n                                        horizontalAlignment: Text.AlignHCenter\n                                        font.pixelSize: parent.width / 2\n                                        text: \"apps\"\n                                        color: Appearance.colors.colOnLayer0\n                                    }\n                                }\n                            }\n                        }    \n                    }\n\n                }\n            }\n        }\n    }\n}\n", "modifiedCode": "import \"root:/\"\nimport \"root:/services\"\nimport \"root:/modules/common\"\nimport \"root:/modules/common/widgets\"\nimport QtQuick\nimport QtQuick.Controls\nimport QtQuick.Effects\nimport QtQuick.Layouts\nimport Quickshell.Io\nimport Quickshell\nimport Quickshell.Widgets\nimport Quickshell.Wayland\nimport Quickshell.Hyprland\n\nScope { // Scope\n    id: root\n    property bool pinned: ConfigOptions?.dock.pinnedOnStartup ?? false\n\n    Variants { // For each monitor\n        model: Quickshell.screens\n\n        LazyLoader {\n            id: dockLoader\n            required property var modelData\n            activeAsync: ConfigOptions?.dock.hoverToReveal || (!ToplevelManager.activeToplevel?.activated)\n\n            component: PanelWindow { // Window\n                id: dockRoot\n                screen: dockLoader.modelData\n                \n                property bool reveal: root.pinned \n                    || (ConfigOptions?.dock.hoverToReveal && dockMouseArea.containsMouse) \n                    || dockApps.requestDockShow \n                    || (!ToplevelManager.activeToplevel?.activated)\n\n                anchors {\n                    bottom: true\n                    left: true\n                    right: true\n                }\n\n                exclusiveZone: root.pinned ? implicitHeight \n                    - (Appearance.sizes.hyprlandGapsOut) \n                    - (Appearance.sizes.elevationMargin - Appearance.sizes.hyprlandGapsOut) : 0\n\n                implicitWidth: dockBackground.implicitWidth\n                WlrLayershell.namespace: \"quickshell:dock\"\n                color: \"transparent\"\n\n                implicitHeight: (ConfigOptions?.dock.height ?? 70) + Appearance.sizes.elevationMargin + Appearance.sizes.hyprlandGapsOut\n\n                mask: Region {\n                    item: dockMouseArea\n                }\n\n                MouseArea {\n                    id: dockMouseArea\n                    anchors.top: parent.top\n                    height: parent.height\n                    anchors.topMargin: dockRoot.reveal ? 0 : \n                        ConfigOptions?.dock.hoverToReveal ? (dockRoot.implicitHeight - ConfigOptions.dock.hoverRegionHeight) :\n                        (dockRoot.implicitHeight + 1)\n                        \n                    anchors.left: parent.left\n                    anchors.right: parent.right\n                    hoverEnabled: true\n\n                    Behavior on anchors.topMargin {\n                        animation: Appearance.animation.elementMoveFast.numberAnimation.createObject(this)\n                    }\n\n                    Item {\n                        id: dockHoverRegion\n                        anchors.fill: parent\n\n                        Item { // Wrapper for the dock background\n                            id: dockBackground\n                            anchors {\n                                top: parent.top\n                                bottom: parent.bottom\n                                horizontalCenter: parent.horizontalCenter\n                            }\n\n                            implicitWidth: dockRow.implicitWidth + 5 * 2\n                            height: parent.height - Appearance.sizes.elevationMargin - Appearance.sizes.hyprlandGapsOut\n\n                            StyledRectangularShadow {\n                                target: dockVisualBackground\n                            }\n                            Rectangle { // The real rectangle that is visible\n                                id: dockVisualBackground\n                                property real margin: Appearance.sizes.elevationMargin\n                                anchors.fill: parent\n                                anchors.topMargin: Appearance.sizes.elevationMargin\n                                anchors.bottomMargin: Appearance.sizes.hyprlandGapsOut\n                                color: Appearance.colors.colLayer0\n                                radius: Appearance.rounding.large\n                            }\n\n                            RowLayout {\n                                id: dockRow\n                                anchors.top: parent.top\n                                anchors.bottom: parent.bottom\n                                anchors.horizontalCenter: parent.horizontalCenter\n                                spacing: 3\n                                property real padding: 5\n\n                                VerticalButtonGroup {\n                                    Layout.topMargin: Appearance.sizes.hyprlandGapsOut // why does this work\n                                    GroupButton { // Pin button\n                                        baseWidth: 35\n                                        baseHeight: 35\n                                        clickedWidth: baseWidth\n                                        clickedHeight: baseHeight + 20\n                                        buttonRadius: Appearance.rounding.normal\n                                        toggled: root.pinned\n                                        onClicked: root.pinned = !root.pinned\n                                        contentItem: MaterialSymbol {\n                                            text: \"keep\"\n                                            horizontalAlignment: Text.AlignHCenter\n                                            iconSize: Appearance.font.pixelSize.larger\n                                            color: root.pinned ? Appearance.m3colors.m3onPrimary : Appearance.colors.colOnLayer0\n                                        }\n                                    }\n                                }\n                                DockSeparator {}\n                                DockApps { id: dockApps; }\n                                DockSeparator {}\n                                DockButton {\n                                    Layout.fillHeight: true\n                                    onClicked: Hyprland.dispatch(\"global quickshell:overviewToggle\")\n                                    contentItem: MaterialSymbol {\n                                        anchors.fill: parent\n                                        horizontalAlignment: Text.AlignHCenter\n                                        font.pixelSize: parent.width / 2\n                                        text: \"apps\"\n                                        color: Appearance.colors.colOnLayer0\n                                    }\n                                }\n                            }\n                        }    \n                    }\n\n                }\n            }\n        }\n    }\n}\n"}