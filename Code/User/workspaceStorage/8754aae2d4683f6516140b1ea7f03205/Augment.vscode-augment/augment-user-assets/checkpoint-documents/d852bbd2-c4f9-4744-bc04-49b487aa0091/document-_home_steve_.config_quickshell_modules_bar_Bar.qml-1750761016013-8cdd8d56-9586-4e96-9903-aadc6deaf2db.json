{"path": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/bar/Bar.qml"}, "originalCode": "import \"root:/\"\nimport \"root:/services\"\nimport \"root:/modules/common/\"\nimport \"root:/modules/common/widgets\"\nimport \"root:/modules/common/functions/color_utils.js\" as ColorUtils\nimport QtQuick\nimport QtQuick.Controls\nimport QtQuick.Layouts\nimport Qt5Compat.GraphicalEffects\nimport Quickshell\nimport Quickshell.Wayland\nimport Quickshell.Hyprland\nimport Quickshell.Services.UPower\n\nScope {\n    id: bar\n\n    readonly property int barHeight: Appearance.sizes.barHeight\n    readonly property int osdHideMouseMoveThreshold: 20\n    property bool showBarBackground: ConfigOptions.bar.showBackground\n\n    component VerticalBarSeparator: Rectangle {\n        Layout.topMargin: barHeight / 3\n        Layout.bottomMargin: barHeight / 3\n        Layout.fillHeight: true\n        implicitWidth: 1\n        color: Appearance.colors.colOutlineVariant\n    }\n\n    Variants { // For each monitor\n        model: {\n            const screens = Quickshell.screens;\n            const list = ConfigOptions.bar.screenList;\n            if (!list || list.length === 0)\n                return screens;\n            return screens.filter(screen => list.includes(screen.name));\n        }\n\n        PanelWindow { // Bar window\n            id: barRoot\n            screen: modelData\n\n            property ShellScreen modelData\n            property var brightnessMonitor: Brightness.getMonitorForScreen(modelData)\n            property real useShortenedForm: (Appearance.sizes.barHellaShortenScreenWidthThreshold >= screen.width) ? 2 :\n                (Appearance.sizes.barShortenScreenWidthThreshold >= screen.width) ? 1 : 0\n            readonly property int centerSideModuleWidth: \n                (useShortenedForm == 2) ? Appearance.sizes.barCenterSideModuleWidthHellaShortened :\n                (useShortenedForm == 1) ? Appearance.sizes.barCenterSideModuleWidthShortened : \n                    Appearance.sizes.barCenterSideModuleWidth\n\n            WlrLayershell.namespace: \"quickshell:bar\"\n            implicitHeight: barHeight + Appearance.rounding.screenRounding\n            exclusiveZone: showBarBackground ? barHeight : (barHeight - 4)\n            mask: Region {\n                item: barContent\n            }\n            color: \"transparent\"\n\n            anchors {\n                top: !ConfigOptions.bar.bottom\n                bottom: ConfigOptions.bar.bottom\n                left: true\n                right: true\n            }\n\n            Rectangle { // Bar background\n                id: barContent\n                anchors {\n                    right: parent.right\n                    left: parent.left\n                    top: !ConfigOptions.bar.bottom ? parent.top : undefined\n                    bottom: ConfigOptions.bar.bottom ? parent.bottom : undefined\n                }\n                color: showBarBackground ? Appearance.colors.colLayer0 : \"transparent\"\n                height: barHeight\n                \n                MouseArea { // Left side | scroll to change brightness\n                    id: barLeftSideMouseArea\n                    anchors.left: parent.left\n                    implicitHeight: barHeight\n                    width: (barRoot.width - middleSection.width) / 2\n                    property bool hovered: false\n                    property real lastScrollX: 0\n                    property real lastScrollY: 0\n                    property bool trackingScroll: false\n                    acceptedButtons: Qt.LeftButton\n                    hoverEnabled: true\n                    propagateComposedEvents: true\n                    onEntered: (event) => {\n                        barLeftSideMouseArea.hovered = true\n                    }\n                    onExited: (event) => {\n                        barLeftSideMouseArea.hovered = false\n                        barLeftSideMouseArea.trackingScroll = false\n                    }\n                    onPressed: (event) => {\n                        if (event.button === Qt.LeftButton) {\n                            Hyprland.dispatch('global quickshell:sidebarLeftOpen')\n                        }\n                    }\n                    // Scroll to change brightness\n                    WheelHandler {\n                        onWheel: (event) => {\n                            if (event.angleDelta.y < 0)\n                                barRoot.brightnessMonitor.setBrightness(barRoot.brightnessMonitor.brightness - 0.05);\n                            else if (event.angleDelta.y > 0)\n                                barRoot.brightnessMonitor.setBrightness(barRoot.brightnessMonitor.brightness + 0.05);\n                            // Store the mouse position and start tracking\n                            barLeftSideMouseArea.lastScrollX = event.x;\n                            barLeftSideMouseArea.lastScrollY = event.y;\n                            barLeftSideMouseArea.trackingScroll = true;\n                        }\n                        acceptedDevices: PointerDevice.Mouse | PointerDevice.TouchPad\n                    }\n                    onPositionChanged: (mouse) => {\n                        if (barLeftSideMouseArea.trackingScroll) {\n                            const dx = mouse.x - barLeftSideMouseArea.lastScrollX;\n                            const dy = mouse.y - barLeftSideMouseArea.lastScrollY;\n                            if (Math.sqrt(dx*dx + dy*dy) > osdHideMouseMoveThreshold) {\n                                Hyprland.dispatch('global quickshell:osdBrightnessHide')\n                                barLeftSideMouseArea.trackingScroll = false;\n                            }\n                        }\n                    }\n                    Item {  // Left section\n                        anchors.fill: parent\n                        implicitHeight: leftSectionRowLayout.implicitHeight\n                        implicitWidth: leftSectionRowLayout.implicitWidth\n\n                        ScrollHint {\n                            reveal: barLeftSideMouseArea.hovered\n                            icon: \"light_mode\"\n                            tooltipText: qsTr(\"滑动以更改亮度\")\n                            side: \"left\"\n                            anchors.left: parent.left\n                            anchors.verticalCenter: parent.verticalCenter\n                            \n                        }\n                        \n                        RowLayout { // Content\n                            id: leftSectionRowLayout\n                            anchors.fill: parent\n                            spacing: 10\n\n                            RippleButton { // Left sidebar button\n                                Layout.alignment: Qt.AlignLeft | Qt.AlignVCenter\n                                Layout.leftMargin: Appearance.rounding.screenRounding\n                                Layout.fillWidth: false\n                                property real buttonPadding: 5\n                                implicitWidth: distroIcon.width + buttonPadding * 2\n                                implicitHeight: distroIcon.height + buttonPadding * 2\n                                \n                                buttonRadius: Appearance.rounding.full\n                                colBackground: barLeftSideMouseArea.hovered ? Appearance.colors.colLayer1Hover : ColorUtils.transparentize(Appearance.colors.colLayer1Hover, 1)\n                                colBackgroundHover: Appearance.colors.colLayer1Hover\n                                colRipple: Appearance.colors.colLayer1Active\n                                colBackgroundToggled: Appearance.colors.colSecondaryContainer\n                                colBackgroundToggledHover: Appearance.colors.colSecondaryContainerHover\n                                colRippleToggled: Appearance.colors.colSecondaryContainerActive\n                                toggled: GlobalStates.sidebarLeftOpen\n                                    property color colText: toggled ? Appearance.m3colors.m3onSecondaryContainer : Appearance.colors.colOnLayer0\n\n                                onPressed: {\n                                    Hyprland.dispatch('global quickshell:sidebarLeftToggle')\n                                }\n\n                                CustomIcon {\n                                    id: distroIcon\n                                    anchors.centerIn: parent\n                                    width: 19.5\n                                    height: 19.5\n                                    source: ConfigOptions.bar.topLeftIcon == 'distro' ? \n                                        SystemInfo.distroIcon : \"spark-symbolic\"\n                                }\n                                \n                                ColorOverlay {\n                                    anchors.fill: distroIcon\n                                    source: distroIcon\n                                    color: Appearance.colors.colOnLayer0\n                                }\n                            }\n\n                            ActiveWindow {\n                                visible: barRoot.useShortenedForm === 0\n                                Layout.rightMargin: Appearance.rounding.screenRounding\n                                Layout.fillWidth: true\n                                Layout.fillHeight: true\n                                bar: barRoot\n                            }\n                        }\n                    }\n                }\n\n                RowLayout { // Middle section\n                    id: middleSection\n                    anchors.centerIn: parent\n                    spacing: ConfigOptions?.bar.borderless ? 4 : 8\n\n                    BarGroup {\n                        id: leftCenterGroup\n                        Layout.preferredWidth: barRoot.centerSideModuleWidth\n                        Layout.fillHeight: true\n\n                        Resources {\n                            alwaysShowAllResources: barRoot.useShortenedForm === 2\n                            Layout.fillWidth: barRoot.useShortenedForm === 2\n                        }\n\n                        Media {\n                            visible: barRoot.useShortenedForm < 2\n                            Layout.fillWidth: true\n                        }\n\n                    }\n\n                    VerticalBarSeparator {visible: ConfigOptions?.bar.borderless}\n\n                    BarGroup {\n                        id: middleCenterGroup\n                        padding: workspacesWidget.widgetPadding\n                        Layout.fillHeight: true\n                        \n                        Workspaces {\n                            id: workspacesWidget\n                            bar: barRoot\n                            Layout.fillHeight: true\n                            MouseArea { // Right-click to toggle overview\n                                anchors.fill: parent\n                                acceptedButtons: Qt.RightButton\n                                \n                                onPressed: (event) => {\n                                    if (event.button === Qt.RightButton) {\n                                        Hyprland.dispatch('global quickshell:overviewToggle')\n                                    }\n                                }\n                            }\n                        }\n                    }\n\n                    VerticalBarSeparator {visible: ConfigOptions?.bar.borderless}\n\n                    MouseArea {\n                        id: rightCenterGroup\n                        implicitWidth: rightCenterGroupContent.implicitWidth\n                        implicitHeight: rightCenterGroupContent.implicitHeight\n                        Layout.preferredWidth: barRoot.centerSideModuleWidth\n                        Layout.fillHeight: true\n\n                        onPressed: {\n                            Hyprland.dispatch('global quickshell:sidebarRightToggle')\n                        }\n\n                        BarGroup {\n                            id: rightCenterGroupContent\n                            anchors.fill: parent\n                            \n                            ClockWidget {\n                                showDate: (ConfigOptions.bar.verbose && barRoot.useShortenedForm < 2)\n                                Layout.alignment: Qt.AlignVCenter\n                                Layout.fillWidth: true\n                            }\n\n                            UtilButtons {\n                                visible: (ConfigOptions.bar.verbose && barRoot.useShortenedForm === 0)\n                                Layout.alignment: Qt.AlignVCenter\n                            }\n\n                            BatteryIndicator {\n                                visible: (barRoot.useShortenedForm < 2 && UPower.displayDevice.isLaptopBattery)\n                                Layout.alignment: Qt.AlignVCenter\n                            }\n                        }\n                    }\n\n                }\n\n                MouseArea { // Right side | scroll to change volume\n                    id: barRightSideMouseArea\n\n                    anchors.right: parent.right\n                    implicitHeight: barHeight\n                    width: (barRoot.width - middleSection.width) / 2\n\n                    property bool hovered: false\n                    property real lastScrollX: 0\n                    property real lastScrollY: 0\n                    property bool trackingScroll: false\n                    \n                    acceptedButtons: Qt.LeftButton\n                    hoverEnabled: true\n                    propagateComposedEvents: true\n                    onEntered: (event) => {\n                        barRightSideMouseArea.hovered = true\n                    }\n                    onExited: (event) => {\n                        barRightSideMouseArea.hovered = false\n                        barRightSideMouseArea.trackingScroll = false\n                    }\n                    onPressed: (event) => {\n                        if (event.button === Qt.LeftButton) {\n                            Hyprland.dispatch('global quickshell:sidebarRightOpen')\n                        }\n                        else if (event.button === Qt.RightButton) {\n                            MprisController.activePlayer.next()\n                        }\n                    }\n                    // Scroll to change volume\n                    WheelHandler {\n                        onWheel: (event) => {\n                            const currentVolume = Audio.value;\n                            const step = currentVolume < 0.1 ? 0.01 : 0.02 || 0.2;\n                            if (event.angleDelta.y < 0)\n                                Audio.sink.audio.volume -= step;\n                            else if (event.angleDelta.y > 0)\n                                Audio.sink.audio.volume = Math.min(1, Audio.sink.audio.volume + step);\n                            // Store the mouse position and start tracking\n                            barRightSideMouseArea.lastScrollX = event.x;\n                            barRightSideMouseArea.lastScrollY = event.y;\n                            barRightSideMouseArea.trackingScroll = true;\n                        }\n                        acceptedDevices: PointerDevice.Mouse | PointerDevice.TouchPad\n                    }\n                    onPositionChanged: (mouse) => {\n                        if (barRightSideMouseArea.trackingScroll) {\n                            const dx = mouse.x - barRightSideMouseArea.lastScrollX;\n                            const dy = mouse.y - barRightSideMouseArea.lastScrollY;\n                            if (Math.sqrt(dx*dx + dy*dy) > osdHideMouseMoveThreshold) {\n                                Hyprland.dispatch('global quickshell:osdVolumeHide')\n                                barRightSideMouseArea.trackingScroll = false;\n                            }\n                        }\n                    }\n\n                    Item {\n                        anchors.fill: parent\n                        implicitHeight: rightSectionRowLayout.implicitHeight\n                        implicitWidth: rightSectionRowLayout.implicitWidth\n                        \n                        ScrollHint {\n                            reveal: barRightSideMouseArea.hovered\n                            icon: \"volume_up\"\n                            tooltipText: qsTr(\"滑动以更改音量\")\n                            side: \"right\"\n                            anchors.right: parent.right\n                            anchors.verticalCenter: parent.verticalCenter\n                        }\n\n                        RowLayout {\n                            id: rightSectionRowLayout\n                            anchors.fill: parent\n                            spacing: 5\n                            layoutDirection: Qt.RightToLeft\n                    \n                            RippleButton { // Right sidebar button\n                                id: rightSidebarButton\n                                Layout.margins: 4\n                                Layout.rightMargin: Appearance.rounding.screenRounding\n                                Layout.fillHeight: true\n                                implicitWidth: indicatorsRowLayout.implicitWidth + 10*2\n                                buttonRadius: Appearance.rounding.full\n                                colBackground: barRightSideMouseArea.hovered ? Appearance.colors.colLayer1Hover : ColorUtils.transparentize(Appearance.colors.colLayer1Hover, 1)\n                                colBackgroundHover: Appearance.colors.colLayer1Hover\n                                colRipple: Appearance.colors.colLayer1Active\n                                colBackgroundToggled: Appearance.colors.colSecondaryContainer\n                                colBackgroundToggledHover: Appearance.colors.colSecondaryContainerHover\n                                colRippleToggled: Appearance.colors.colSecondaryContainerActive\n                                toggled: GlobalStates.sidebarRightOpen\n                                property color colText: toggled ? Appearance.m3colors.m3onSecondaryContainer : Appearance.colors.colOnLayer0\n\n                                Behavior on colText {\n                                    animation: Appearance.animation.elementMoveFast.colorAnimation.createObject(this)\n                                }\n\n                                onPressed: {\n                                    Hyprland.dispatch('global quickshell:sidebarRightToggle')\n                                }\n\n                                RowLayout {\n                                    id: indicatorsRowLayout\n                                    anchors.centerIn: parent\n                                    property real realSpacing: 15\n                                    spacing: 0\n                                    \n                                    Revealer {\n                                        reveal: Audio.sink?.audio?.muted ?? false\n                                        Layout.fillHeight: true\n                                        Layout.rightMargin: reveal ? indicatorsRowLayout.realSpacing : 0\n                                        Behavior on Layout.rightMargin {\n                                            NumberAnimation {\n                                                duration: Appearance.animation.elementMoveFast.duration\n                                                easing.type: Appearance.animation.elementMoveFast.type\n                                                easing.bezierCurve: Appearance.animation.elementMoveFast.bezierCurve\n                                            }\n                                        }\n                                        MaterialSymbol {\n                                            text: \"静音\"\n                                            iconSize: Appearance.font.pixelSize.larger\n                                            color: rightSidebarButton.colText\n                                        }\n                                    }\n                                    Revealer {\n                                        reveal: Audio.source?.audio?.muted ?? false\n                                        Layout.fillHeight: true\n                                        Layout.rightMargin: reveal ? indicatorsRowLayout.realSpacing : 0\n                                        Behavior on Layout.rightMargin {\n                                            NumberAnimation {\n                                                duration: Appearance.animation.elementMoveFast.duration\n                                                easing.type: Appearance.animation.elementMoveFast.type\n                                                easing.bezierCurve: Appearance.animation.elementMoveFast.bezierCurve\n                                            }\n                                        }\n                                        MaterialSymbol {\n                                            text: \"麦克风关闭\"\n                                            iconSize: Appearance.font.pixelSize.larger\n                                            color: rightSidebarButton.colText\n                                        }\n                                    }\n                                    MaterialSymbol {\n                                        Layout.rightMargin: indicatorsRowLayout.realSpacing\n                                        text: Network.materialSymbol\n                                        iconSize: Appearance.font.pixelSize.larger\n                                        color: rightSidebarButton.colText\n                                    }\n                                    MaterialSymbol {\n                                        text: Bluetooth.bluetoothConnected ? \"bluetooth_connected\" : Bluetooth.bluetoothEnabled ? \"bluetooth\" : \"bluetooth_disabled\"\n                                        iconSize: Appearance.font.pixelSize.larger\n                                        color: rightSidebarButton.colText\n                                    }\n                                }\n                            }\n\n                            SysTray {\n                                bar: barRoot\n                                visible: barRoot.useShortenedForm === 0\n                                Layout.fillWidth: false\n                                Layout.fillHeight: true\n                            }\n\n                            Item {\n                                Layout.fillWidth: true\n                                Layout.fillHeight: true\n                            }\n                        }\n                    }\n                }\n            }\n\n            // Round decorators\n            Item {\n                anchors {\n                    left: parent.left\n                    right: parent.right\n                    // top: barContent.bottom\n                    top: ConfigOptions.bar.bottom ? undefined : barContent.bottom\n                    bottom: ConfigOptions.bar.bottom ? barContent.top : undefined\n                }\n                height: Appearance.rounding.screenRounding\n                visible: showBarBackground\n\n                RoundCorner {\n                    anchors.top: parent.top\n                    anchors.left: parent.left\n                    size: Appearance.rounding.screenRounding\n                    corner: ConfigOptions.bar.bottom ? cornerEnum.bottomLeft : cornerEnum.topLeft\n                    color: showBarBackground ? Appearance.colors.colLayer0 : \"transparent\"\n                    opacity: 1.0 - Appearance.transparency\n                }\n                RoundCorner {\n                    anchors.top: parent.top\n                    anchors.right: parent.right\n                    size: Appearance.rounding.screenRounding\n                    corner: ConfigOptions.bar.bottom ? cornerEnum.bottomRight : cornerEnum.topRight\n                    color: showBarBackground ? Appearance.colors.colLayer0 : \"transparent\"\n                    opacity: 1.0 - Appearance.transparency\n                }\n            }\n\n        }\n\n    }\n\n}\n", "modifiedCode": "import \"root:/\"\nimport \"root:/services\"\nimport \"root:/modules/common/\"\nimport \"root:/modules/common/widgets\"\nimport \"root:/modules/common/functions/color_utils.js\" as ColorUtils\nimport QtQuick\nimport QtQuick.Controls\nimport QtQuick.Layouts\nimport Qt5Compat.GraphicalEffects\nimport Quickshell\nimport Quickshell.Wayland\nimport Quickshell.Hyprland\nimport Quickshell.Services.UPower\n\nScope {\n    id: bar\n\n    readonly property int barHeight: Appearance.sizes.barHeight\n    readonly property int osdHideMouseMoveThreshold: 20\n    property bool showBarBackground: ConfigOptions.bar.showBackground\n\n    component VerticalBarSeparator: Rectangle {\n        Layout.topMargin: barHeight / 3\n        Layout.bottomMargin: barHeight / 3\n        Layout.fillHeight: true\n        implicitWidth: 1\n        color: Appearance.colors.colOutlineVariant\n    }\n\n    Variants { // For each monitor\n        model: {\n            const screens = Quickshell.screens;\n            const list = ConfigOptions.bar.screenList;\n            if (!list || list.length === 0)\n                return screens;\n            return screens.filter(screen => list.includes(screen.name));\n        }\n\n        PanelWindow { // Bar window\n            id: barRoot\n            screen: modelData\n\n            property ShellScreen modelData\n            property var brightnessMonitor: Brightness.getMonitorForScreen(modelData)\n            property real useShortenedForm: (Appearance.sizes.barHellaShortenScreenWidthThreshold >= screen.width) ? 2 :\n                (Appearance.sizes.barShortenScreenWidthThreshold >= screen.width) ? 1 : 0\n            readonly property int centerSideModuleWidth: \n                (useShortenedForm == 2) ? Appearance.sizes.barCenterSideModuleWidthHellaShortened :\n                (useShortenedForm == 1) ? Appearance.sizes.barCenterSideModuleWidthShortened : \n                    Appearance.sizes.barCenterSideModuleWidth\n\n            WlrLayershell.namespace: \"quickshell:bar\"\n            implicitHeight: barHeight + Appearance.rounding.screenRounding\n            exclusiveZone: showBarBackground ? barHeight : (barHeight - 4)\n            mask: Region {\n                item: barContent\n            }\n            color: \"transparent\"\n\n            anchors {\n                top: !ConfigOptions.bar.bottom\n                bottom: ConfigOptions.bar.bottom\n                left: true\n                right: true\n            }\n\n            Rectangle { // Bar background\n                id: barContent\n                anchors {\n                    right: parent.right\n                    left: parent.left\n                    top: !ConfigOptions.bar.bottom ? parent.top : undefined\n                    bottom: ConfigOptions.bar.bottom ? parent.bottom : undefined\n                }\n                color: showBarBackground ? Appearance.colors.colLayer0 : \"transparent\"\n                height: barHeight\n                \n                MouseArea { // Left side | scroll to change brightness\n                    id: barLeftSideMouseArea\n                    anchors.left: parent.left\n                    implicitHeight: barHeight\n                    width: (barRoot.width - middleSection.width) / 2\n                    property bool hovered: false\n                    property real lastScrollX: 0\n                    property real lastScrollY: 0\n                    property bool trackingScroll: false\n                    acceptedButtons: Qt.LeftButton\n                    hoverEnabled: true\n                    propagateComposedEvents: true\n                    onEntered: (event) => {\n                        barLeftSideMouseArea.hovered = true\n                    }\n                    onExited: (event) => {\n                        barLeftSideMouseArea.hovered = false\n                        barLeftSideMouseArea.trackingScroll = false\n                    }\n                    onPressed: (event) => {\n                        if (event.button === Qt.LeftButton) {\n                            Hyprland.dispatch('global quickshell:sidebarLeftOpen')\n                        }\n                    }\n                    // Scroll to change brightness\n                    WheelHandler {\n                        onWheel: (event) => {\n                            if (event.angleDelta.y < 0)\n                                barRoot.brightnessMonitor.setBrightness(barRoot.brightnessMonitor.brightness - 0.05);\n                            else if (event.angleDelta.y > 0)\n                                barRoot.brightnessMonitor.setBrightness(barRoot.brightnessMonitor.brightness + 0.05);\n                            // Store the mouse position and start tracking\n                            barLeftSideMouseArea.lastScrollX = event.x;\n                            barLeftSideMouseArea.lastScrollY = event.y;\n                            barLeftSideMouseArea.trackingScroll = true;\n                        }\n                        acceptedDevices: PointerDevice.Mouse | PointerDevice.TouchPad\n                    }\n                    onPositionChanged: (mouse) => {\n                        if (barLeftSideMouseArea.trackingScroll) {\n                            const dx = mouse.x - barLeftSideMouseArea.lastScrollX;\n                            const dy = mouse.y - barLeftSideMouseArea.lastScrollY;\n                            if (Math.sqrt(dx*dx + dy*dy) > osdHideMouseMoveThreshold) {\n                                Hyprland.dispatch('global quickshell:osdBrightnessHide')\n                                barLeftSideMouseArea.trackingScroll = false;\n                            }\n                        }\n                    }\n                    Item {  // Left section\n                        anchors.fill: parent\n                        implicitHeight: leftSectionRowLayout.implicitHeight\n                        implicitWidth: leftSectionRowLayout.implicitWidth\n\n                        ScrollHint {\n                            reveal: barLeftSideMouseArea.hovered\n                            icon: \"light_mode\"\n                            tooltipText: qsTr(\"Scroll to change brightness\")\n                            side: \"left\"\n                            anchors.left: parent.left\n                            anchors.verticalCenter: parent.verticalCenter\n                            \n                        }\n                        \n                        RowLayout { // Content\n                            id: leftSectionRowLayout\n                            anchors.fill: parent\n                            spacing: 10\n\n                            RippleButton { // Left sidebar button\n                                Layout.alignment: Qt.AlignLeft | Qt.AlignVCenter\n                                Layout.leftMargin: Appearance.rounding.screenRounding\n                                Layout.fillWidth: false\n                                property real buttonPadding: 5\n                                implicitWidth: distroIcon.width + buttonPadding * 2\n                                implicitHeight: distroIcon.height + buttonPadding * 2\n                                \n                                buttonRadius: Appearance.rounding.full\n                                colBackground: barLeftSideMouseArea.hovered ? Appearance.colors.colLayer1Hover : ColorUtils.transparentize(Appearance.colors.colLayer1Hover, 1)\n                                colBackgroundHover: Appearance.colors.colLayer1Hover\n                                colRipple: Appearance.colors.colLayer1Active\n                                colBackgroundToggled: Appearance.colors.colSecondaryContainer\n                                colBackgroundToggledHover: Appearance.colors.colSecondaryContainerHover\n                                colRippleToggled: Appearance.colors.colSecondaryContainerActive\n                                toggled: GlobalStates.sidebarLeftOpen\n                                    property color colText: toggled ? Appearance.m3colors.m3onSecondaryContainer : Appearance.colors.colOnLayer0\n\n                                onPressed: {\n                                    Hyprland.dispatch('global quickshell:sidebarLeftToggle')\n                                }\n\n                                CustomIcon {\n                                    id: distroIcon\n                                    anchors.centerIn: parent\n                                    width: 19.5\n                                    height: 19.5\n                                    source: ConfigOptions.bar.topLeftIcon == 'distro' ? \n                                        SystemInfo.distroIcon : \"spark-symbolic\"\n                                }\n                                \n                                ColorOverlay {\n                                    anchors.fill: distroIcon\n                                    source: distroIcon\n                                    color: Appearance.colors.colOnLayer0\n                                }\n                            }\n\n                            ActiveWindow {\n                                visible: barRoot.useShortenedForm === 0\n                                Layout.rightMargin: Appearance.rounding.screenRounding\n                                Layout.fillWidth: true\n                                Layout.fillHeight: true\n                                bar: barRoot\n                            }\n                        }\n                    }\n                }\n\n                RowLayout { // Middle section\n                    id: middleSection\n                    anchors.centerIn: parent\n                    spacing: ConfigOptions?.bar.borderless ? 4 : 8\n\n                    BarGroup {\n                        id: leftCenterGroup\n                        Layout.preferredWidth: barRoot.centerSideModuleWidth\n                        Layout.fillHeight: true\n\n                        Resources {\n                            alwaysShowAllResources: barRoot.useShortenedForm === 2\n                            Layout.fillWidth: barRoot.useShortenedForm === 2\n                        }\n\n                        Media {\n                            visible: barRoot.useShortenedForm < 2\n                            Layout.fillWidth: true\n                        }\n\n                    }\n\n                    VerticalBarSeparator {visible: ConfigOptions?.bar.borderless}\n\n                    BarGroup {\n                        id: middleCenterGroup\n                        padding: workspacesWidget.widgetPadding\n                        Layout.fillHeight: true\n                        \n                        Workspaces {\n                            id: workspacesWidget\n                            bar: barRoot\n                            Layout.fillHeight: true\n                            MouseArea { // Right-click to toggle overview\n                                anchors.fill: parent\n                                acceptedButtons: Qt.RightButton\n                                \n                                onPressed: (event) => {\n                                    if (event.button === Qt.RightButton) {\n                                        Hyprland.dispatch('global quickshell:overviewToggle')\n                                    }\n                                }\n                            }\n                        }\n                    }\n\n                    VerticalBarSeparator {visible: ConfigOptions?.bar.borderless}\n\n                    MouseArea {\n                        id: rightCenterGroup\n                        implicitWidth: rightCenterGroupContent.implicitWidth\n                        implicitHeight: rightCenterGroupContent.implicitHeight\n                        Layout.preferredWidth: barRoot.centerSideModuleWidth\n                        Layout.fillHeight: true\n\n                        onPressed: {\n                            Hyprland.dispatch('global quickshell:sidebarRightToggle')\n                        }\n\n                        BarGroup {\n                            id: rightCenterGroupContent\n                            anchors.fill: parent\n                            \n                            ClockWidget {\n                                showDate: (ConfigOptions.bar.verbose && barRoot.useShortenedForm < 2)\n                                Layout.alignment: Qt.AlignVCenter\n                                Layout.fillWidth: true\n                            }\n\n                            UtilButtons {\n                                visible: (ConfigOptions.bar.verbose && barRoot.useShortenedForm === 0)\n                                Layout.alignment: Qt.AlignVCenter\n                            }\n\n                            BatteryIndicator {\n                                visible: (barRoot.useShortenedForm < 2 && UPower.displayDevice.isLaptopBattery)\n                                Layout.alignment: Qt.AlignVCenter\n                            }\n                        }\n                    }\n\n                }\n\n                MouseArea { // Right side | scroll to change volume\n                    id: barRightSideMouseArea\n\n                    anchors.right: parent.right\n                    implicitHeight: barHeight\n                    width: (barRoot.width - middleSection.width) / 2\n\n                    property bool hovered: false\n                    property real lastScrollX: 0\n                    property real lastScrollY: 0\n                    property bool trackingScroll: false\n                    \n                    acceptedButtons: Qt.LeftButton\n                    hoverEnabled: true\n                    propagateComposedEvents: true\n                    onEntered: (event) => {\n                        barRightSideMouseArea.hovered = true\n                    }\n                    onExited: (event) => {\n                        barRightSideMouseArea.hovered = false\n                        barRightSideMouseArea.trackingScroll = false\n                    }\n                    onPressed: (event) => {\n                        if (event.button === Qt.LeftButton) {\n                            Hyprland.dispatch('global quickshell:sidebarRightOpen')\n                        }\n                        else if (event.button === Qt.RightButton) {\n                            MprisController.activePlayer.next()\n                        }\n                    }\n                    // Scroll to change volume\n                    WheelHandler {\n                        onWheel: (event) => {\n                            const currentVolume = Audio.value;\n                            const step = currentVolume < 0.1 ? 0.01 : 0.02 || 0.2;\n                            if (event.angleDelta.y < 0)\n                                Audio.sink.audio.volume -= step;\n                            else if (event.angleDelta.y > 0)\n                                Audio.sink.audio.volume = Math.min(1, Audio.sink.audio.volume + step);\n                            // Store the mouse position and start tracking\n                            barRightSideMouseArea.lastScrollX = event.x;\n                            barRightSideMouseArea.lastScrollY = event.y;\n                            barRightSideMouseArea.trackingScroll = true;\n                        }\n                        acceptedDevices: PointerDevice.Mouse | PointerDevice.TouchPad\n                    }\n                    onPositionChanged: (mouse) => {\n                        if (barRightSideMouseArea.trackingScroll) {\n                            const dx = mouse.x - barRightSideMouseArea.lastScrollX;\n                            const dy = mouse.y - barRightSideMouseArea.lastScrollY;\n                            if (Math.sqrt(dx*dx + dy*dy) > osdHideMouseMoveThreshold) {\n                                Hyprland.dispatch('global quickshell:osdVolumeHide')\n                                barRightSideMouseArea.trackingScroll = false;\n                            }\n                        }\n                    }\n\n                    Item {\n                        anchors.fill: parent\n                        implicitHeight: rightSectionRowLayout.implicitHeight\n                        implicitWidth: rightSectionRowLayout.implicitWidth\n                        \n                        ScrollHint {\n                            reveal: barRightSideMouseArea.hovered\n                            icon: \"volume_up\"\n                            tooltipText: qsTr(\"Scroll to change volume\")\n                            side: \"right\"\n                            anchors.right: parent.right\n                            anchors.verticalCenter: parent.verticalCenter\n                        }\n\n                        RowLayout {\n                            id: rightSectionRowLayout\n                            anchors.fill: parent\n                            spacing: 5\n                            layoutDirection: Qt.RightToLeft\n                    \n                            RippleButton { // Right sidebar button\n                                id: rightSidebarButton\n                                Layout.margins: 4\n                                Layout.rightMargin: Appearance.rounding.screenRounding\n                                Layout.fillHeight: true\n                                implicitWidth: indicatorsRowLayout.implicitWidth + 10*2\n                                buttonRadius: Appearance.rounding.full\n                                colBackground: barRightSideMouseArea.hovered ? Appearance.colors.colLayer1Hover : ColorUtils.transparentize(Appearance.colors.colLayer1Hover, 1)\n                                colBackgroundHover: Appearance.colors.colLayer1Hover\n                                colRipple: Appearance.colors.colLayer1Active\n                                colBackgroundToggled: Appearance.colors.colSecondaryContainer\n                                colBackgroundToggledHover: Appearance.colors.colSecondaryContainerHover\n                                colRippleToggled: Appearance.colors.colSecondaryContainerActive\n                                toggled: GlobalStates.sidebarRightOpen\n                                property color colText: toggled ? Appearance.m3colors.m3onSecondaryContainer : Appearance.colors.colOnLayer0\n\n                                Behavior on colText {\n                                    animation: Appearance.animation.elementMoveFast.colorAnimation.createObject(this)\n                                }\n\n                                onPressed: {\n                                    Hyprland.dispatch('global quickshell:sidebarRightToggle')\n                                }\n\n                                RowLayout {\n                                    id: indicatorsRowLayout\n                                    anchors.centerIn: parent\n                                    property real realSpacing: 15\n                                    spacing: 0\n                                    \n                                    Revealer {\n                                        reveal: Audio.sink?.audio?.muted ?? false\n                                        Layout.fillHeight: true\n                                        Layout.rightMargin: reveal ? indicatorsRowLayout.realSpacing : 0\n                                        Behavior on Layout.rightMargin {\n                                            NumberAnimation {\n                                                duration: Appearance.animation.elementMoveFast.duration\n                                                easing.type: Appearance.animation.elementMoveFast.type\n                                                easing.bezierCurve: Appearance.animation.elementMoveFast.bezierCurve\n                                            }\n                                        }\n                                        MaterialSymbol {\n                                            text: \"volume_off\"\n                                            iconSize: Appearance.font.pixelSize.larger\n                                            color: rightSidebarButton.colText\n                                        }\n                                    }\n                                    Revealer {\n                                        reveal: Audio.source?.audio?.muted ?? false\n                                        Layout.fillHeight: true\n                                        Layout.rightMargin: reveal ? indicatorsRowLayout.realSpacing : 0\n                                        Behavior on Layout.rightMargin {\n                                            NumberAnimation {\n                                                duration: Appearance.animation.elementMoveFast.duration\n                                                easing.type: Appearance.animation.elementMoveFast.type\n                                                easing.bezierCurve: Appearance.animation.elementMoveFast.bezierCurve\n                                            }\n                                        }\n                                        MaterialSymbol {\n                                            text: \"mic_off\"\n                                            iconSize: Appearance.font.pixelSize.larger\n                                            color: rightSidebarButton.colText\n                                        }\n                                    }\n                                    MaterialSymbol {\n                                        Layout.rightMargin: indicatorsRowLayout.realSpacing\n                                        text: Network.materialSymbol\n                                        iconSize: Appearance.font.pixelSize.larger\n                                        color: rightSidebarButton.colText\n                                    }\n                                    MaterialSymbol {\n                                        text: Bluetooth.bluetoothConnected ? \"bluetooth_connected\" : Bluetooth.bluetoothEnabled ? \"bluetooth\" : \"bluetooth_disabled\"\n                                        iconSize: Appearance.font.pixelSize.larger\n                                        color: rightSidebarButton.colText\n                                    }\n                                }\n                            }\n\n                            SysTray {\n                                bar: barRoot\n                                visible: barRoot.useShortenedForm === 0\n                                Layout.fillWidth: false\n                                Layout.fillHeight: true\n                            }\n\n                            Item {\n                                Layout.fillWidth: true\n                                Layout.fillHeight: true\n                            }\n                        }\n                    }\n                }\n            }\n\n            // Round decorators\n            Item {\n                anchors {\n                    left: parent.left\n                    right: parent.right\n                    // top: barContent.bottom\n                    top: ConfigOptions.bar.bottom ? undefined : barContent.bottom\n                    bottom: ConfigOptions.bar.bottom ? barContent.top : undefined\n                }\n                height: Appearance.rounding.screenRounding\n                visible: showBarBackground\n\n                RoundCorner {\n                    anchors.top: parent.top\n                    anchors.left: parent.left\n                    size: Appearance.rounding.screenRounding\n                    corner: ConfigOptions.bar.bottom ? cornerEnum.bottomLeft : cornerEnum.topLeft\n                    color: showBarBackground ? Appearance.colors.colLayer0 : \"transparent\"\n                    opacity: 1.0 - Appearance.transparency\n                }\n                RoundCorner {\n                    anchors.top: parent.top\n                    anchors.right: parent.right\n                    size: Appearance.rounding.screenRounding\n                    corner: ConfigOptions.bar.bottom ? cornerEnum.bottomRight : cornerEnum.topRight\n                    color: showBarBackground ? Appearance.colors.colLayer0 : \"transparent\"\n                    opacity: 1.0 - Appearance.transparency\n                }\n            }\n\n        }\n\n    }\n\n}\n"}