{"path": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/onScreenDisplay/OnScreenDisplayVolume.qml"}, "originalCode": "import \"root:/services/\"\nimport \"root:/modules/common\"\nimport \"root:/modules/common/widgets\"\nimport QtQuick\nimport QtQuick.Controls\nimport QtQuick.Layouts\nimport Quickshell\nimport Quickshell.Io\nimport Quickshell.Wayland\nimport Quickshell.Hyprland\n\nScope {\n    id: root\n    property bool showOsdValues: false\n    property string protectionMessage: \"\"\n    property var focusedScreen: Quickshell.screens.find(s => s.name === Hyprland.focusedMonitor?.name)\n\n    function triggerOsd() {\n        showOsdValues = true\n        osdTimeout.restart()\n    }\n\n    Timer {\n        id: osdTimeout\n        interval: ConfigOptions.osd.timeout\n        repeat: false\n        running: false\n        onTriggered: {\n            root.showOsdValues = false\n            root.protectionMessage = \"\"\n        }\n    }\n\n    Connections {\n        target: Brightness\n        function onBrightnessChanged() {\n            showOsdValues = false\n        }\n    }\n\n    Connections { // Listen to volume changes\n        target: Audio.sink?.audio ?? null\n        function onVolumeChanged() {\n            if (!Audio.ready) return\n            root.triggerOsd()\n        }\n        function onMutedChanged() {\n            if (!Audio.ready) return\n            root.triggerOsd()\n        }\n    }\n\n    Connections { // Listen to protection triggers\n        target: Audio\n        function onSinkProtectionTriggered(reason) {\n            root.protectionMessage = reason;\n            root.triggerOsd()\n        }\n    }\n\n    Loader {\n        id: osdLoader\n        active: showOsdValues\n\n        sourceComponent: PanelWindow {\n            id: osdRoot\n\n            Connections {\n                target: root\n                function onFocusedScreenChanged() {\n                    osdRoot.screen = root.focusedScreen\n                }\n            }\n\n            exclusionMode: ExclusionMode.Normal\n            WlrLayershell.namespace: \"quickshell:onScreenDisplay\"\n            WlrLayershell.layer: WlrLayer.Overlay\n            color: \"transparent\"\n\n            anchors {\n                top: !ConfigOptions.bar.bottom\n                bottom: ConfigOptions.bar.bottom\n            }\n            mask: Region {\n                item: osdValuesWrapper\n            }\n\n            implicitWidth: columnLayout.implicitWidth\n            implicitHeight: columnLayout.implicitHeight\n            visible: osdLoader.active\n\n            ColumnLayout {\n                id: columnLayout\n                anchors.horizontalCenter: parent.horizontalCenter\n                Item {\n                    id: osdValuesWrapper\n                    // Extra space for shadow\n                    implicitHeight: contentColumnLayout.implicitHeight + Appearance.sizes.elevationMargin * 2\n                    implicitWidth: contentColumnLayout.implicitWidth\n                    clip: true\n\n                    MouseArea {\n                        anchors.fill: parent\n                        hoverEnabled: true\n                        onEntered: root.showOsdValues = false\n                    }\n\n                    ColumnLayout {\n                        id: contentColumnLayout\n                        anchors {\n                            top: parent.top\n                            left: parent.left\n                            right: parent.right\n                            leftMargin: Appearance.sizes.elevationMargin\n                            rightMargin: Appearance.sizes.elevationMargin\n                        }\n                        spacing: 0\n\n                        OsdValueIndicator {\n                            id: osdValues\n                            Layout.fillWidth: true\n                            value: Audio.sink?.audio.volume ?? 0\n                            icon: Audio.sink?.audio.muted ? \"volume_off\" : \"volume_up\"\n                            name: qsTr(\"音量\")\n                        }\n\n                        Item {\n                            id: protectionMessageWrapper\n                            implicitHeight: protectionMessageBackground.implicitHeight\n                            implicitWidth: protectionMessageBackground.implicitWidth\n                            Layout.alignment: Qt.AlignHCenter\n                            opacity: root.protectionMessage !== \"\" ? 1 : 0\n\n                            StyledRectangularShadow {\n                                target: protectionMessageBackground\n                            }\n                            Rectangle {\n                                id: protectionMessageBackground\n                                anchors.centerIn: parent\n                                color: Appearance.m3colors.m3error\n                                property real padding: 10\n                                implicitHeight: protectionMessageRowLayout.implicitHeight + padding * 2\n                                implicitWidth: protectionMessageRowLayout.implicitWidth + padding * 2\n                                radius: Appearance.rounding.normal\n\n                                RowLayout {\n                                    id: protectionMessageRowLayout\n                                    anchors.centerIn: parent\n                                    MaterialSymbol {\n                                        id: protectionMessageIcon\n                                        text: \"warning\"\n                                        iconSize: Appearance.font.pixelSize.hugeass\n                                        color: Appearance.m3colors.m3onError\n                                    }\n                                    StyledText {\n                                        id: protectionMessageTextWidget\n                                        horizontalAlignment: Text.AlignHCenter\n                                        color: Appearance.m3colors.m3onError\n                                        wrapMode: Text.Wrap\n                                        text: root.protectionMessage\n                                    }\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    IpcHandler {\n\t\ttarget: \"osdVolume\"\n\n\t\tfunction trigger() {\n            root.triggerOsd()\n        }\n\n        function hide() {\n            showOsdValues = false\n        }\n\n        function toggle() {\n            showOsdValues = !showOsdValues\n        }\n\t}\n    GlobalShortcut {\n        name: \"osdVolumeTrigger\"\n        description: qsTr(\"按下时触发音量OSD\")\n\n        onPressed: {\n            root.triggerOsd()\n        }\n    }\n    GlobalShortcut {\n        name: \"osdVolumeHide\"\n        description: qsTr(\"按下时隐藏音量OSD\")\n\n        onPressed: {\n            root.showOsdValues = false\n        }\n    }\n\n}\n", "modifiedCode": "import \"root:/services/\"\nimport \"root:/modules/common\"\nimport \"root:/modules/common/widgets\"\nimport QtQuick\nimport QtQuick.Controls\nimport QtQuick.Layouts\nimport Quickshell\nimport Quickshell.Io\nimport Quickshell.Wayland\nimport Quickshell.Hyprland\n\nScope {\n    id: root\n    property bool showOsdValues: false\n    property string protectionMessage: \"\"\n    property var focusedScreen: Quickshell.screens.find(s => s.name === Hyprland.focusedMonitor?.name)\n\n    function triggerOsd() {\n        showOsdValues = true\n        osdTimeout.restart()\n    }\n\n    Timer {\n        id: osdTimeout\n        interval: ConfigOptions.osd.timeout\n        repeat: false\n        running: false\n        onTriggered: {\n            root.showOsdValues = false\n            root.protectionMessage = \"\"\n        }\n    }\n\n    Connections {\n        target: Brightness\n        function onBrightnessChanged() {\n            showOsdValues = false\n        }\n    }\n\n    Connections { // Listen to volume changes\n        target: Audio.sink?.audio ?? null\n        function onVolumeChanged() {\n            if (!Audio.ready) return\n            root.triggerOsd()\n        }\n        function onMutedChanged() {\n            if (!Audio.ready) return\n            root.triggerOsd()\n        }\n    }\n\n    Connections { // Listen to protection triggers\n        target: Audio\n        function onSinkProtectionTriggered(reason) {\n            root.protectionMessage = reason;\n            root.triggerOsd()\n        }\n    }\n\n    Loader {\n        id: osdLoader\n        active: showOsdValues\n\n        sourceComponent: PanelWindow {\n            id: osdRoot\n\n            Connections {\n                target: root\n                function onFocusedScreenChanged() {\n                    osdRoot.screen = root.focusedScreen\n                }\n            }\n\n            exclusionMode: ExclusionMode.Normal\n            WlrLayershell.namespace: \"quickshell:onScreenDisplay\"\n            WlrLayershell.layer: WlrLayer.Overlay\n            color: \"transparent\"\n\n            anchors {\n                top: !ConfigOptions.bar.bottom\n                bottom: ConfigOptions.bar.bottom\n            }\n            mask: Region {\n                item: osdValuesWrapper\n            }\n\n            implicitWidth: columnLayout.implicitWidth\n            implicitHeight: columnLayout.implicitHeight\n            visible: osdLoader.active\n\n            ColumnLayout {\n                id: columnLayout\n                anchors.horizontalCenter: parent.horizontalCenter\n                Item {\n                    id: osdValuesWrapper\n                    // Extra space for shadow\n                    implicitHeight: contentColumnLayout.implicitHeight + Appearance.sizes.elevationMargin * 2\n                    implicitWidth: contentColumnLayout.implicitWidth\n                    clip: true\n\n                    MouseArea {\n                        anchors.fill: parent\n                        hoverEnabled: true\n                        onEntered: root.showOsdValues = false\n                    }\n\n                    ColumnLayout {\n                        id: contentColumnLayout\n                        anchors {\n                            top: parent.top\n                            left: parent.left\n                            right: parent.right\n                            leftMargin: Appearance.sizes.elevationMargin\n                            rightMargin: Appearance.sizes.elevationMargin\n                        }\n                        spacing: 0\n\n                        OsdValueIndicator {\n                            id: osdValues\n                            Layout.fillWidth: true\n                            value: Audio.sink?.audio.volume ?? 0\n                            icon: Audio.sink?.audio.muted ? \"volume_off\" : \"volume_up\"\n                            name: qsTr(\"Volume\")\n                        }\n\n                        Item {\n                            id: protectionMessageWrapper\n                            implicitHeight: protectionMessageBackground.implicitHeight\n                            implicitWidth: protectionMessageBackground.implicitWidth\n                            Layout.alignment: Qt.AlignHCenter\n                            opacity: root.protectionMessage !== \"\" ? 1 : 0\n\n                            StyledRectangularShadow {\n                                target: protectionMessageBackground\n                            }\n                            Rectangle {\n                                id: protectionMessageBackground\n                                anchors.centerIn: parent\n                                color: Appearance.m3colors.m3error\n                                property real padding: 10\n                                implicitHeight: protectionMessageRowLayout.implicitHeight + padding * 2\n                                implicitWidth: protectionMessageRowLayout.implicitWidth + padding * 2\n                                radius: Appearance.rounding.normal\n\n                                RowLayout {\n                                    id: protectionMessageRowLayout\n                                    anchors.centerIn: parent\n                                    MaterialSymbol {\n                                        id: protectionMessageIcon\n                                        text: \"dangerous\"\n                                        iconSize: Appearance.font.pixelSize.hugeass\n                                        color: Appearance.m3colors.m3onError\n                                    }\n                                    StyledText {\n                                        id: protectionMessageTextWidget\n                                        horizontalAlignment: Text.AlignHCenter\n                                        color: Appearance.m3colors.m3onError\n                                        wrapMode: Text.Wrap\n                                        text: root.protectionMessage\n                                    }\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    IpcHandler {\n\t\ttarget: \"osdVolume\"\n\n\t\tfunction trigger() {\n            root.triggerOsd()\n        }\n\n        function hide() {\n            showOsdValues = false\n        }\n\n        function toggle() {\n            showOsdValues = !showOsdValues\n        }\n\t}\n    GlobalShortcut {\n        name: \"osdVolumeTrigger\"\n        description: qsTr(\"Triggers volume OSD on press\")\n\n        onPressed: {\n            root.triggerOsd()\n        }\n    }\n    GlobalShortcut {\n        name: \"osdVolumeHide\"\n        description: qsTr(\"Hides volume OSD on press\")\n\n        onPressed: {\n            root.showOsdValues = false\n        }\n    }\n\n}\n"}