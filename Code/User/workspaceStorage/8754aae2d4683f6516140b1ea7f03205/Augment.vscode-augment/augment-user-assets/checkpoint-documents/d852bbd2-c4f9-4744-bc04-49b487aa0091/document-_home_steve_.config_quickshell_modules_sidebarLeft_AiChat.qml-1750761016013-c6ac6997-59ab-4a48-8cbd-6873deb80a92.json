{"path": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/sidebarLeft/AiChat.qml"}, "originalCode": "import \"root:/\"\nimport \"root:/services\"\nimport \"root:/modules/common\"\nimport \"root:/modules/common/widgets\"\nimport \"./aiChat/\"\nimport \"root:/modules/common/functions/fuzzysort.js\" as Fuzzy\nimport \"root:/modules/common/functions/string_utils.js\" as StringUtils\nimport \"root:/modules/common/functions/file_utils.js\" as FileUtils\nimport QtQuick\nimport QtQuick.Controls\nimport QtQuick.Layouts\nimport Qt5Compat.GraphicalEffects\nimport Quickshell.Io\nimport Quickshell\nimport Quickshell.Hyprland\n\nItem {\n    id: root\n    property var inputField: messageInputField\n    property string commandPrefix: \"/\"\n\n    property var suggestionQuery: \"\"\n    property var suggestionList: []\n\n    onFocusChanged: (focus) => {\n        if (focus) {\n            root.inputField.forceActiveFocus()\n        }\n    }\n\n    Keys.onPressed: (event) => {\n        messageInputField.forceActiveFocus()\n        if (event.modifiers === Qt.NoModifier) {\n            if (event.key === Qt.Key_PageUp) {\n                messageListView.contentY = Math.max(0, messageListView.contentY - messageListView.height / 2)\n                event.accepted = true\n            } else if (event.key === Qt.Key_PageDown) {\n                messageListView.contentY = Math.min(messageListView.contentHeight - messageListView.height / 2, messageListView.contentY + messageListView.height / 2)\n                event.accepted = true\n            }\n        }\n    }\n\n    property var allCommands: [\n        {\n            name: \"model\",\n            description: qsTr(\"选择模型\"),\n            execute: (args) => {\n                Ai.setModel(args[0]);\n            }\n        },\n        {\n            name: \"clear\",\n            description: qsTr(\"清除聊天记录\"),\n            execute: () => {\n                Ai.clearMessages();\n            }\n        },\n        {\n            name: \"key\",\n            description: qsTr(\"设置API密钥\"),\n            execute: (args) => {\n                if (args[0] == \"get\") {\n                    Ai.printApiKey()\n                } else {\n                    Ai.setApiKey(args[0]);\n                }\n            }\n        },\n        {\n            name: \"temp\",\n            description: qsTr(\"Set temperature (randomness) of the model. Values range between 0 to 2 for Gemini, 0 to 1 for other models. Default is 0.5.\"),\n            execute: (args) => {\n                // console.log(args)\n                if (args.length == 0 || args[0] == \"get\") {\n                    Ai.printTemperature()\n                } else {\n                    const temp = parseFloat(args[0]);\n                    Ai.setTemperature(temp);\n                }\n            }\n        },\n        {\n            name: \"test\",\n            description: qsTr(\"Markdown测试\"),\n            execute: () => {\n                Ai.addMessage(`\n<think>\nA longer think block to test revealing animation\nOwO wem ipsum dowo sit amet, consekituwet awipiscing ewit, sed do eiuwsmod tempow inwididunt ut wabowe et dowo mawa. Ut enim ad minim weniam, quis nostwud exeucitation uwuwamcow bowowis nisi ut awiquip ex ea commowo consequat. Duuis aute iwuwe dowo in wepwependewit in wowuptate velit esse ciwwum dowo eu fugiat nuwa pawiatuw. Excepteuw sint occaecat cupidatat non pwowoident, sunt in cuwpa qui officia desewunt mowit anim id est wabowum. Meouw! >w<\nMowe uwu wem ipsum!\n</think>\n## ✏️ Markdown test\n### Formatting\n\n- *Italic*, \\`Monospace\\`, **Bold**, [Link](https://example.com)\n- Arch lincox icon <img src=\"/home/<USER>/.config/quickshell/assets/icons/arch-symbolic.svg\" height=\"${Appearance.font.pixelSize.small}\"/>\n\n### Table\n\nQuickshell vs AGS/Astal\n\n|                          | Quickshell       | AGS/Astal         |\n|--------------------------|------------------|-------------------|\n| UI Toolkit               | Qt               | Gtk3/Gtk4         |\n| Language                 | QML              | Js/Ts/Lua         |\n| Reactivity               | Implied          | Needs declaration |\n| Widget placement         | Mildly difficult | More intuitive    |\n| Bluetooth & Wifi support | ❌               | ✅                |\n| No-delay keybinds        | ✅               | ❌                |\n| Development              | New APIs         | New syntax        |\n\n### Code block\n\nJust a hello world...\n\n\\`\\`\\`cpp\n#include <bits/stdc++.h>\n// This is intentionally very long to test scrolling\nconst std::string GREETING = \\\"UwU\\\";\nint main(int argc, char* argv[]) {\n    std::cout << GREETING;\n}\n\\`\\`\\`\n\n### LaTeX\n\n\nInline w/ dollar signs: $\\\\frac{1}{2} = \\\\frac{2}{4}$\n\nInline w/ double dollar signs: $$\\\\int_0^\\\\infty e^{-x^2} dx = \\\\frac{\\\\sqrt{\\\\pi}}{2}$$\n\nInline w/ backslash and square brackets \\\\[\\\\int_0^\\\\infty \\\\frac{1}{x^2} dx = \\\\infty\\\\]\n\nInline w/ backslash and round brackets \\\\(e^{i\\\\pi} + 1 = 0\\\\)\n`, \n                    Ai.interfaceRole);\n            }\n        },\n    ]\n\n    function handleInput(inputText) {\n        if (inputText.startsWith(root.commandPrefix)) {\n            // Handle special commands\n            const command = inputText.split(\" \")[0].substring(1);\n            const args = inputText.split(\" \").slice(1);\n            const commandObj = root.allCommands.find(cmd => cmd.name === `${command}`);\n            if (commandObj) {\n                commandObj.execute(args);\n            } else {\n                Ai.addMessage(qsTr(\"Unknown command: \") + command, Ai.interfaceRole);\n            }\n        }\n        else {\n            Ai.sendUserMessage(inputText);\n        }\n    }\n\n    ColumnLayout {\n        id: columnLayout\n        anchors.fill: parent\n\n        Item { // Messages\n            Layout.fillWidth: true\n            Layout.fillHeight: true\n            StyledListView { // Message list\n                id: messageListView\n                anchors.fill: parent\n                spacing: 10\n                popin: false\n\n                property int lastResponseLength: 0\n\n                clip: true\n                layer.enabled: true\n                layer.effect: OpacityMask {\n                    maskSource: Rectangle {\n                        width: swipeView.width\n                        height: swipeView.height\n                        radius: Appearance.rounding.small\n                    }\n                }\n\n                add: null // Prevent function calls from being janky\n\n                Behavior on contentY {\n                    NumberAnimation {\n                        id: scrollAnim\n                        duration: Appearance.animation.scroll.duration\n                        easing.type: Appearance.animation.scroll.type\n                        easing.bezierCurve: Appearance.animation.scroll.bezierCurve\n                    }\n                }\n\n                model: ScriptModel {\n                    values: Ai.messageIDs.filter(id => {\n                        const message = Ai.messageByID[id];\n                        return message?.visibleToUser ?? true;\n                    })\n                }\n                delegate: AiMessage {\n                    required property var modelData\n                    required property int index\n                    messageIndex: index\n                    messageData: {\n                        Ai.messageByID[modelData]\n                    }\n                    messageInputField: root.inputField\n                }\n            }\n\n            Item { // Placeholder when list is empty\n                opacity: Ai.messageIDs.length === 0 ? 1 : 0\n                visible: opacity > 0\n                anchors.fill: parent\n\n                Behavior on opacity {\n                    animation: Appearance.animation.elementMoveEnter.numberAnimation.createObject(this)\n                }\n\n                ColumnLayout {\n                    anchors.centerIn: parent\n                    spacing: 5\n\n                    MaterialSymbol {\n                        Layout.alignment: Qt.AlignHCenter\n                        iconSize: 60\n                        color: Appearance.m3colors.m3outline\n                        text: \"neurology\"\n                    }\n                    StyledText {\n                        id: widgetNameText\n                        Layout.alignment: Qt.AlignHCenter\n                        font.pixelSize: Appearance.font.pixelSize.larger\n                        font.family: Appearance.font.family.title\n                        color: Appearance.m3colors.m3outline\n                        horizontalAlignment: Text.AlignHCenter\n                        text: qsTr(\"大型语言模型\")\n                    }\n                    StyledText {\n                        id: widgetDescriptionText\n                        Layout.fillWidth: true\n                        font.pixelSize: Appearance.font.pixelSize.small\n                        color: Appearance.m3colors.m3outline\n                        horizontalAlignment: Text.AlignLeft\n                        wrapMode: Text.Wrap\n                        text: qsTr(\"Type /key to get started with online models\\nCtrl+O to expand the sidebar\\nCtrl+P to detach sidebar into a window\")\n                    }\n                }\n            }\n        }\n\n        Item { // Suggestion description\n            visible: descriptionText.text.length > 0\n            Layout.fillWidth: true\n            implicitHeight: descriptionBackground.implicitHeight\n\n            Rectangle {\n                id: descriptionBackground\n                color: Appearance.colors.colTooltip\n                anchors.left: parent.left\n                anchors.right: parent.right\n                anchors.verticalCenter: parent.verticalCenter\n                implicitHeight: descriptionText.implicitHeight + 5 * 2\n                radius: Appearance.rounding.verysmall\n\n                StyledText {\n                    id: descriptionText\n                    anchors.left: parent.left\n                    anchors.right: parent.right\n                    anchors.leftMargin: 10\n                    anchors.rightMargin: 10\n                    anchors.verticalCenter: parent.verticalCenter\n                    font.pixelSize: Appearance.font.pixelSize.smaller\n                    color: Appearance.colors.colOnTooltip\n                    wrapMode: Text.Wrap\n                    text: root.suggestionList[suggestions.selectedIndex]?.description ?? \"\"\n                }\n            }\n        }\n\n        FlowButtonGroup { // Suggestions\n            id: suggestions\n            visible: root.suggestionList.length > 0 && messageInputField.text.length > 0\n            property int selectedIndex: 0\n            Layout.fillWidth: true\n            spacing: 5\n\n            Repeater {\n                id: suggestionRepeater\n                model: {\n                    suggestions.selectedIndex = 0\n                    return root.suggestionList.slice(0, 10)\n                }\n                delegate: ApiCommandButton {\n                    id: commandButton\n                    colBackground: suggestions.selectedIndex === index ? Appearance.colors.colLayer2Hover : Appearance.colors.colLayer2\n                    bounce: false\n                    contentItem: StyledText {\n                        font.pixelSize: Appearance.font.pixelSize.small\n                        color: Appearance.m3colors.m3onSurface\n                        horizontalAlignment: Text.AlignHCenter\n                        text: modelData.displayName ?? modelData.name\n                    }\n\n                    onHoveredChanged: {\n                        if (commandButton.hovered) {\n                            suggestions.selectedIndex = index;\n                        }\n                    }\n                    onClicked: {\n                        suggestions.acceptSuggestion(modelData.name)\n                    }\n                }\n            }\n\n            function acceptSuggestion(word) {\n                const words = messageInputField.text.trim().split(/\\s+/);\n                if (words.length > 0) {\n                    words[words.length - 1] = word;\n                } else {\n                    words.push(word);\n                }\n                const updatedText = words.join(\" \") + \" \";\n                messageInputField.text = updatedText;\n                messageInputField.cursorPosition = messageInputField.text.length;\n                messageInputField.forceActiveFocus();\n            }\n\n            function acceptSelectedWord() {\n                if (suggestions.selectedIndex >= 0 && suggestions.selectedIndex < suggestionRepeater.count) {\n                    const word = root.suggestionList[suggestions.selectedIndex].name;\n                    suggestions.acceptSuggestion(word);\n                }\n            }\n        }\n\n        Rectangle { // Input area\n            id: inputWrapper\n            property real columnSpacing: 5\n            Layout.fillWidth: true\n            radius: Appearance.rounding.small\n            color: Appearance.colors.colLayer1\n            implicitWidth: messageInputField.implicitWidth\n            implicitHeight: Math.max(inputFieldRowLayout.implicitHeight + inputFieldRowLayout.anchors.topMargin \n                + commandButtonsRow.implicitHeight + commandButtonsRow.anchors.bottomMargin + columnSpacing, 45)\n            clip: true\n            border.color: Appearance.colors.colOutlineVariant\n            border.width: 1\n\n            Behavior on implicitHeight {\n                animation: Appearance.animation.elementMove.numberAnimation.createObject(this)\n            }\n\n            RowLayout { // Input field and send button\n                id: inputFieldRowLayout\n                anchors.top: parent.top\n                anchors.left: parent.left\n                anchors.right: parent.right\n                anchors.topMargin: 5\n                spacing: 0\n\n                StyledTextArea { // The actual TextArea\n                    id: messageInputField\n                    wrapMode: TextArea.Wrap\n                    Layout.fillWidth: true\n                    padding: 10\n                    color: activeFocus ? Appearance.m3colors.m3onSurface : Appearance.m3colors.m3onSurfaceVariant\n                    placeholderText: StringUtils.format(qsTr('Message the model... \"{0}\" for commands'), root.commandPrefix)\n\n                    background: null\n\n                    onTextChanged: { // Handle suggestions\n                        if(messageInputField.text.length === 0) {\n                            root.suggestionQuery = \"\"\n                            root.suggestionList = []\n                            return\n                        } else if(messageInputField.text.startsWith(`${root.commandPrefix}model`)) {\n                            root.suggestionQuery = messageInputField.text.split(\" \")[1] ?? \"\"\n                            const modelResults = Fuzzy.go(root.suggestionQuery, Ai.modelList.map(model => {\n                                return {\n                                    name: Fuzzy.prepare(model),\n                                    obj: model,\n                                }\n                            }), {\n                                all: true,\n                                key: \"name\"\n                            })\n                            root.suggestionList = modelResults.map(model => {\n                                return {\n                                    name: `${messageInputField.text.trim().split(\" \").length == 1 ? (root.commandPrefix + \"model \") : \"\"}${model.target}`,\n                                    displayName: `${Ai.models[model.target].name}`,\n                                    description: `${Ai.models[model.target].description}`,\n                                }\n                            })\n                        } else if(messageInputField.text.startsWith(root.commandPrefix)) {\n                            root.suggestionQuery = messageInputField.text\n                            root.suggestionList = root.allCommands.filter(cmd => cmd.name.startsWith(messageInputField.text.substring(1))).map(cmd => {\n                                return {\n                                    name: `${root.commandPrefix}${cmd.name}`,\n                                    description: `${cmd.description}`,\n                                }\n                            })\n                        }\n                    }\n\n                    function accept() {\n                        root.handleInput(text)\n                        text = \"\"\n                    }\n\n                    Keys.onPressed: (event) => {\n                        if (event.key === Qt.Key_Tab) {\n                            suggestions.acceptSelectedWord();\n                            event.accepted = true;\n                        } else if (event.key === Qt.Key_Up && suggestions.visible) {\n                            suggestions.selectedIndex = Math.max(0, suggestions.selectedIndex - 1);\n                            event.accepted = true;\n                        } else if (event.key === Qt.Key_Down && suggestions.visible) {\n                            suggestions.selectedIndex = Math.min(root.suggestionList.length - 1, suggestions.selectedIndex + 1);\n                            event.accepted = true;\n                        } else if ((event.key === Qt.Key_Enter || event.key === Qt.Key_Return)) {\n                            if (event.modifiers & Qt.ShiftModifier) {\n                                // Insert newline\n                                messageInputField.insert(messageInputField.cursorPosition, \"\\n\")\n                                event.accepted = true\n                            } else { // Accept text\n                                const inputText = messageInputField.text\n                                messageInputField.clear()\n                                root.handleInput(inputText)\n                                event.accepted = true\n                            }\n                        }\n                    }\n                }\n\n                RippleButton { // Send button\n                    id: sendButton\n                    Layout.alignment: Qt.AlignTop\n                    Layout.rightMargin: 5\n                    implicitWidth: 40\n                    implicitHeight: 40\n                    buttonRadius: Appearance.rounding.small\n                    enabled: messageInputField.text.length > 0\n                    toggled: enabled\n\n                    MouseArea {\n                        anchors.fill: parent\n                        cursorShape: sendButton.enabled ? Qt.PointingHandCursor : Qt.ArrowCursor\n                        onClicked: {\n                            const inputText = messageInputField.text\n                            root.handleInput(inputText)\n                            messageInputField.clear()\n                        }\n                    }\n\n                    contentItem: MaterialSymbol {\n                        anchors.centerIn: parent\n                        horizontalAlignment: Text.AlignHCenter\n                        iconSize: Appearance.font.pixelSize.larger\n                        // fill: sendButton.enabled ? 1 : 0\n                        color: sendButton.enabled ? Appearance.m3colors.m3onPrimary : Appearance.colors.colOnLayer2Disabled\n                        text: \"发送\"\n                    }\n                }\n            }\n\n            RowLayout { // Controls\n                id: commandButtonsRow\n                anchors.left: parent.left\n                anchors.right: parent.right\n                anchors.bottom: parent.bottom\n                anchors.bottomMargin: 5\n                anchors.leftMargin: 5\n                anchors.rightMargin: 5\n                spacing: 5\n\n                property var commandsShown: [\n                    {\n                        name: \"model\",\n                        sendDirectly: false,\n                    },\n                    {\n                        name: \"clear\",\n                        sendDirectly: true,\n                    }, \n                ]\n\n                Item {\n                    implicitHeight: providerRowLayout.implicitHeight + 5 * 2\n                    implicitWidth: providerRowLayout.implicitWidth + 10 * 2\n                    \n                    RowLayout {\n                        id: providerRowLayout\n                        anchors.centerIn: parent\n\n                        MaterialSymbol {\n                            text: \"接口\"\n                            iconSize: Appearance.font.pixelSize.large\n                        }\n                        StyledText {\n                            id: providerName\n                            font.pixelSize: Appearance.font.pixelSize.small\n                            color: Appearance.m3colors.m3onSurface\n                            elide: Text.ElideRight\n                            text: Ai.getModel().name\n                        }\n                    }\n                    StyledToolTip {\n                        id: toolTip\n                        extraVisibleCondition: false\n                        alternativeVisibleCondition: mouseArea.containsMouse // Show tooltip when hovered\n                        content: StringUtils.format(qsTr(\"Current model: {0}\\nSet it with {1}model MODEL\"), \n                            Ai.getModel().name, root.commandPrefix)\n                    }\n\n                    MouseArea {\n                        id: mouseArea\n                        anchors.fill: parent\n                        hoverEnabled: true\n                    }\n                }\n\n                Item { Layout.fillWidth: true }\n\n                ButtonGroup {\n                    padding: 0\n\n                    Repeater { // Command buttons\n                        model: commandButtonsRow.commandsShown\n                        delegate: ApiCommandButton {\n                            property string commandRepresentation: `${root.commandPrefix}${modelData.name}`\n                            buttonText: commandRepresentation\n                            onClicked: {\n                                if(modelData.sendDirectly) {\n                                    root.handleInput(commandRepresentation)\n                                } else {\n                                    messageInputField.text = commandRepresentation + \" \"\n                                    messageInputField.cursorPosition = messageInputField.text.length\n                                    messageInputField.forceActiveFocus()\n                                }\n                                if (modelData.name === \"clear\") {\n                                    messageInputField.text = \"\"\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n\n        }\n        \n    }\n\n}", "modifiedCode": "import \"root:/\"\nimport \"root:/services\"\nimport \"root:/modules/common\"\nimport \"root:/modules/common/widgets\"\nimport \"./aiChat/\"\nimport \"root:/modules/common/functions/fuzzysort.js\" as Fuzzy\nimport \"root:/modules/common/functions/string_utils.js\" as StringUtils\nimport \"root:/modules/common/functions/file_utils.js\" as FileUtils\nimport QtQuick\nimport QtQuick.Controls\nimport QtQuick.Layouts\nimport Qt5Compat.GraphicalEffects\nimport Quickshell.Io\nimport Quickshell\nimport Quickshell.Hyprland\n\nItem {\n    id: root\n    property var inputField: messageInputField\n    property string commandPrefix: \"/\"\n\n    property var suggestionQuery: \"\"\n    property var suggestionList: []\n\n    onFocusChanged: (focus) => {\n        if (focus) {\n            root.inputField.forceActiveFocus()\n        }\n    }\n\n    Keys.onPressed: (event) => {\n        messageInputField.forceActiveFocus()\n        if (event.modifiers === Qt.NoModifier) {\n            if (event.key === Qt.Key_PageUp) {\n                messageListView.contentY = Math.max(0, messageListView.contentY - messageListView.height / 2)\n                event.accepted = true\n            } else if (event.key === Qt.Key_PageDown) {\n                messageListView.contentY = Math.min(messageListView.contentHeight - messageListView.height / 2, messageListView.contentY + messageListView.height / 2)\n                event.accepted = true\n            }\n        }\n    }\n\n    property var allCommands: [\n        {\n            name: \"model\",\n            description: qsTr(\"Choose model\"),\n            execute: (args) => {\n                Ai.setModel(args[0]);\n            }\n        },\n        {\n            name: \"clear\",\n            description: qsTr(\"Clear chat history\"),\n            execute: () => {\n                Ai.clearMessages();\n            }\n        },\n        {\n            name: \"key\",\n            description: qsTr(\"Set API key\"),\n            execute: (args) => {\n                if (args[0] == \"get\") {\n                    Ai.printApiKey()\n                } else {\n                    Ai.setApiKey(args[0]);\n                }\n            }\n        },\n        {\n            name: \"temp\",\n            description: qsTr(\"Set temperature (randomness) of the model. Values range between 0 to 2 for Gemini, 0 to 1 for other models. Default is 0.5.\"),\n            execute: (args) => {\n                // console.log(args)\n                if (args.length == 0 || args[0] == \"get\") {\n                    Ai.printTemperature()\n                } else {\n                    const temp = parseFloat(args[0]);\n                    Ai.setTemperature(temp);\n                }\n            }\n        },\n        {\n            name: \"test\",\n            description: qsTr(\"Markdown test\"),\n            execute: () => {\n                Ai.addMessage(`\n<think>\nA longer think block to test revealing animation\nOwO wem ipsum dowo sit amet, consekituwet awipiscing ewit, sed do eiuwsmod tempow inwididunt ut wabowe et dowo mawa. Ut enim ad minim weniam, quis nostwud exeucitation uwuwamcow bowowis nisi ut awiquip ex ea commowo consequat. Duuis aute iwuwe dowo in wepwependewit in wowuptate velit esse ciwwum dowo eu fugiat nuwa pawiatuw. Excepteuw sint occaecat cupidatat non pwowoident, sunt in cuwpa qui officia desewunt mowit anim id est wabowum. Meouw! >w<\nMowe uwu wem ipsum!\n</think>\n## ✏️ Markdown test\n### Formatting\n\n- *Italic*, \\`Monospace\\`, **Bold**, [Link](https://example.com)\n- Arch lincox icon <img src=\"/home/<USER>/.config/quickshell/assets/icons/arch-symbolic.svg\" height=\"${Appearance.font.pixelSize.small}\"/>\n\n### Table\n\nQuickshell vs AGS/Astal\n\n|                          | Quickshell       | AGS/Astal         |\n|--------------------------|------------------|-------------------|\n| UI Toolkit               | Qt               | Gtk3/Gtk4         |\n| Language                 | QML              | Js/Ts/Lua         |\n| Reactivity               | Implied          | Needs declaration |\n| Widget placement         | Mildly difficult | More intuitive    |\n| Bluetooth & Wifi support | ❌               | ✅                |\n| No-delay keybinds        | ✅               | ❌                |\n| Development              | New APIs         | New syntax        |\n\n### Code block\n\nJust a hello world...\n\n\\`\\`\\`cpp\n#include <bits/stdc++.h>\n// This is intentionally very long to test scrolling\nconst std::string GREETING = \\\"UwU\\\";\nint main(int argc, char* argv[]) {\n    std::cout << GREETING;\n}\n\\`\\`\\`\n\n### LaTeX\n\n\nInline w/ dollar signs: $\\\\frac{1}{2} = \\\\frac{2}{4}$\n\nInline w/ double dollar signs: $$\\\\int_0^\\\\infty e^{-x^2} dx = \\\\frac{\\\\sqrt{\\\\pi}}{2}$$\n\nInline w/ backslash and square brackets \\\\[\\\\int_0^\\\\infty \\\\frac{1}{x^2} dx = \\\\infty\\\\]\n\nInline w/ backslash and round brackets \\\\(e^{i\\\\pi} + 1 = 0\\\\)\n`, \n                    Ai.interfaceRole);\n            }\n        },\n    ]\n\n    function handleInput(inputText) {\n        if (inputText.startsWith(root.commandPrefix)) {\n            // Handle special commands\n            const command = inputText.split(\" \")[0].substring(1);\n            const args = inputText.split(\" \").slice(1);\n            const commandObj = root.allCommands.find(cmd => cmd.name === `${command}`);\n            if (commandObj) {\n                commandObj.execute(args);\n            } else {\n                Ai.addMessage(qsTr(\"Unknown command: \") + command, Ai.interfaceRole);\n            }\n        }\n        else {\n            Ai.sendUserMessage(inputText);\n        }\n    }\n\n    ColumnLayout {\n        id: columnLayout\n        anchors.fill: parent\n\n        Item { // Messages\n            Layout.fillWidth: true\n            Layout.fillHeight: true\n            StyledListView { // Message list\n                id: messageListView\n                anchors.fill: parent\n                spacing: 10\n                popin: false\n\n                property int lastResponseLength: 0\n\n                clip: true\n                layer.enabled: true\n                layer.effect: OpacityMask {\n                    maskSource: Rectangle {\n                        width: swipeView.width\n                        height: swipeView.height\n                        radius: Appearance.rounding.small\n                    }\n                }\n\n                add: null // Prevent function calls from being janky\n\n                Behavior on contentY {\n                    NumberAnimation {\n                        id: scrollAnim\n                        duration: Appearance.animation.scroll.duration\n                        easing.type: Appearance.animation.scroll.type\n                        easing.bezierCurve: Appearance.animation.scroll.bezierCurve\n                    }\n                }\n\n                model: ScriptModel {\n                    values: Ai.messageIDs.filter(id => {\n                        const message = Ai.messageByID[id];\n                        return message?.visibleToUser ?? true;\n                    })\n                }\n                delegate: AiMessage {\n                    required property var modelData\n                    required property int index\n                    messageIndex: index\n                    messageData: {\n                        Ai.messageByID[modelData]\n                    }\n                    messageInputField: root.inputField\n                }\n            }\n\n            Item { // Placeholder when list is empty\n                opacity: Ai.messageIDs.length === 0 ? 1 : 0\n                visible: opacity > 0\n                anchors.fill: parent\n\n                Behavior on opacity {\n                    animation: Appearance.animation.elementMoveEnter.numberAnimation.createObject(this)\n                }\n\n                ColumnLayout {\n                    anchors.centerIn: parent\n                    spacing: 5\n\n                    MaterialSymbol {\n                        Layout.alignment: Qt.AlignHCenter\n                        iconSize: 60\n                        color: Appearance.m3colors.m3outline\n                        text: \"neurology\"\n                    }\n                    StyledText {\n                        id: widgetNameText\n                        Layout.alignment: Qt.AlignHCenter\n                        font.pixelSize: Appearance.font.pixelSize.larger\n                        font.family: Appearance.font.family.title\n                        color: Appearance.m3colors.m3outline\n                        horizontalAlignment: Text.AlignHCenter\n                        text: qsTr(\"Large language models\")\n                    }\n                    StyledText {\n                        id: widgetDescriptionText\n                        Layout.fillWidth: true\n                        font.pixelSize: Appearance.font.pixelSize.small\n                        color: Appearance.m3colors.m3outline\n                        horizontalAlignment: Text.AlignLeft\n                        wrapMode: Text.Wrap\n                        text: qsTr(\"Type /key to get started with online models\\nCtrl+O to expand the sidebar\\nCtrl+P to detach sidebar into a window\")\n                    }\n                }\n            }\n        }\n\n        Item { // Suggestion description\n            visible: descriptionText.text.length > 0\n            Layout.fillWidth: true\n            implicitHeight: descriptionBackground.implicitHeight\n\n            Rectangle {\n                id: descriptionBackground\n                color: Appearance.colors.colTooltip\n                anchors.left: parent.left\n                anchors.right: parent.right\n                anchors.verticalCenter: parent.verticalCenter\n                implicitHeight: descriptionText.implicitHeight + 5 * 2\n                radius: Appearance.rounding.verysmall\n\n                StyledText {\n                    id: descriptionText\n                    anchors.left: parent.left\n                    anchors.right: parent.right\n                    anchors.leftMargin: 10\n                    anchors.rightMargin: 10\n                    anchors.verticalCenter: parent.verticalCenter\n                    font.pixelSize: Appearance.font.pixelSize.smaller\n                    color: Appearance.colors.colOnTooltip\n                    wrapMode: Text.Wrap\n                    text: root.suggestionList[suggestions.selectedIndex]?.description ?? \"\"\n                }\n            }\n        }\n\n        FlowButtonGroup { // Suggestions\n            id: suggestions\n            visible: root.suggestionList.length > 0 && messageInputField.text.length > 0\n            property int selectedIndex: 0\n            Layout.fillWidth: true\n            spacing: 5\n\n            Repeater {\n                id: suggestionRepeater\n                model: {\n                    suggestions.selectedIndex = 0\n                    return root.suggestionList.slice(0, 10)\n                }\n                delegate: ApiCommandButton {\n                    id: commandButton\n                    colBackground: suggestions.selectedIndex === index ? Appearance.colors.colLayer2Hover : Appearance.colors.colLayer2\n                    bounce: false\n                    contentItem: StyledText {\n                        font.pixelSize: Appearance.font.pixelSize.small\n                        color: Appearance.m3colors.m3onSurface\n                        horizontalAlignment: Text.AlignHCenter\n                        text: modelData.displayName ?? modelData.name\n                    }\n\n                    onHoveredChanged: {\n                        if (commandButton.hovered) {\n                            suggestions.selectedIndex = index;\n                        }\n                    }\n                    onClicked: {\n                        suggestions.acceptSuggestion(modelData.name)\n                    }\n                }\n            }\n\n            function acceptSuggestion(word) {\n                const words = messageInputField.text.trim().split(/\\s+/);\n                if (words.length > 0) {\n                    words[words.length - 1] = word;\n                } else {\n                    words.push(word);\n                }\n                const updatedText = words.join(\" \") + \" \";\n                messageInputField.text = updatedText;\n                messageInputField.cursorPosition = messageInputField.text.length;\n                messageInputField.forceActiveFocus();\n            }\n\n            function acceptSelectedWord() {\n                if (suggestions.selectedIndex >= 0 && suggestions.selectedIndex < suggestionRepeater.count) {\n                    const word = root.suggestionList[suggestions.selectedIndex].name;\n                    suggestions.acceptSuggestion(word);\n                }\n            }\n        }\n\n        Rectangle { // Input area\n            id: inputWrapper\n            property real columnSpacing: 5\n            Layout.fillWidth: true\n            radius: Appearance.rounding.small\n            color: Appearance.colors.colLayer1\n            implicitWidth: messageInputField.implicitWidth\n            implicitHeight: Math.max(inputFieldRowLayout.implicitHeight + inputFieldRowLayout.anchors.topMargin \n                + commandButtonsRow.implicitHeight + commandButtonsRow.anchors.bottomMargin + columnSpacing, 45)\n            clip: true\n            border.color: Appearance.colors.colOutlineVariant\n            border.width: 1\n\n            Behavior on implicitHeight {\n                animation: Appearance.animation.elementMove.numberAnimation.createObject(this)\n            }\n\n            RowLayout { // Input field and send button\n                id: inputFieldRowLayout\n                anchors.top: parent.top\n                anchors.left: parent.left\n                anchors.right: parent.right\n                anchors.topMargin: 5\n                spacing: 0\n\n                StyledTextArea { // The actual TextArea\n                    id: messageInputField\n                    wrapMode: TextArea.Wrap\n                    Layout.fillWidth: true\n                    padding: 10\n                    color: activeFocus ? Appearance.m3colors.m3onSurface : Appearance.m3colors.m3onSurfaceVariant\n                    placeholderText: StringUtils.format(qsTr('Message the model... \"{0}\" for commands'), root.commandPrefix)\n\n                    background: null\n\n                    onTextChanged: { // Handle suggestions\n                        if(messageInputField.text.length === 0) {\n                            root.suggestionQuery = \"\"\n                            root.suggestionList = []\n                            return\n                        } else if(messageInputField.text.startsWith(`${root.commandPrefix}model`)) {\n                            root.suggestionQuery = messageInputField.text.split(\" \")[1] ?? \"\"\n                            const modelResults = Fuzzy.go(root.suggestionQuery, Ai.modelList.map(model => {\n                                return {\n                                    name: Fuzzy.prepare(model),\n                                    obj: model,\n                                }\n                            }), {\n                                all: true,\n                                key: \"name\"\n                            })\n                            root.suggestionList = modelResults.map(model => {\n                                return {\n                                    name: `${messageInputField.text.trim().split(\" \").length == 1 ? (root.commandPrefix + \"model \") : \"\"}${model.target}`,\n                                    displayName: `${Ai.models[model.target].name}`,\n                                    description: `${Ai.models[model.target].description}`,\n                                }\n                            })\n                        } else if(messageInputField.text.startsWith(root.commandPrefix)) {\n                            root.suggestionQuery = messageInputField.text\n                            root.suggestionList = root.allCommands.filter(cmd => cmd.name.startsWith(messageInputField.text.substring(1))).map(cmd => {\n                                return {\n                                    name: `${root.commandPrefix}${cmd.name}`,\n                                    description: `${cmd.description}`,\n                                }\n                            })\n                        }\n                    }\n\n                    function accept() {\n                        root.handleInput(text)\n                        text = \"\"\n                    }\n\n                    Keys.onPressed: (event) => {\n                        if (event.key === Qt.Key_Tab) {\n                            suggestions.acceptSelectedWord();\n                            event.accepted = true;\n                        } else if (event.key === Qt.Key_Up && suggestions.visible) {\n                            suggestions.selectedIndex = Math.max(0, suggestions.selectedIndex - 1);\n                            event.accepted = true;\n                        } else if (event.key === Qt.Key_Down && suggestions.visible) {\n                            suggestions.selectedIndex = Math.min(root.suggestionList.length - 1, suggestions.selectedIndex + 1);\n                            event.accepted = true;\n                        } else if ((event.key === Qt.Key_Enter || event.key === Qt.Key_Return)) {\n                            if (event.modifiers & Qt.ShiftModifier) {\n                                // Insert newline\n                                messageInputField.insert(messageInputField.cursorPosition, \"\\n\")\n                                event.accepted = true\n                            } else { // Accept text\n                                const inputText = messageInputField.text\n                                messageInputField.clear()\n                                root.handleInput(inputText)\n                                event.accepted = true\n                            }\n                        }\n                    }\n                }\n\n                RippleButton { // Send button\n                    id: sendButton\n                    Layout.alignment: Qt.AlignTop\n                    Layout.rightMargin: 5\n                    implicitWidth: 40\n                    implicitHeight: 40\n                    buttonRadius: Appearance.rounding.small\n                    enabled: messageInputField.text.length > 0\n                    toggled: enabled\n\n                    MouseArea {\n                        anchors.fill: parent\n                        cursorShape: sendButton.enabled ? Qt.PointingHandCursor : Qt.ArrowCursor\n                        onClicked: {\n                            const inputText = messageInputField.text\n                            root.handleInput(inputText)\n                            messageInputField.clear()\n                        }\n                    }\n\n                    contentItem: MaterialSymbol {\n                        anchors.centerIn: parent\n                        horizontalAlignment: Text.AlignHCenter\n                        iconSize: Appearance.font.pixelSize.larger\n                        // fill: sendButton.enabled ? 1 : 0\n                        color: sendButton.enabled ? Appearance.m3colors.m3onPrimary : Appearance.colors.colOnLayer2Disabled\n                        text: \"send\"\n                    }\n                }\n            }\n\n            RowLayout { // Controls\n                id: commandButtonsRow\n                anchors.left: parent.left\n                anchors.right: parent.right\n                anchors.bottom: parent.bottom\n                anchors.bottomMargin: 5\n                anchors.leftMargin: 5\n                anchors.rightMargin: 5\n                spacing: 5\n\n                property var commandsShown: [\n                    {\n                        name: \"model\",\n                        sendDirectly: false,\n                    },\n                    {\n                        name: \"clear\",\n                        sendDirectly: true,\n                    }, \n                ]\n\n                Item {\n                    implicitHeight: providerRowLayout.implicitHeight + 5 * 2\n                    implicitWidth: providerRowLayout.implicitWidth + 10 * 2\n                    \n                    RowLayout {\n                        id: providerRowLayout\n                        anchors.centerIn: parent\n\n                        MaterialSymbol {\n                            text: \"api\"\n                            iconSize: Appearance.font.pixelSize.large\n                        }\n                        StyledText {\n                            id: providerName\n                            font.pixelSize: Appearance.font.pixelSize.small\n                            color: Appearance.m3colors.m3onSurface\n                            elide: Text.ElideRight\n                            text: Ai.getModel().name\n                        }\n                    }\n                    StyledToolTip {\n                        id: toolTip\n                        extraVisibleCondition: false\n                        alternativeVisibleCondition: mouseArea.containsMouse // Show tooltip when hovered\n                        content: StringUtils.format(qsTr(\"Current model: {0}\\nSet it with {1}model MODEL\"), \n                            Ai.getModel().name, root.commandPrefix)\n                    }\n\n                    MouseArea {\n                        id: mouseArea\n                        anchors.fill: parent\n                        hoverEnabled: true\n                    }\n                }\n\n                Item { Layout.fillWidth: true }\n\n                ButtonGroup {\n                    padding: 0\n\n                    Repeater { // Command buttons\n                        model: commandButtonsRow.commandsShown\n                        delegate: ApiCommandButton {\n                            property string commandRepresentation: `${root.commandPrefix}${modelData.name}`\n                            buttonText: commandRepresentation\n                            onClicked: {\n                                if(modelData.sendDirectly) {\n                                    root.handleInput(commandRepresentation)\n                                } else {\n                                    messageInputField.text = commandRepresentation + \" \"\n                                    messageInputField.cursorPosition = messageInputField.text.length\n                                    messageInputField.forceActiveFocus()\n                                }\n                                if (modelData.name === \"clear\") {\n                                    messageInputField.text = \"\"\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n\n        }\n        \n    }\n\n}"}