{"path": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/common/Appearance.qml"}, "originalCode": "import QtQuick\nimport Quickshell\nimport \"root:/modules/common/functions/color_utils.js\" as ColorUtils\npragma Singleton\npragma ComponentBehavior: Bo<PERSON>\n\nSingleton {\n    id: root\n    property QtObject m3colors\n    property QtObject animation\n    property QtObject animationCurves\n    property QtObject colors\n    property QtObject rounding\n    property QtObject font\n    property QtObject sizes\n    property string syntaxHighlightingTheme\n\n    // Extremely conservative transparency values for consistency and readability\n    property real transparency: ConfigOptions?.appearance.transparency ? (m3colors.darkmode ? 0.1 : 0.07) : 0\n    property real contentTransparency: ConfigOptions?.appearance.transparency ? (m3colors.darkmode ? 0.55 : 0.55) : 0\n\n    m3colors: QtObject {\n        property bool darkmode: false\n        property bool transparent: false\n        property color m3primary_paletteKeyColor: \"#91689E\"\n        property color m3secondary_paletteKeyColor: \"#837186\"\n        property color m3tertiary_paletteKeyColor: \"#9D6A67\"\n        property color m3neutral_paletteKeyColor: \"#7C757B\"\n        property color m3neutral_variant_paletteKeyColor: \"#7D747D\"\n        property color m3background: \"#161217\"\n        property color m3onBackground: \"#EAE0E7\"\n        property color m3surface: \"#161217\"\n        property color m3surfaceDim: \"#161217\"\n        property color m3surfaceBright: \"#3D373D\"\n        property color m3surfaceContainerLowest: \"#110D12\"\n        property color m3surfaceContainerLow: \"#1F1A1F\"\n        property color m3surfaceContainer: \"#231E23\"\n        property color m3surfaceContainerHigh: \"#2D282E\"\n        property color m3surfaceContainerHighest: \"#383339\"\n        property color m3onSurface: \"#EAE0E7\"\n        property color m3surfaceVariant: \"#4C444D\"\n        property color m3onSurfaceVariant: \"#CFC3CD\"\n        property color m3inverseSurface: \"#EAE0E7\"\n        property color m3inverseOnSurface: \"#342F34\"\n        property color m3outline: \"#988E97\"\n        property color m3outlineVariant: \"#4C444D\"\n        property color m3shadow: \"#000000\"\n        property color m3scrim: \"#000000\"\n        property color m3surfaceTint: \"#E5B6F2\"\n        property color m3primary: \"#E5B6F2\"\n        property color m3onPrimary: \"#452152\"\n        property color m3primaryContainer: \"#5D386A\"\n        property color m3onPrimaryContainer: \"#F9D8FF\"\n        property color m3inversePrimary: \"#775084\"\n        property color m3secondary: \"#D5C0D7\"\n        property color m3onSecondary: \"#392C3D\"\n        property color m3secondaryContainer: \"#534457\"\n        property color m3onSecondaryContainer: \"#F2DCF3\"\n        property color m3tertiary: \"#F5B7B3\"\n        property color m3onTertiary: \"#4C2523\"\n        property color m3tertiaryContainer: \"#BA837F\"\n        property color m3onTertiaryContainer: \"#000000\"\n        property color m3error: \"#FFB4AB\"\n        property color m3onError: \"#690005\"\n        property color m3errorContainer: \"#93000A\"\n        property color m3onErrorContainer: \"#FFDAD6\"\n        property color m3primaryFixed: \"#F9D8FF\"\n        property color m3primaryFixedDim: \"#E5B6F2\"\n        property color m3onPrimaryFixed: \"#2E0A3C\"\n        property color m3onPrimaryFixedVariant: \"#5D386A\"\n        property color m3secondaryFixed: \"#F2DCF3\"\n        property color m3secondaryFixedDim: \"#D5C0D7\"\n        property color m3onSecondaryFixed: \"#241727\"\n        property color m3onSecondaryFixedVariant: \"#514254\"\n        property color m3tertiaryFixed: \"#FFDAD7\"\n        property color m3tertiaryFixedDim: \"#F5B7B3\"\n        property color m3onTertiaryFixed: \"#331110\"\n        property color m3onTertiaryFixedVariant: \"#663B39\"\n        property color m3success: \"#B5CCBA\"\n        property color m3onSuccess: \"#213528\"\n        property color m3successContainer: \"#374B3E\"\n        property color m3onSuccessContainer: \"#D1E9D6\"\n        property color term0: \"#EDE4E4\"\n        property color term1: \"#B52755\"\n        property color term2: \"#A97363\"\n        property color term3: \"#AF535D\"\n        property color term4: \"#A67F7C\"\n        property color term5: \"#B2416B\"\n        property color term6: \"#8D76AD\"\n        property color term7: \"#272022\"\n        property color term8: \"#0E0D0D\"\n        property color term9: \"#B52755\"\n        property color term10: \"#A97363\"\n        property color term11: \"#AF535D\"\n        property color term12: \"#A67F7C\"\n        property color term13: \"#B2416B\"\n        property color term14: \"#8D76AD\"\n        property color term15: \"#221A1A\"\n    }\n\n    colors: QtObject {\n        property color colSubtext: m3colors.m3outline\n        property color colLayer0: ColorUtils.transparentize(m3colors.m3background, root.transparency)\n        property color colOnLayer0: m3colors.m3onBackground\n        property color colLayer0Hover: ColorUtils.transparentize(ColorUtils.mix(colLayer0, colOnLayer0, 0.9, root.contentTransparency))\n        property color colLayer0Active: ColorUtils.transparentize(ColorUtils.mix(colLayer0, colOnLayer0, 0.8, root.contentTransparency))\n        property color colLayer1: ColorUtils.transparentize(ColorUtils.mix(m3colors.m3surfaceContainerLow, m3colors.m3background, 0.8), root.contentTransparency);\n        property color colOnLayer1: m3colors.m3onSurfaceVariant;\n        property color colOnLayer1Inactive: ColorUtils.mix(colOnLayer1, colLayer1, 0.45);\n        property color colLayer2: ColorUtils.transparentize(ColorUtils.mix(m3colors.m3surfaceContainer, m3colors.m3surfaceContainerHigh, 0.1), root.contentTransparency)\n        property color colOnLayer2: m3colors.m3onSurface;\n        property color colOnLayer2Disabled: ColorUtils.mix(colOnLayer2, m3colors.m3background, 0.4);\n        property color colLayer3: ColorUtils.transparentize(ColorUtils.mix(m3colors.m3surfaceContainerHigh, m3colors.m3onSurface, 0.96), root.contentTransparency)\n        property color colOnLayer3: m3colors.m3onSurface;\n        property color colLayer1Hover: ColorUtils.transparentize(ColorUtils.mix(colLayer1, colOnLayer1, 0.92), root.contentTransparency)\n        property color colLayer1Active: ColorUtils.transparentize(ColorUtils.mix(colLayer1, colOnLayer1, 0.85), root.contentTransparency);\n        property color colLayer2Hover: ColorUtils.transparentize(ColorUtils.mix(colLayer2, colOnLayer2, 0.90), root.contentTransparency)\n        property color colLayer2Active: ColorUtils.transparentize(ColorUtils.mix(colLayer2, colOnLayer2, 0.80), root.contentTransparency);\n        property color colLayer2Disabled: ColorUtils.transparentize(ColorUtils.mix(colLayer2, m3colors.m3background, 0.8), root.contentTransparency);\n        property color colLayer3Hover: ColorUtils.transparentize(ColorUtils.mix(colLayer3, colOnLayer3, 0.90), root.contentTransparency)\n        property color colLayer3Active: ColorUtils.transparentize(ColorUtils.mix(colLayer3, colOnLayer3, 0.80), root.contentTransparency);\n        property color colPrimary: m3colors.m3primary\n        property color colOnPrimary: m3colors.m3onPrimary\n        property color colPrimaryHover: ColorUtils.mix(colors.colPrimary, colLayer1Hover, 0.87)\n        property color colPrimaryActive: ColorUtils.mix(colors.colPrimary, colLayer1Active, 0.7)\n        property color colPrimaryContainer: m3colors.m3primaryContainer\n        property color colPrimaryContainerHover: ColorUtils.mix(colors.colPrimaryContainer, colLayer1Hover, 0.7)\n        property color colPrimaryContainerActive: ColorUtils.mix(colors.colPrimaryContainer, colLayer1Active, 0.6)\n        property color colOnPrimaryContainer: m3colors.m3onPrimaryContainer\n        property color colSecondary: m3colors.m3secondary\n        property color colSecondaryHover: ColorUtils.mix(m3colors.m3secondary, colLayer1Hover, 0.85)\n        property color colSecondaryActive: ColorUtils.mix(m3colors.m3secondary, colLayer1Active, 0.4)\n        property color colSecondaryContainer: m3colors.m3secondaryContainer\n        property color colSecondaryContainerHover: ColorUtils.mix(m3colors.m3secondaryContainer, colLayer1Hover, 0.6)\n        property color colSecondaryContainerActive: ColorUtils.mix(m3colors.m3secondaryContainer, colLayer1Active, 0.54)\n        property color colOnSecondaryContainer: m3colors.m3onSecondaryContainer\n        property color colSurfaceContainerLow: ColorUtils.transparentize(m3colors.m3surfaceContainerLow, root.contentTransparency)\n        property color colSurfaceContainer: ColorUtils.transparentize(m3colors.m3surfaceContainer, root.contentTransparency)\n        property color colSurfaceContainerHigh: ColorUtils.transparentize(m3colors.m3surfaceContainerHigh, root.contentTransparency)\n        property color colSurfaceContainerHighest: ColorUtils.transparentize(m3colors.m3surfaceContainerHighest, root.contentTransparency)\n        property color colSurfaceContainerHighestHover: ColorUtils.mix(m3colors.m3surfaceContainerHighest, m3colors.m3onSurface, 0.95)\n        property color colSurfaceContainerHighestActive: ColorUtils.mix(m3colors.m3surfaceContainerHighest, m3colors.m3onSurface, 0.85)\n        property color colTooltip: m3colors.darkmode ? ColorUtils.mix(m3colors.m3background, \"#3C4043\", 0.5) : \"#3C4043\" // m3colors.m3inverseSurface in the specs, but the m3 website actually uses #3C4043\n        property color colOnTooltip: \"#F8F9FA\" // m3colors.m3inverseOnSurface in the specs, but the m3 website actually uses this color\n        property color colScrim: ColorUtils.transparentize(m3colors.m3scrim, 0.5)\n        property color colShadow: ColorUtils.transparentize(m3colors.m3shadow, 0.7)\n        property color colOutlineVariant: m3colors.m3outlineVariant\n    }\n\n    rounding: QtObject {\n        property int unsharpen: 2\n        property int unsharpenmore: 6\n        property int verysmall: 8\n        property int small: 12\n        property int normal: 17\n        property int large: 23\n        property int verylarge: 30\n        property int full: 9999\n        property int screenRounding: large\n        property int windowRounding: 18\n    }\n\n    font: QtObject {\n        property QtObject family: QtObject {\n            property string main: \"Rubik\"\n            property string title: \"Gabarito\"\n            property string iconMaterial: \"Material Symbols Rounded, Material Symbols Outlined, Material Symbols Sharp\"\n            property string iconNerd: \"SpaceMono NF\"\n            property string monospace: \"JetBrains Mono NF\"\n            property string reading: \"Readex Pro\"\n        }\n        property QtObject pixelSize: QtObject {\n            property int smallest: 10\n            property int smaller: 13\n            property int small: 15\n            property int normal: 16\n            property int large: 17\n            property int larger: 19\n            property int huge: 22\n            property int hugeass: 23\n            property int title: huge\n        }\n    }\n\n    animationCurves: QtObject {\n        readonly property list<real> expressiveFastSpatial: [0.42, 1.67, 0.21, 0.90, 1, 1] // Default, 350ms\n        readonly property list<real> expressiveDefaultSpatial: [0.38, 1.21, 0.22, 1.00, 1, 1] // Default, 500ms\n        readonly property list<real> expressiveSlowSpatial: [0.39, 1.29, 0.35, 0.98, 1, 1] // Default, 650ms\n        readonly property list<real> expressiveEffects: [0.34, 0.80, 0.34, 1.00, 1, 1] // Default, 200ms\n        readonly property list<real> emphasized: [0.05, 0, 2 / 15, 0.06, 1 / 6, 0.4, 5 / 24, 0.82, 0.25, 1, 1, 1]\n        readonly property list<real> emphasizedFirstHalf: [0.05, 0, 2 / 15, 0.06, 1 / 6, 0.4, 5 / 24, 0.82]\n        readonly property list<real> emphasizedLastHalf: [5 / 24, 0.82, 0.25, 1, 1, 1]\n        readonly property list<real> emphasizedAccel: [0.3, 0, 0.8, 0.15, 1, 1]\n        readonly property list<real> emphasizedDecel: [0.05, 0.7, 0.1, 1, 1, 1]\n        readonly property list<real> standard: [0.2, 0, 0, 1, 1, 1]\n        readonly property list<real> standardAccel: [0.3, 0, 1, 1, 1, 1]\n        readonly property list<real> standardDecel: [0, 0, 0, 1, 1, 1]\n        readonly property real expressiveFastSpatialDuration: 350\n        readonly property real expressiveDefaultSpatialDuration: 500\n        readonly property real expressiveSlowSpatialDuration: 650\n        readonly property real expressiveEffectsDuration: 200\n    }\n\n    animation: QtObject {\n        property QtObject elementMove: QtObject {\n            property int duration: animationCurves.expressiveDefaultSpatialDuration\n            property int type: Easing.BezierSpline\n            property list<real> bezierCurve: animationCurves.expressiveDefaultSpatial\n            property int velocity: 650\n            property Component numberAnimation: Component {\n                NumberAnimation {\n                    duration: root.animation.elementMove.duration\n                    easing.type: root.animation.elementMove.type\n                    easing.bezierCurve: root.animation.elementMove.bezierCurve\n                }\n            }\n            property Component colorAnimation: Component {\n                ColorAnimation {\n                    duration: root.animation.elementMove.duration\n                    easing.type: root.animation.elementMove.type\n                    easing.bezierCurve: root.animation.elementMove.bezierCurve\n                }\n            }\n        }\n        property QtObject elementMoveEnter: QtObject {\n            property int duration: 400\n            property int type: Easing.BezierSpline\n            property list<real> bezierCurve: animationCurves.emphasizedDecel\n            property int velocity: 650\n            property Component numberAnimation: Component {\n                NumberAnimation {\n                    duration: root.animation.elementMoveEnter.duration\n                    easing.type: root.animation.elementMoveEnter.type\n                    easing.bezierCurve: root.animation.elementMoveEnter.bezierCurve\n                }\n            }\n        }\n        property QtObject elementMoveExit: QtObject {\n            property int duration: 200\n            property int type: Easing.BezierSpline\n            property list<real> bezierCurve: animationCurves.emphasizedAccel\n            property int velocity: 650\n            property Component numberAnimation: Component {\n                NumberAnimation {\n                    duration: root.animation.elementMoveExit.duration\n                    easing.type: root.animation.elementMoveExit.type\n                    easing.bezierCurve: root.animation.elementMoveExit.bezierCurve\n                }\n            }\n        }\n        property QtObject elementMoveFast: QtObject {\n            property int duration: animationCurves.expressiveEffectsDuration\n            property int type: Easing.BezierSpline\n            property list<real> bezierCurve: animationCurves.expressiveEffects\n            property int velocity: 850\n            property Component colorAnimation: Component { ColorAnimation {\n                duration: root.animation.elementMoveFast.duration\n                easing.type: root.animation.elementMoveFast.type\n                easing.bezierCurve: root.animation.elementMoveFast.bezierCurve\n            }}\n            property Component numberAnimation: Component { NumberAnimation {\n                    duration: root.animation.elementMoveFast.duration\n                    easing.type: root.animation.elementMoveFast.type\n                    easing.bezierCurve: root.animation.elementMoveFast.bezierCurve\n            }}\n        }\n\n        property QtObject clickBounce: QtObject {\n            property int duration: 200\n            property int type: Easing.BezierSpline\n            property list<real> bezierCurve: animationCurves.expressiveFastSpatial\n            property int velocity: 850\n            property Component numberAnimation: Component { NumberAnimation {\n                    duration: root.animation.clickBounce.duration\n                    easing.type: root.animation.clickBounce.type\n                    easing.bezierCurve: root.animation.clickBounce.bezierCurve\n            }}\n        }\n        property QtObject scroll: QtObject {\n            property int duration: 400\n            property int type: Easing.BezierSpline\n            property list<real> bezierCurve: animationCurves.standardDecel\n        }\n        property QtObject menuDecel: QtObject {\n            property int duration: 350\n            property int type: Easing.OutExpo\n        }\n    }\n\n    sizes: QtObject {\n        property real barHeight: 40\n        property real barCenterSideModuleWidth: ConfigOptions?.bar.verbose ? 360 : 140\n        property real barCenterSideModuleWidthShortened: 280\n        property real barCenterSideModuleWidthHellaShortened: 190\n        property real barShortenScreenWidthThreshold: 1200 // Shorten if screen width is at most this value\n        property real barHellaShortenScreenWidthThreshold: 1000 // Shorten even more...\n        property real sidebarWidth: 460\n        property real sidebarWidthExtended: 750\n        property real osdWidth: 200\n        property real mediaControlsWidth: 440\n        property real mediaControlsHeight: 160\n        property real notificationPopupWidth: 410\n        property real searchWidthCollapsed: 260\n        property real searchWidth: 450\n        property real hyprlandGapsOut: 5\n        property real elevationMargin: 10\n        property real fabShadowRadius: 5\n        property real fabHoveredShadowRadius: 7\n    }\n\n    syntaxHighlightingTheme: Appearance.m3colors.darkmode ? \"Monokai\" : \"ayu Light\"\n}\n", "modifiedCode": "import QtQuick\nimport Quickshell\nimport \"root:/modules/common/functions/color_utils.js\" as ColorUtils\npragma Singleton\npragma ComponentBehavior: Bo<PERSON>\n\nSingleton {\n    id: root\n    property QtObject m3colors\n    property QtObject animation\n    property QtObject animationCurves\n    property QtObject colors\n    property QtObject rounding\n    property QtObject font\n    property QtObject sizes\n    property string syntaxHighlightingTheme\n\n    // Extremely conservative transparency values for consistency and readability\n    property real transparency: ConfigOptions?.appearance.transparency ? (m3colors.darkmode ? 0.1 : 0.07) : 0\n    property real contentTransparency: ConfigOptions?.appearance.transparency ? (m3colors.darkmode ? 0.55 : 0.55) : 0\n\n    m3colors: QtObject {\n        property bool darkmode: false\n        property bool transparent: false\n        property color m3primary_paletteKeyColor: \"#91689E\"\n        property color m3secondary_paletteKeyColor: \"#837186\"\n        property color m3tertiary_paletteKeyColor: \"#9D6A67\"\n        property color m3neutral_paletteKeyColor: \"#7C757B\"\n        property color m3neutral_variant_paletteKeyColor: \"#7D747D\"\n        property color m3background: \"#161217\"\n        property color m3onBackground: \"#EAE0E7\"\n        property color m3surface: \"#161217\"\n        property color m3surfaceDim: \"#161217\"\n        property color m3surfaceBright: \"#3D373D\"\n        property color m3surfaceContainerLowest: \"#110D12\"\n        property color m3surfaceContainerLow: \"#1F1A1F\"\n        property color m3surfaceContainer: \"#231E23\"\n        property color m3surfaceContainerHigh: \"#2D282E\"\n        property color m3surfaceContainerHighest: \"#383339\"\n        property color m3onSurface: \"#EAE0E7\"\n        property color m3surfaceVariant: \"#4C444D\"\n        property color m3onSurfaceVariant: \"#CFC3CD\"\n        property color m3inverseSurface: \"#EAE0E7\"\n        property color m3inverseOnSurface: \"#342F34\"\n        property color m3outline: \"#988E97\"\n        property color m3outlineVariant: \"#4C444D\"\n        property color m3shadow: \"#000000\"\n        property color m3scrim: \"#000000\"\n        property color m3surfaceTint: \"#E5B6F2\"\n        property color m3primary: \"#E5B6F2\"\n        property color m3onPrimary: \"#452152\"\n        property color m3primaryContainer: \"#5D386A\"\n        property color m3onPrimaryContainer: \"#F9D8FF\"\n        property color m3inversePrimary: \"#775084\"\n        property color m3secondary: \"#D5C0D7\"\n        property color m3onSecondary: \"#392C3D\"\n        property color m3secondaryContainer: \"#534457\"\n        property color m3onSecondaryContainer: \"#F2DCF3\"\n        property color m3tertiary: \"#F5B7B3\"\n        property color m3onTertiary: \"#4C2523\"\n        property color m3tertiaryContainer: \"#BA837F\"\n        property color m3onTertiaryContainer: \"#000000\"\n        property color m3error: \"#FFB4AB\"\n        property color m3onError: \"#690005\"\n        property color m3errorContainer: \"#93000A\"\n        property color m3onErrorContainer: \"#FFDAD6\"\n        property color m3primaryFixed: \"#F9D8FF\"\n        property color m3primaryFixedDim: \"#E5B6F2\"\n        property color m3onPrimaryFixed: \"#2E0A3C\"\n        property color m3onPrimaryFixedVariant: \"#5D386A\"\n        property color m3secondaryFixed: \"#F2DCF3\"\n        property color m3secondaryFixedDim: \"#D5C0D7\"\n        property color m3onSecondaryFixed: \"#241727\"\n        property color m3onSecondaryFixedVariant: \"#514254\"\n        property color m3tertiaryFixed: \"#FFDAD7\"\n        property color m3tertiaryFixedDim: \"#F5B7B3\"\n        property color m3onTertiaryFixed: \"#331110\"\n        property color m3onTertiaryFixedVariant: \"#663B39\"\n        property color m3success: \"#B5CCBA\"\n        property color m3onSuccess: \"#213528\"\n        property color m3successContainer: \"#374B3E\"\n        property color m3onSuccessContainer: \"#D1E9D6\"\n        property color term0: \"#EDE4E4\"\n        property color term1: \"#B52755\"\n        property color term2: \"#A97363\"\n        property color term3: \"#AF535D\"\n        property color term4: \"#A67F7C\"\n        property color term5: \"#B2416B\"\n        property color term6: \"#8D76AD\"\n        property color term7: \"#272022\"\n        property color term8: \"#0E0D0D\"\n        property color term9: \"#B52755\"\n        property color term10: \"#A97363\"\n        property color term11: \"#AF535D\"\n        property color term12: \"#A67F7C\"\n        property color term13: \"#B2416B\"\n        property color term14: \"#8D76AD\"\n        property color term15: \"#221A1A\"\n    }\n\n    colors: QtObject {\n        property color colSubtext: m3colors.m3outline\n        property color colLayer0: ColorUtils.transparentize(m3colors.m3background, root.transparency)\n        property color colOnLayer0: m3colors.m3onBackground\n        property color colLayer0Hover: ColorUtils.transparentize(ColorUtils.mix(colLayer0, colOnLayer0, 0.9, root.contentTransparency))\n        property color colLayer0Active: ColorUtils.transparentize(ColorUtils.mix(colLayer0, colOnLayer0, 0.8, root.contentTransparency))\n        property color colLayer1: ColorUtils.transparentize(ColorUtils.mix(m3colors.m3surfaceContainerLow, m3colors.m3background, 0.8), root.contentTransparency);\n        property color colOnLayer1: m3colors.m3onSurfaceVariant;\n        property color colOnLayer1Inactive: ColorUtils.mix(colOnLayer1, colLayer1, 0.45);\n        property color colLayer2: ColorUtils.transparentize(ColorUtils.mix(m3colors.m3surfaceContainer, m3colors.m3surfaceContainerHigh, 0.1), root.contentTransparency)\n        property color colOnLayer2: m3colors.m3onSurface;\n        property color colOnLayer2Disabled: ColorUtils.mix(colOnLayer2, m3colors.m3background, 0.4);\n        property color colLayer3: ColorUtils.transparentize(ColorUtils.mix(m3colors.m3surfaceContainerHigh, m3colors.m3onSurface, 0.96), root.contentTransparency)\n        property color colOnLayer3: m3colors.m3onSurface;\n        property color colLayer1Hover: ColorUtils.transparentize(ColorUtils.mix(colLayer1, colOnLayer1, 0.92), root.contentTransparency)\n        property color colLayer1Active: ColorUtils.transparentize(ColorUtils.mix(colLayer1, colOnLayer1, 0.85), root.contentTransparency);\n        property color colLayer2Hover: ColorUtils.transparentize(ColorUtils.mix(colLayer2, colOnLayer2, 0.90), root.contentTransparency)\n        property color colLayer2Active: ColorUtils.transparentize(ColorUtils.mix(colLayer2, colOnLayer2, 0.80), root.contentTransparency);\n        property color colLayer2Disabled: ColorUtils.transparentize(ColorUtils.mix(colLayer2, m3colors.m3background, 0.8), root.contentTransparency);\n        property color colLayer3Hover: ColorUtils.transparentize(ColorUtils.mix(colLayer3, colOnLayer3, 0.90), root.contentTransparency)\n        property color colLayer3Active: ColorUtils.transparentize(ColorUtils.mix(colLayer3, colOnLayer3, 0.80), root.contentTransparency);\n        property color colPrimary: m3colors.m3primary\n        property color colOnPrimary: m3colors.m3onPrimary\n        property color colPrimaryHover: ColorUtils.mix(colors.colPrimary, colLayer1Hover, 0.87)\n        property color colPrimaryActive: ColorUtils.mix(colors.colPrimary, colLayer1Active, 0.7)\n        property color colPrimaryContainer: m3colors.m3primaryContainer\n        property color colPrimaryContainerHover: ColorUtils.mix(colors.colPrimaryContainer, colLayer1Hover, 0.7)\n        property color colPrimaryContainerActive: ColorUtils.mix(colors.colPrimaryContainer, colLayer1Active, 0.6)\n        property color colOnPrimaryContainer: m3colors.m3onPrimaryContainer\n        property color colSecondary: m3colors.m3secondary\n        property color colSecondaryHover: ColorUtils.mix(m3colors.m3secondary, colLayer1Hover, 0.85)\n        property color colSecondaryActive: ColorUtils.mix(m3colors.m3secondary, colLayer1Active, 0.4)\n        property color colSecondaryContainer: m3colors.m3secondaryContainer\n        property color colSecondaryContainerHover: ColorUtils.mix(m3colors.m3secondaryContainer, colLayer1Hover, 0.6)\n        property color colSecondaryContainerActive: ColorUtils.mix(m3colors.m3secondaryContainer, colLayer1Active, 0.54)\n        property color colOnSecondaryContainer: m3colors.m3onSecondaryContainer\n        property color colSurfaceContainerLow: ColorUtils.transparentize(m3colors.m3surfaceContainerLow, root.contentTransparency)\n        property color colSurfaceContainer: ColorUtils.transparentize(m3colors.m3surfaceContainer, root.contentTransparency)\n        property color colSurfaceContainerHigh: ColorUtils.transparentize(m3colors.m3surfaceContainerHigh, root.contentTransparency)\n        property color colSurfaceContainerHighest: ColorUtils.transparentize(m3colors.m3surfaceContainerHighest, root.contentTransparency)\n        property color colSurfaceContainerHighestHover: ColorUtils.mix(m3colors.m3surfaceContainerHighest, m3colors.m3onSurface, 0.95)\n        property color colSurfaceContainerHighestActive: ColorUtils.mix(m3colors.m3surfaceContainerHighest, m3colors.m3onSurface, 0.85)\n        property color colTooltip: m3colors.darkmode ? ColorUtils.mix(m3colors.m3background, \"#3C4043\", 0.5) : \"#3C4043\" // m3colors.m3inverseSurface in the specs, but the m3 website actually uses #3C4043\n        property color colOnTooltip: \"#F8F9FA\" // m3colors.m3inverseOnSurface in the specs, but the m3 website actually uses this color\n        property color colScrim: ColorUtils.transparentize(m3colors.m3scrim, 0.5)\n        property color colShadow: ColorUtils.transparentize(m3colors.m3shadow, 0.7)\n        property color colOutlineVariant: m3colors.m3outlineVariant\n    }\n\n    rounding: QtObject {\n        property int unsharpen: 2\n        property int unsharpenmore: 6\n        property int verysmall: 8\n        property int small: 12\n        property int normal: 17\n        property int large: 23\n        property int verylarge: 30\n        property int full: 9999\n        property int screenRounding: large\n        property int windowRounding: 18\n    }\n\n    font: QtObject {\n        property QtObject family: QtObject {\n            property string main: \"Rubik\"\n            property string title: \"Gabarito\"\n            property string iconMaterial: \"Material Symbols Rounded\"\n            property string iconNerd: \"SpaceMono NF\"\n            property string monospace: \"JetBrains Mono NF\"\n            property string reading: \"Readex Pro\"\n        }\n        property QtObject pixelSize: QtObject {\n            property int smallest: 10\n            property int smaller: 13\n            property int small: 15\n            property int normal: 16\n            property int large: 17\n            property int larger: 19\n            property int huge: 22\n            property int hugeass: 23\n            property int title: huge\n        }\n    }\n\n    animationCurves: QtObject {\n        readonly property list<real> expressiveFastSpatial: [0.42, 1.67, 0.21, 0.90, 1, 1] // Default, 350ms\n        readonly property list<real> expressiveDefaultSpatial: [0.38, 1.21, 0.22, 1.00, 1, 1] // Default, 500ms\n        readonly property list<real> expressiveSlowSpatial: [0.39, 1.29, 0.35, 0.98, 1, 1] // Default, 650ms\n        readonly property list<real> expressiveEffects: [0.34, 0.80, 0.34, 1.00, 1, 1] // Default, 200ms\n        readonly property list<real> emphasized: [0.05, 0, 2 / 15, 0.06, 1 / 6, 0.4, 5 / 24, 0.82, 0.25, 1, 1, 1]\n        readonly property list<real> emphasizedFirstHalf: [0.05, 0, 2 / 15, 0.06, 1 / 6, 0.4, 5 / 24, 0.82]\n        readonly property list<real> emphasizedLastHalf: [5 / 24, 0.82, 0.25, 1, 1, 1]\n        readonly property list<real> emphasizedAccel: [0.3, 0, 0.8, 0.15, 1, 1]\n        readonly property list<real> emphasizedDecel: [0.05, 0.7, 0.1, 1, 1, 1]\n        readonly property list<real> standard: [0.2, 0, 0, 1, 1, 1]\n        readonly property list<real> standardAccel: [0.3, 0, 1, 1, 1, 1]\n        readonly property list<real> standardDecel: [0, 0, 0, 1, 1, 1]\n        readonly property real expressiveFastSpatialDuration: 350\n        readonly property real expressiveDefaultSpatialDuration: 500\n        readonly property real expressiveSlowSpatialDuration: 650\n        readonly property real expressiveEffectsDuration: 200\n    }\n\n    animation: QtObject {\n        property QtObject elementMove: QtObject {\n            property int duration: animationCurves.expressiveDefaultSpatialDuration\n            property int type: Easing.BezierSpline\n            property list<real> bezierCurve: animationCurves.expressiveDefaultSpatial\n            property int velocity: 650\n            property Component numberAnimation: Component {\n                NumberAnimation {\n                    duration: root.animation.elementMove.duration\n                    easing.type: root.animation.elementMove.type\n                    easing.bezierCurve: root.animation.elementMove.bezierCurve\n                }\n            }\n            property Component colorAnimation: Component {\n                ColorAnimation {\n                    duration: root.animation.elementMove.duration\n                    easing.type: root.animation.elementMove.type\n                    easing.bezierCurve: root.animation.elementMove.bezierCurve\n                }\n            }\n        }\n        property QtObject elementMoveEnter: QtObject {\n            property int duration: 400\n            property int type: Easing.BezierSpline\n            property list<real> bezierCurve: animationCurves.emphasizedDecel\n            property int velocity: 650\n            property Component numberAnimation: Component {\n                NumberAnimation {\n                    duration: root.animation.elementMoveEnter.duration\n                    easing.type: root.animation.elementMoveEnter.type\n                    easing.bezierCurve: root.animation.elementMoveEnter.bezierCurve\n                }\n            }\n        }\n        property QtObject elementMoveExit: QtObject {\n            property int duration: 200\n            property int type: Easing.BezierSpline\n            property list<real> bezierCurve: animationCurves.emphasizedAccel\n            property int velocity: 650\n            property Component numberAnimation: Component {\n                NumberAnimation {\n                    duration: root.animation.elementMoveExit.duration\n                    easing.type: root.animation.elementMoveExit.type\n                    easing.bezierCurve: root.animation.elementMoveExit.bezierCurve\n                }\n            }\n        }\n        property QtObject elementMoveFast: QtObject {\n            property int duration: animationCurves.expressiveEffectsDuration\n            property int type: Easing.BezierSpline\n            property list<real> bezierCurve: animationCurves.expressiveEffects\n            property int velocity: 850\n            property Component colorAnimation: Component { ColorAnimation {\n                duration: root.animation.elementMoveFast.duration\n                easing.type: root.animation.elementMoveFast.type\n                easing.bezierCurve: root.animation.elementMoveFast.bezierCurve\n            }}\n            property Component numberAnimation: Component { NumberAnimation {\n                    duration: root.animation.elementMoveFast.duration\n                    easing.type: root.animation.elementMoveFast.type\n                    easing.bezierCurve: root.animation.elementMoveFast.bezierCurve\n            }}\n        }\n\n        property QtObject clickBounce: QtObject {\n            property int duration: 200\n            property int type: Easing.BezierSpline\n            property list<real> bezierCurve: animationCurves.expressiveFastSpatial\n            property int velocity: 850\n            property Component numberAnimation: Component { NumberAnimation {\n                    duration: root.animation.clickBounce.duration\n                    easing.type: root.animation.clickBounce.type\n                    easing.bezierCurve: root.animation.clickBounce.bezierCurve\n            }}\n        }\n        property QtObject scroll: QtObject {\n            property int duration: 400\n            property int type: Easing.BezierSpline\n            property list<real> bezierCurve: animationCurves.standardDecel\n        }\n        property QtObject menuDecel: QtObject {\n            property int duration: 350\n            property int type: Easing.OutExpo\n        }\n    }\n\n    sizes: QtObject {\n        property real barHeight: 40\n        property real barCenterSideModuleWidth: ConfigOptions?.bar.verbose ? 360 : 140\n        property real barCenterSideModuleWidthShortened: 280\n        property real barCenterSideModuleWidthHellaShortened: 190\n        property real barShortenScreenWidthThreshold: 1200 // Shorten if screen width is at most this value\n        property real barHellaShortenScreenWidthThreshold: 1000 // Shorten even more...\n        property real sidebarWidth: 460\n        property real sidebarWidthExtended: 750\n        property real osdWidth: 200\n        property real mediaControlsWidth: 440\n        property real mediaControlsHeight: 160\n        property real notificationPopupWidth: 410\n        property real searchWidthCollapsed: 260\n        property real searchWidth: 450\n        property real hyprlandGapsOut: 5\n        property real elevationMargin: 10\n        property real fabShadowRadius: 5\n        property real fabHoveredShadowRadius: 7\n    }\n\n    syntaxHighlightingTheme: Appearance.m3colors.darkmode ? \"Monokai\" : \"ayu Light\"\n}\n"}