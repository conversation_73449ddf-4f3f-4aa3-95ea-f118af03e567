{"path": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/onScreenKeyboard/OnScreenKeyboard.qml"}, "originalCode": "import \"root:/\"\nimport \"root:/services\"\nimport \"root:/modules/common\"\nimport \"root:/modules/common/widgets\"\nimport \"root:/modules/common/functions/color_utils.js\" as ColorUtils\nimport QtQuick\nimport QtQuick.Controls\nimport QtQuick.Effects\nimport QtQuick.Layouts\nimport Quickshell.Io\nimport Quickshell\nimport Quickshell.Widgets\nimport Quickshell.Wayland\nimport Quickshell.Hyprland\n\nScope { // Scope\n    id: root\n    property bool pinned: ConfigOptions?.osk.pinnedOnStartup ?? false\n\n    component OskControlButton: GroupButton { // Pin button\n        baseWidth: 40\n        baseHeight: 40\n        clickedWidth: baseWidth\n        clickedHeight: baseHeight + 20\n        buttonRadius: Appearance.rounding.normal\n    }\n\n    Loader {\n        id: oskLoader\n        active: false\n        onActiveChanged: {\n            if (!oskLoader.active) {\n                Ydotool.releaseAllKeys();\n            }\n        }\n        \n        sourceComponent: PanelWindow { // Window\n            id: oskRoot\n            visible: oskLoader.active\n\n            anchors {\n                bottom: true\n                left: true\n                right: true\n            }\n\n            function hide() {\n                oskLoader.active = false\n            }\n            exclusiveZone: root.pinned ? implicitHeight - Appearance.sizes.hyprlandGapsOut : 0\n            implicitWidth: oskBackground.width + Appearance.sizes.elevationMargin * 2\n            implicitHeight: oskBackground.height + Appearance.sizes.elevationMargin * 2\n            WlrLayershell.namespace: \"quickshell:osk\"\n            WlrLayershell.layer: WlrLayer.Overlay\n            // Hyprland 0.49: Focus is always exclusive and setting this breaks mouse focus grab\n            // WlrLayershell.keyboardFocus: WlrKeyboardFocus.Exclusive\n            color: \"transparent\"\n\n            mask: Region {\n                item: oskBackground\n            }\n\n\n            // Background\n            StyledRectangularShadow {\n                target: oskBackground\n            }\n            Rectangle {\n                id: oskBackground\n                anchors.centerIn: parent\n                color: Appearance.colors.colLayer0\n                radius: Appearance.rounding.windowRounding\n                property real padding: 10\n                implicitWidth: oskRowLayout.implicitWidth + padding * 2\n                implicitHeight: oskRowLayout.implicitHeight + padding * 2\n\n                Keys.onPressed: (event) => { // Esc to close\n                    if (event.key === Qt.Key_Escape) {\n                        oskRoot.hide()\n                    }\n                }\n\n                RowLayout {\n                    id: oskRowLayout\n                    anchors.centerIn: parent\n                    spacing: 5\n                    VerticalButtonGroup {\n                        OskControlButton { // Pin button\n                            toggled: root.pinned\n                            onClicked: root.pinned = !root.pinned\n                            contentItem: MaterialSymbol {\n                                text: \"push_pin\"\n                                horizontalAlignment: Text.AlignHCenter\n                                iconSize: Appearance.font.pixelSize.larger\n                                color: root.pinned ? Appearance.m3colors.m3onPrimary : Appearance.colors.colOnLayer0\n                            }\n                        }\n                        OskControlButton {\n                            onClicked: () => {\n                                oskRoot.hide()\n                            }\n                            contentItem: MaterialSymbol {\n                                horizontalAlignment: Text.AlignHCenter\n                                text: \"keyboard_hide\"\n                                iconSize: Appearance.font.pixelSize.larger\n                            }\n                        }\n                    }\n                    Rectangle {\n                        Layout.topMargin: 20\n                        Layout.bottomMargin: 20\n                        Layout.fillHeight: true\n                        implicitWidth: 1\n                        color: Appearance.colors.colOutlineVariant\n                    }\n                    OskContent {\n                        id: oskContent\n                        Layout.fillWidth: true\n                    }\n                }\n            }\n\n        }\n    }\n\n    IpcHandler {\n        target: \"osk\"\n\n        function toggle(): void {\n            oskLoader.active = !oskLoader.active\n        }\n\n        function close(): void {\n            oskLoader.active = false\n        }\n\n        function open(): void {\n            oskLoader.active = true\n        }\n    }\n\n    GlobalShortcut {\n        name: \"oskToggle\"\n        description: qsTr(\"按下时切换屏幕键盘\")\n\n        onPressed: {\n            oskLoader.active = !oskLoader.active;\n        }\n    }\n\n    GlobalShortcut {\n        name: \"oskOpen\"\n        description: qsTr(\"按下时打开屏幕键盘\")\n\n        onPressed: {\n            oskLoader.active = true;\n        }\n    }\n\n    GlobalShortcut {\n        name: \"oskClose\"\n        description: qsTr(\"按下时关闭屏幕键盘\")\n\n        onPressed: {\n            oskLoader.active = false;\n        }\n    }\n\n}\n", "modifiedCode": "import \"root:/\"\nimport \"root:/services\"\nimport \"root:/modules/common\"\nimport \"root:/modules/common/widgets\"\nimport \"root:/modules/common/functions/color_utils.js\" as ColorUtils\nimport QtQuick\nimport QtQuick.Controls\nimport QtQuick.Effects\nimport QtQuick.Layouts\nimport Quickshell.Io\nimport Quickshell\nimport Quickshell.Widgets\nimport Quickshell.Wayland\nimport Quickshell.Hyprland\n\nScope { // Scope\n    id: root\n    property bool pinned: ConfigOptions?.osk.pinnedOnStartup ?? false\n\n    component OskControlButton: GroupButton { // Pin button\n        baseWidth: 40\n        baseHeight: 40\n        clickedWidth: baseWidth\n        clickedHeight: baseHeight + 20\n        buttonRadius: Appearance.rounding.normal\n    }\n\n    Loader {\n        id: oskLoader\n        active: false\n        onActiveChanged: {\n            if (!oskLoader.active) {\n                Ydotool.releaseAllKeys();\n            }\n        }\n        \n        sourceComponent: PanelWindow { // Window\n            id: oskRoot\n            visible: oskLoader.active\n\n            anchors {\n                bottom: true\n                left: true\n                right: true\n            }\n\n            function hide() {\n                oskLoader.active = false\n            }\n            exclusiveZone: root.pinned ? implicitHeight - Appearance.sizes.hyprlandGapsOut : 0\n            implicitWidth: oskBackground.width + Appearance.sizes.elevationMargin * 2\n            implicitHeight: oskBackground.height + Appearance.sizes.elevationMargin * 2\n            WlrLayershell.namespace: \"quickshell:osk\"\n            WlrLayershell.layer: WlrLayer.Overlay\n            // Hyprland 0.49: Focus is always exclusive and setting this breaks mouse focus grab\n            // WlrLayershell.keyboardFocus: WlrKeyboardFocus.Exclusive\n            color: \"transparent\"\n\n            mask: Region {\n                item: oskBackground\n            }\n\n\n            // Background\n            StyledRectangularShadow {\n                target: oskBackground\n            }\n            Rectangle {\n                id: oskBackground\n                anchors.centerIn: parent\n                color: Appearance.colors.colLayer0\n                radius: Appearance.rounding.windowRounding\n                property real padding: 10\n                implicitWidth: oskRowLayout.implicitWidth + padding * 2\n                implicitHeight: oskRowLayout.implicitHeight + padding * 2\n\n                Keys.onPressed: (event) => { // Esc to close\n                    if (event.key === Qt.Key_Escape) {\n                        oskRoot.hide()\n                    }\n                }\n\n                RowLayout {\n                    id: oskRowLayout\n                    anchors.centerIn: parent\n                    spacing: 5\n                    VerticalButtonGroup {\n                        OskControlButton { // Pin button\n                            toggled: root.pinned\n                            onClicked: root.pinned = !root.pinned\n                            contentItem: MaterialSymbol {\n                                text: \"keep\"\n                                horizontalAlignment: Text.AlignHCenter\n                                iconSize: Appearance.font.pixelSize.larger\n                                color: root.pinned ? Appearance.m3colors.m3onPrimary : Appearance.colors.colOnLayer0\n                            }\n                        }\n                        OskControlButton {\n                            onClicked: () => {\n                                oskRoot.hide()\n                            }\n                            contentItem: MaterialSymbol {\n                                horizontalAlignment: Text.AlignHCenter\n                                text: \"keyboard_hide\"\n                                iconSize: Appearance.font.pixelSize.larger\n                            }\n                        }\n                    }\n                    Rectangle {\n                        Layout.topMargin: 20\n                        Layout.bottomMargin: 20\n                        Layout.fillHeight: true\n                        implicitWidth: 1\n                        color: Appearance.colors.colOutlineVariant\n                    }\n                    OskContent {\n                        id: oskContent\n                        Layout.fillWidth: true\n                    }\n                }\n            }\n\n        }\n    }\n\n    IpcHandler {\n        target: \"osk\"\n\n        function toggle(): void {\n            oskLoader.active = !oskLoader.active\n        }\n\n        function close(): void {\n            oskLoader.active = false\n        }\n\n        function open(): void {\n            oskLoader.active = true\n        }\n    }\n\n    GlobalShortcut {\n        name: \"oskToggle\"\n        description: qsTr(\"Toggles on screen keyboard on press\")\n\n        onPressed: {\n            oskLoader.active = !oskLoader.active;\n        }\n    }\n\n    GlobalShortcut {\n        name: \"oskOpen\"\n        description: qsTr(\"Opens on screen keyboard on press\")\n\n        onPressed: {\n            oskLoader.active = true;\n        }\n    }\n\n    GlobalShortcut {\n        name: \"oskClose\"\n        description: qsTr(\"Closes on screen keyboard on press\")\n\n        onPressed: {\n            oskLoader.active = false;\n        }\n    }\n\n}\n"}