{"path": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/common/widgets/MaterialSymbol.qml"}, "originalCode": "import \"root:/modules/common/\"\nimport QtQuick\nimport QtQuick.Layouts\n\nText {\n    id: root\n    property real iconSize: Appearance?.font.pixelSize.small ?? 16\n    property real fill: 0\n    property real truncatedFill: Math.round(fill * 100) / 100 // Reduce memory consumption spikes from constant font remapping\n    renderType: Text.NativeRendering\n    font {\n        hintingPreference: Font.PreferFullHinting\n        family: Appearance?.font.family.iconMaterial ?? \"Material Symbols Rounded\"\n        pixelSize: iconSize\n        weight: Font.Normal + (Font.DemiBold - Font.Normal) * fill\n    }\n    verticalAlignment: Text.AlignVCenter\n    color: Appearance.m3colors.m3onBackground\n\n    // Behavior on fill {\n    //     NumberAnimation {\n    //         duration: Appearance?.animation.elementMoveFast.duration ?? 200\n    //         easing.type: Appearance?.animation.elementMoveFast.type ?? Easing.BezierSpline\n    //         easing.bezierCurve: Appearance?.animation.elementMoveFast.bezierCurve ?? [0.34, 0.80, 0.34, 1.00, 1, 1]\n    //     }\n    // }\n\n    // Try to set variable axes, but gracefully handle if not supported\n    Component.onCompleted: {\n        try {\n            font.variableAxes = {\n                \"FILL\": truncatedFill,\n                \"opsz\": iconSize,\n            }\n        } catch (e) {\n            console.warn(\"Variable font axes not supported, using fallback font weight\")\n        }\n    }\n\n    // Update variable axes when properties change\n    onTruncatedFillChanged: {\n        try {\n            font.variableAxes = {\n                \"FILL\": truncatedFill,\n                \"opsz\": iconSize,\n            }\n        } catch (e) {\n            // Fallback: use font weight instead of FILL axis\n            font.weight = Font.Normal + (Font.Bold - Font.Normal) * fill\n        }\n    }\n\n    onIconSizeChanged: {\n        try {\n            font.variableAxes = {\n                \"FILL\": truncatedFill,\n                \"opsz\": iconSize,\n            }\n        } catch (e) {\n            // Variable axes not supported, continue with regular font properties\n        }\n    }\n}\n", "modifiedCode": "import \"root:/modules/common/\"\nimport QtQuick\nimport QtQuick.Layouts\n\nText {\n    id: root\n    property real iconSize: Appearance?.font.pixelSize.small ?? 16\n    property real fill: 0\n    property real truncatedFill: Math.round(fill * 100) / 100 // Reduce memory consumption spikes from constant font remapping\n    renderType: Text.NativeRendering\n    font {\n        hintingPreference: Font.PreferFullHinting\n        family: Appearance?.font.family.iconMaterial ?? \"Material Symbols Rounded\"\n        pixelSize: iconSize\n        weight: Font.Normal + (Font.DemiBold - Font.Normal) * fill\n    }\n    verticalAlignment: Text.AlignVCenter\n    color: Appearance.m3colors.m3onBackground\n\n    // Behavior on fill {\n    //     NumberAnimation {\n    //         duration: Appearance?.animation.elementMoveFast.duration ?? 200\n    //         easing.type: Appearance?.animation.elementMoveFast.type ?? Easing.BezierSpline\n    //         easing.bezierCurve: Appearance?.animation.elementMoveFast.bezierCurve ?? [0.34, 0.80, 0.34, 1.00, 1, 1]\n    //     }\n    // }\n\n    font.variableAxes: { \n        \"FILL\": truncatedFill,\n        // \"wght\": font.weight,\n        // \"GRAD\": 0,\n        \"opsz\": iconSize,\n    }\n}\n"}