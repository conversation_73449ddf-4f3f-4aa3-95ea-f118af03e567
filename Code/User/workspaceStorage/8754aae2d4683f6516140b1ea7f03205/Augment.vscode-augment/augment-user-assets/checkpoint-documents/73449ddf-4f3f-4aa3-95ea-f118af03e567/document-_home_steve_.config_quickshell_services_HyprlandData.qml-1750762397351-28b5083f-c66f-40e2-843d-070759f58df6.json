{"path": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/services/HyprlandData.qml"}, "originalCode": "pragma Singleton\npragma ComponentBehavior: Bound\n\nimport QtQuick\nimport Quickshell\nimport Quickshell.Io\nimport Quickshell.Wayland\nimport Quickshell.Hyprland\n\n/**\n * Provides access to some Hyprland data not available in Quickshell.Hyprland.\n */\nSingleton {\n    id: root\n    property var windowList: []\n    property var addresses: []\n    property var windowByAddress: ({})\n    property var monitors: []\n\n    function updateWindowList() {\n        getClients.running = true\n        getMonitors.running = true\n    }\n\n    Component.onCompleted: {\n        updateWindowList()\n    }\n\n    Connections {\n        target: Hyprland\n\n        function onRawEvent(event) {\n            // Filter out redundant old v1 events for the same thing\n            if(event.name in [\n                \"activewindow\", \"focusedmon\", \"monitoradded\", \n                \"createworkspace\", \"destroyworkspace\", \"moveworkspace\", \n                \"activespecial\", \"movewindow\", \"windowtitle\"\n            ]) return ;\n            updateWindowList()\n        }\n    }\n\n    Process {\n        id: getClients\n        command: [\"bash\", \"-c\", \"hyprctl clients -j | jq -c\"]\n        stdout: SplitParser {\n            onRead: (data) => {\n                try {\n                    if (!data || data.trim() === \"\") {\n                        console.warn(\"[HyprlandData] Empty window data received\")\n                        return\n                    }\n                    root.windowList = JSON.parse(data)\n                    let tempWinByAddress = {}\n                    for (var i = 0; i < root.windowList.length; ++i) {\n                        var win = root.windowList[i]\n                        tempWinByAddress[win.address] = win\n                    }\n                    root.windowByAddress = tempWinByAddress\n                    root.addresses = root.windowList.map((win) => win.address)\n                } catch (error) {\n                    console.error(\"[HyprlandData] Failed to parse window data:\", error.message)\n                    console.error(\"[HyprlandData] Data was:\", data)\n                }\n            }\n        }\n    }\n    Process {\n        id: getMonitors\n        command: [\"bash\", \"-c\", \"hyprctl monitors -j | jq -c\"]\n        stdout: SplitParser {\n            onRead: (data) => {\n                try {\n                    if (!data || data.trim() === \"\") {\n                        console.warn(\"[HyprlandData] Empty monitor data received\")\n                        return\n                    }\n                    root.monitors = JSON.parse(data)\n                } catch (error) {\n                    console.error(\"[HyprlandData] Failed to parse monitor data:\", error.message)\n                    console.error(\"[HyprlandData] Data was:\", data)\n                }\n            }\n        }\n    }\n}\n\n", "modifiedCode": "pragma Singleton\npragma ComponentBehavior: Bound\n\nimport QtQuick\nimport Quickshell\nimport Quickshell.Io\nimport Quickshell.Wayland\nimport Quickshell.Hyprland\n\n/**\n * Provides access to some Hyprland data not available in Quickshell.Hyprland.\n */\nSingleton {\n    id: root\n    property var windowList: []\n    property var addresses: []\n    property var windowByAddress: ({})\n    property var monitors: []\n\n    function updateWindowList() {\n        getClients.running = true\n        getMonitors.running = true\n    }\n\n    Component.onCompleted: {\n        updateWindowList()\n    }\n\n    Connections {\n        target: Hyprland\n\n        function onRawEvent(event) {\n            // Filter out redundant old v1 events for the same thing\n            if(event.name in [\n                \"activewindow\", \"focusedmon\", \"monitoradded\", \n                \"createworkspace\", \"destroyworkspace\", \"moveworkspace\", \n                \"activespecial\", \"movewindow\", \"windowtitle\"\n            ]) return ;\n            updateWindowList()\n        }\n    }\n\n    Process {\n        id: getClients\n        command: [\"bash\", \"-c\", \"hyprctl clients -j | jq -c\"]\n        stdout: SplitParser {\n            onRead: (data) => {\n                try {\n                    if (!data || data.trim() === \"\") {\n                        console.warn(\"[HyprlandData] Empty window data received\")\n                        return\n                    }\n                    root.windowList = JSON.parse(data)\n                    let tempWinByAddress = {}\n                    for (var i = 0; i < root.windowList.length; ++i) {\n                        var win = root.windowList[i]\n                        tempWinByAddress[win.address] = win\n                    }\n                    root.windowByAddress = tempWinByAddress\n                    root.addresses = root.windowList.map((win) => win.address)\n                } catch (error) {\n                    console.error(\"[HyprlandData] Failed to parse window data:\", error.message)\n                    console.error(\"[HyprlandData] Data was:\", data)\n                }\n            }\n        }\n    }\n    Process {\n        id: getMonitors\n        command: [\"bash\", \"-c\", \"hyprctl monitors -j | jq -c\"]\n        stdout: SplitParser {\n            onRead: (data) => {\n                try {\n                    if (!data || data.trim() === \"\") {\n                        console.warn(\"[HyprlandData] Empty monitor data received\")\n                        return\n                    }\n                    root.monitors = JSON.parse(data)\n                } catch (error) {\n                    console.error(\"[HyprlandData] Failed to parse monitor data:\", error.message)\n                    console.error(\"[HyprlandData] Data was:\", data)\n                }\n            }\n        }\n    }\n}\n\n"}