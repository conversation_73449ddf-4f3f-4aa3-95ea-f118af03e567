{"path": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/common/widgets/ConfirmDialog.qml"}, "modifiedCode": "import QtQuick\nimport QtQuick.Controls\nimport QtQuick.Layouts\nimport \"root:/modules/common/\"\n\n/**\n * Simple confirmation dialog\n */\nDialog {\n    id: root\n    \n    property string title: qsTr(\"确认\")\n    property string message: qsTr(\"您确定要执行此操作吗？\")\n    property string confirmText: qsTr(\"确认\")\n    property string cancelText: qsTr(\"取消\")\n    \n    signal confirmed()\n    signal cancelled()\n    \n    modal: true\n    anchors.centerIn: parent\n    \n    background: Rectangle {\n        color: Appearance.colors.colLayer1\n        radius: Appearance.rounding.normal\n        border.color: Appearance.colors.colOutlineVariant\n        border.width: 1\n    }\n    \n    contentItem: ColumnLayout {\n        spacing: 20\n        \n        StyledText {\n            Layout.fillWidth: true\n            text: root.title\n            font.pixelSize: Appearance.font.pixelSize.normal\n            font.weight: Font.Medium\n            color: Appearance.colors.colOnLayer1\n            horizontalAlignment: Text.AlignHCenter\n        }\n        \n        StyledText {\n            Layout.fillWidth: true\n            text: root.message\n            font.pixelSize: Appearance.font.pixelSize.small\n            color: Appearance.colors.colOnLayer1\n            wrapMode: Text.Wrap\n            horizontalAlignment: Text.AlignHCenter\n        }\n        \n        RowLayout {\n            Layout.fillWidth: true\n            spacing: 10\n            \n            DialogButton {\n                Layout.fillWidth: true\n                buttonText: root.cancelText\n                onClicked: {\n                    root.cancelled();\n                    root.close();\n                }\n            }\n            \n            DialogButton {\n                Layout.fillWidth: true\n                buttonText: root.confirmText\n                onClicked: {\n                    root.confirmed();\n                    root.close();\n                }\n            }\n        }\n    }\n}\n"}