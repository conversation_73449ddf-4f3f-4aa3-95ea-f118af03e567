{"path": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml"}, "originalCode": "import QtQuick\nimport QtQuick.Controls\nimport QtQuick.Layouts\nimport QtGraphicalEffects\nimport \"root:/modules/common/\"\nimport \"root:/modules/common/widgets/\"\nimport \"root:/services/\"\n\n/**\n * Quick model selector popup\n */\nPopup {\n    id: root\n    \n    modal: true\n    focus: true\n    closePolicy: Popup.CloseOnEscape | Popup.CloseOnPressOutside\n    \n    background: Rectangle {\n        color: Appearance.colors.colLayer1\n        radius: Appearance.rounding.normal\n        border.color: Appearance.colors.colOutlineVariant\n        border.width: 1\n        \n        layer.enabled: true\n        layer.effect: DropShadow {\n            horizontalOffset: 0\n            verticalOffset: 4\n            radius: 8\n            samples: 16\n            color: Qt.rgba(0, 0, 0, 0.2)\n        }\n    }\n    \n    contentItem: ColumnLayout {\n        spacing: 8\n        implicitWidth: 300\n        implicitHeight: Math.min(400, modelListView.contentHeight + headerLayout.implicitHeight + 20)\n        \n        RowLayout {\n            id: headerLayout\n            Layout.fillWidth: true\n            Layout.margins: 10\n            \n            MaterialSymbol {\n                text: \"smart_toy\"\n                iconSize: Appearance.font.pixelSize.normal\n                color: Appearance.colors.colPrimary\n            }\n            \n            StyledText {\n                Layout.fillWidth: true\n                text: qsTr(\"选择模型\")\n                font.pixelSize: Appearance.font.pixelSize.normal\n                font.weight: Font.Medium\n                color: Appearance.colors.colOnLayer1\n            }\n            \n            AiMessageControlButton {\n                buttonIcon: \"close\"\n                onClicked: root.close()\n            }\n        }\n        \n        Rectangle {\n            Layout.fillWidth: true\n            height: 1\n            color: Appearance.colors.colOutlineVariant\n        }\n        \n        ScrollView {\n            Layout.fillWidth: true\n            Layout.fillHeight: true\n            Layout.margins: 5\n            clip: true\n            \n            ListView {\n                id: modelListView\n                spacing: 2\n                \n                model: Ai.modelList\n                delegate: ItemDelegate {\n                    required property string modelData\n                    width: modelListView.width\n                    implicitHeight: modelItemLayout.implicitHeight + 16\n                    \n                    background: Rectangle {\n                        color: parent.hovered ? Appearance.colors.colLayer2Hover : \n                               modelData === Ai.currentModelId ? Appearance.colors.colLayer2 : \"transparent\"\n                        radius: Appearance.rounding.small\n                        border.color: modelData === Ai.currentModelId ? Appearance.colors.colPrimary : \"transparent\"\n                        border.width: 1\n                        \n                        Behavior on color {\n                            animation: Appearance.animation.elementMoveFast.colorAnimation.createObject(this)\n                        }\n                    }\n                    \n                    onClicked: {\n                        if (modelData !== Ai.currentModelId) {\n                            Ai.setModel(modelData, true);\n                        }\n                        root.close();\n                    }\n                    \n                    RowLayout {\n                        id: modelItemLayout\n                        anchors.fill: parent\n                        anchors.margins: 8\n                        spacing: 10\n                        \n                        MaterialSymbol {\n                            text: Ai.models[modelData]?.icon || \"smart_toy\"\n                            iconSize: Appearance.font.pixelSize.normal\n                            color: modelData === Ai.currentModelId ? Appearance.colors.colPrimary : Appearance.colors.colOnLayer1\n                        }\n                        \n                        ColumnLayout {\n                            Layout.fillWidth: true\n                            spacing: 2\n                            \n                            StyledText {\n                                text: Ai.models[modelData]?.name || modelData\n                                font.pixelSize: Appearance.font.pixelSize.small\n                                font.weight: modelData === Ai.currentModelId ? Font.Medium : Font.Normal\n                                color: Appearance.colors.colOnLayer1\n                                elide: Text.ElideRight\n                            }\n                            \n                            StyledText {\n                                text: {\n                                    const model = Ai.models[modelData];\n                                    if (!model) return \"\";\n                                    \n                                    let info = [];\n                                    if (model.custom) info.push(qsTr(\"自定义\"));\n                                    if (model.requires_key) {\n                                        const hasKey = Ai.apiKeysLoaded && Ai.apiKeys[model.key_id];\n                                        info.push(hasKey ? qsTr(\"已配置密钥\") : qsTr(\"需要密钥\"));\n                                    }\n                                    if (model.api_format) info.push(model.api_format.toUpperCase());\n                                    \n                                    return info.join(\" • \");\n                                }\n                                font.pixelSize: Appearance.font.pixelSize.smaller\n                                color: Appearance.colors.colSubtext\n                                visible: text.length > 0\n                                elide: Text.ElideRight\n                            }\n                        }\n                        \n                        MaterialSymbol {\n                            text: \"check\"\n                            iconSize: Appearance.font.pixelSize.small\n                            color: Appearance.colors.colPrimary\n                            visible: modelData === Ai.currentModelId\n                        }\n                    }\n                }\n            }\n        }\n        \n        Rectangle {\n            Layout.fillWidth: true\n            height: 1\n            color: Appearance.colors.colOutlineVariant\n        }\n        \n        RowLayout {\n            Layout.fillWidth: true\n            Layout.margins: 10\n            \n            DialogButton {\n                Layout.fillWidth: true\n                buttonText: qsTr(\"管理模型\")\n                onClicked: {\n                    Qt.openUrlExternally(\"quickshell://settings?page=ai\");\n                    root.close();\n                }\n            }\n        }\n    }\n}\n", "modifiedCode": "import QtQuick\nimport QtQuick.Controls\nimport QtQuick.Layouts\nimport \"root:/modules/common/\"\nimport \"root:/modules/common/widgets/\"\nimport \"root:/services/\"\n\n/**\n * Quick model selector popup\n */\nPopup {\n    id: root\n    \n    modal: true\n    focus: true\n    closePolicy: Popup.CloseOnEscape | Popup.CloseOnPressOutside\n    \n    background: Rectangle {\n        color: Appearance.colors.colLayer1\n        radius: Appearance.rounding.normal\n        border.color: Appearance.colors.colOutlineVariant\n        border.width: 1\n        \n        layer.enabled: true\n        layer.effect: DropShadow {\n            horizontalOffset: 0\n            verticalOffset: 4\n            radius: 8\n            samples: 16\n            color: Qt.rgba(0, 0, 0, 0.2)\n        }\n    }\n    \n    contentItem: ColumnLayout {\n        spacing: 8\n        implicitWidth: 300\n        implicitHeight: Math.min(400, modelListView.contentHeight + headerLayout.implicitHeight + 20)\n        \n        RowLayout {\n            id: headerLayout\n            Layout.fillWidth: true\n            Layout.margins: 10\n            \n            MaterialSymbol {\n                text: \"smart_toy\"\n                iconSize: Appearance.font.pixelSize.normal\n                color: Appearance.colors.colPrimary\n            }\n            \n            StyledText {\n                Layout.fillWidth: true\n                text: qsTr(\"选择模型\")\n                font.pixelSize: Appearance.font.pixelSize.normal\n                font.weight: Font.Medium\n                color: Appearance.colors.colOnLayer1\n            }\n            \n            AiMessageControlButton {\n                buttonIcon: \"close\"\n                onClicked: root.close()\n            }\n        }\n        \n        Rectangle {\n            Layout.fillWidth: true\n            height: 1\n            color: Appearance.colors.colOutlineVariant\n        }\n        \n        ScrollView {\n            Layout.fillWidth: true\n            Layout.fillHeight: true\n            Layout.margins: 5\n            clip: true\n            \n            ListView {\n                id: modelListView\n                spacing: 2\n                \n                model: Ai.modelList\n                delegate: ItemDelegate {\n                    required property string modelData\n                    width: modelListView.width\n                    implicitHeight: modelItemLayout.implicitHeight + 16\n                    \n                    background: Rectangle {\n                        color: parent.hovered ? Appearance.colors.colLayer2Hover : \n                               modelData === Ai.currentModelId ? Appearance.colors.colLayer2 : \"transparent\"\n                        radius: Appearance.rounding.small\n                        border.color: modelData === Ai.currentModelId ? Appearance.colors.colPrimary : \"transparent\"\n                        border.width: 1\n                        \n                        Behavior on color {\n                            animation: Appearance.animation.elementMoveFast.colorAnimation.createObject(this)\n                        }\n                    }\n                    \n                    onClicked: {\n                        if (modelData !== Ai.currentModelId) {\n                            Ai.setModel(modelData, true);\n                        }\n                        root.close();\n                    }\n                    \n                    RowLayout {\n                        id: modelItemLayout\n                        anchors.fill: parent\n                        anchors.margins: 8\n                        spacing: 10\n                        \n                        MaterialSymbol {\n                            text: Ai.models[modelData]?.icon || \"smart_toy\"\n                            iconSize: Appearance.font.pixelSize.normal\n                            color: modelData === Ai.currentModelId ? Appearance.colors.colPrimary : Appearance.colors.colOnLayer1\n                        }\n                        \n                        ColumnLayout {\n                            Layout.fillWidth: true\n                            spacing: 2\n                            \n                            StyledText {\n                                text: Ai.models[modelData]?.name || modelData\n                                font.pixelSize: Appearance.font.pixelSize.small\n                                font.weight: modelData === Ai.currentModelId ? Font.Medium : Font.Normal\n                                color: Appearance.colors.colOnLayer1\n                                elide: Text.ElideRight\n                            }\n                            \n                            StyledText {\n                                text: {\n                                    const model = Ai.models[modelData];\n                                    if (!model) return \"\";\n                                    \n                                    let info = [];\n                                    if (model.custom) info.push(qsTr(\"自定义\"));\n                                    if (model.requires_key) {\n                                        const hasKey = Ai.apiKeysLoaded && Ai.apiKeys[model.key_id];\n                                        info.push(hasKey ? qsTr(\"已配置密钥\") : qsTr(\"需要密钥\"));\n                                    }\n                                    if (model.api_format) info.push(model.api_format.toUpperCase());\n                                    \n                                    return info.join(\" • \");\n                                }\n                                font.pixelSize: Appearance.font.pixelSize.smaller\n                                color: Appearance.colors.colSubtext\n                                visible: text.length > 0\n                                elide: Text.ElideRight\n                            }\n                        }\n                        \n                        MaterialSymbol {\n                            text: \"check\"\n                            iconSize: Appearance.font.pixelSize.small\n                            color: Appearance.colors.colPrimary\n                            visible: modelData === Ai.currentModelId\n                        }\n                    }\n                }\n            }\n        }\n        \n        Rectangle {\n            Layout.fillWidth: true\n            height: 1\n            color: Appearance.colors.colOutlineVariant\n        }\n        \n        RowLayout {\n            Layout.fillWidth: true\n            Layout.margins: 10\n            \n            DialogButton {\n                Layout.fillWidth: true\n                buttonText: qsTr(\"管理模型\")\n                onClicked: {\n                    Qt.openUrlExternally(\"quickshell://settings?page=ai\");\n                    root.close();\n                }\n            }\n        }\n    }\n}\n"}