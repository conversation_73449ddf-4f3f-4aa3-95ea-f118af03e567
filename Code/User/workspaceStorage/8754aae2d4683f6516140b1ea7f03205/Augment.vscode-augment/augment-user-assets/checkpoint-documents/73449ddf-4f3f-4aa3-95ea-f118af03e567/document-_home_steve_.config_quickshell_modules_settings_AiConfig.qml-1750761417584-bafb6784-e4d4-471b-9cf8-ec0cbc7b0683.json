{"path": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/settings/AiConfig.qml"}, "originalCode": "import QtQuick\nimport QtQuick.Controls\nimport QtQuick.Layouts\nimport \"root:/services/\"\nimport \"root:/modules/common/\"\nimport \"root:/modules/common/widgets/\"\n\nContentPage {\n    id: root\n    forceWidth: true\n\n    ContentSection {\n        title: qsTr(\"模型配置\")\n\n        ContentSubsection {\n            title: qsTr(\"当前模型\")\n            tooltip: qsTr(\"选择要使用的AI模型\")\n\n            ConfigSelectionArray {\n                currentValue: Ai.currentModelId\n                configOptionName: \"ai.model\"\n                onSelected: (newValue) => {\n                    Ai.setModel(newValue, true);\n                }\n                options: Ai.modelList.map(modelId => {\n                    const model = Ai.models[modelId];\n                    return {\n                        displayName: model.name || modelId,\n                        value: modelId\n                    };\n                })\n            }\n        }\n\n        ContentSubsection {\n            title: qsTr(\"模型信息\")\n            visible: Ai.currentModelId && Ai.models[Ai.currentModelId]\n\n            Rectangle {\n                Layout.fillWidth: true\n                implicitHeight: modelInfoLayout.implicitHeight + 20\n                color: Appearance.colors.colLayer2\n                radius: Appearance.rounding.small\n                border.color: Appearance.colors.colOutlineVariant\n                border.width: 1\n\n                ColumnLayout {\n                    id: modelInfoLayout\n                    anchors.fill: parent\n                    anchors.margins: 10\n                    spacing: 8\n\n                    RowLayout {\n                        Layout.fillWidth: true\n                        MaterialSymbol {\n                            text: Ai.models[Ai.currentModelId]?.icon || \"smart_toy\"\n                            iconSize: Appearance.font.pixelSize.larger\n                            color: Appearance.colors.colPrimary\n                        }\n                        StyledText {\n                            Layout.fillWidth: true\n                            text: Ai.models[Ai.currentModelId]?.name || \"\"\n                            font.pixelSize: Appearance.font.pixelSize.normal\n                            font.weight: Font.Medium\n                            color: Appearance.colors.colOnLayer2\n                        }\n                    }\n\n                    StyledText {\n                        Layout.fillWidth: true\n                        text: Ai.models[Ai.currentModelId]?.description || \"\"\n                        font.pixelSize: Appearance.font.pixelSize.small\n                        color: Appearance.colors.colSubtext\n                        wrapMode: Text.Wrap\n                    }\n\n                    RowLayout {\n                        Layout.fillWidth: true\n                        visible: Ai.models[Ai.currentModelId]?.endpoint\n\n                        StyledText {\n                            text: qsTr(\"端点:\")\n                            font.pixelSize: Appearance.font.pixelSize.smaller\n                            color: Appearance.colors.colSubtext\n                        }\n                        StyledText {\n                            Layout.fillWidth: true\n                            text: Ai.models[Ai.currentModelId]?.endpoint || \"\"\n                            font.pixelSize: Appearance.font.pixelSize.smaller\n                            color: Appearance.colors.colOnLayer2\n                            elide: Text.ElideMiddle\n                        }\n                    }\n\n                    RowLayout {\n                        Layout.fillWidth: true\n                        visible: Ai.models[Ai.currentModelId]?.requires_key\n\n                        MaterialSymbol {\n                            text: \"key\"\n                            iconSize: Appearance.font.pixelSize.small\n                            color: Appearance.colors.colSubtext\n                        }\n                        StyledText {\n                            text: qsTr(\"需要API密钥\")\n                            font.pixelSize: Appearance.font.pixelSize.smaller\n                            color: Appearance.colors.colSubtext\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    ContentSection {\n        title: qsTr(\"API密钥管理\")\n        visible: Ai.models[Ai.currentModelId]?.requires_key\n\n        ContentSubsection {\n            title: qsTr(\"当前模型密钥\")\n            tooltip: qsTr(\"为当前选择的模型设置API密钥\")\n\n            ColumnLayout {\n                Layout.fillWidth: true\n                spacing: 8\n\n                RowLayout {\n                    Layout.fillWidth: true\n                    MaterialTextField {\n                        id: apiKeyField\n                        Layout.fillWidth: true\n                        placeholderText: qsTr(\"输入API密钥...\")\n                        echoMode: showApiKey.checked ? TextInput.Normal : TextInput.Password\n                        text: \"\"\n                        \n                        Component.onCompleted: {\n                            if (Ai.apiKeysLoaded && Ai.models[Ai.currentModelId]?.key_id) {\n                                text = Ai.apiKeys[Ai.models[Ai.currentModelId].key_id] || \"\";\n                            }\n                        }\n\n                        Connections {\n                            target: Ai\n                            function onCurrentModelIdChanged() {\n                                if (Ai.apiKeysLoaded && Ai.models[Ai.currentModelId]?.key_id) {\n                                    apiKeyField.text = Ai.apiKeys[Ai.models[Ai.currentModelId].key_id] || \"\";\n                                }\n                            }\n                        }\n                    }\n\n                    ConfigSwitch {\n                        id: showApiKey\n                        text: qsTr(\"显示\")\n                        checked: false\n                        Layout.fillWidth: false\n                    }\n                }\n\n                RowLayout {\n                    Layout.fillWidth: true\n                    spacing: 8\n\n                    DialogButton {\n                        buttonText: qsTr(\"保存密钥\")\n                        enabled: apiKeyField.text.length > 0\n                        onClicked: {\n                            Ai.setApiKey(apiKeyField.text);\n                        }\n                    }\n\n                    DialogButton {\n                        buttonText: qsTr(\"清除密钥\")\n                        enabled: apiKeyField.text.length > 0\n                        onClicked: {\n                            Ai.setApiKey(\"\");\n                            apiKeyField.text = \"\";\n                        }\n                    }\n\n                    DialogButton {\n                        buttonText: qsTr(\"获取密钥\")\n                        visible: Ai.models[Ai.currentModelId]?.key_get_link\n                        onClicked: {\n                            Qt.openUrlExternally(Ai.models[Ai.currentModelId].key_get_link);\n                        }\n                    }\n                }\n\n                Rectangle {\n                    Layout.fillWidth: true\n                    visible: Ai.models[Ai.currentModelId]?.key_get_description\n                    implicitHeight: keyHelpText.implicitHeight + 16\n                    color: Appearance.colors.colLayer2\n                    radius: Appearance.rounding.small\n                    border.color: Appearance.colors.colOutlineVariant\n                    border.width: 1\n\n                    StyledText {\n                        id: keyHelpText\n                        anchors.fill: parent\n                        anchors.margins: 8\n                        text: Ai.models[Ai.currentModelId]?.key_get_description || \"\"\n                        font.pixelSize: Appearance.font.pixelSize.smaller\n                        color: Appearance.colors.colOnLayer2\n                        wrapMode: Text.Wrap\n                        textFormat: Text.MarkdownText\n                    }\n                }\n            }\n        }\n    }\n\n    ContentSection {\n        title: qsTr(\"模型参数\")\n\n        ContentSubsection {\n            title: qsTr(\"温度设置\")\n            tooltip: qsTr(\"控制模型输出的随机性，值越高越随机\")\n\n            ConfigRow {\n                ConfigSpinBox {\n                    text: qsTr(\"温度\")\n                    value: Math.round(Ai.temperature * 100) / 100\n                    from: 0\n                    to: 200\n                    stepSize: 5\n                    onValueChanged: {\n                        Ai.setTemperature(value / 100);\n                    }\n                }\n\n                StyledText {\n                    Layout.fillWidth: true\n                    text: qsTr(\"当前值: %1\").arg(Ai.temperature.toFixed(2))\n                    font.pixelSize: Appearance.font.pixelSize.smaller\n                    color: Appearance.colors.colSubtext\n                    horizontalAlignment: Text.AlignRight\n                }\n            }\n        }\n\n        ContentSubsection {\n            title: qsTr(\"系统提示\")\n            tooltip: qsTr(\"设置AI助手的行为和角色\")\n\n            MaterialTextField {\n                Layout.fillWidth: true\n                placeholderText: qsTr(\"输入系统提示...\")\n                text: ConfigOptions.ai?.systemPrompt || \"\"\n                wrapMode: TextEdit.Wrap\n                onTextChanged: {\n                    ConfigLoader.setConfigValueAndSave(\"ai.systemPrompt\", text);\n                }\n            }\n        }\n    }\n\n    ContentSection {\n        title: qsTr(\"自定义模型\")\n\n        ContentSubsection {\n            title: qsTr(\"添加自定义模型\")\n            tooltip: qsTr(\"添加自己的AI模型配置\")\n\n            ColumnLayout {\n                Layout.fillWidth: true\n                spacing: 8\n\n                ConfigRow {\n                    MaterialTextField {\n                        id: customModelName\n                        Layout.fillWidth: true\n                        placeholderText: qsTr(\"模型名称\")\n                    }\n                    MaterialTextField {\n                        id: customModelDisplayName\n                        Layout.fillWidth: true\n                        placeholderText: qsTr(\"显示名称\")\n                    }\n                }\n\n                ConfigRow {\n                    MaterialTextField {\n                        id: customModelEndpoint\n                        Layout.fillWidth: true\n                        placeholderText: qsTr(\"API端点URL\")\n                    }\n                    MaterialTextField {\n                        id: customModelId\n                        Layout.fillWidth: true\n                        placeholderText: qsTr(\"模型ID\")\n                    }\n                }\n\n                ConfigRow {\n                    ConfigSelectionArray {\n                        id: customApiFormat\n                        Layout.fillWidth: true\n                        currentValue: \"openai\"\n                        options: [\n                            { displayName: \"OpenAI\", value: \"openai\" },\n                            { displayName: \"Gemini\", value: \"gemini\" }\n                        ]\n                    }\n                    ConfigSwitch {\n                        id: customRequiresKey\n                        text: qsTr(\"需要API密钥\")\n                        checked: true\n                    }\n                }\n\n                MaterialTextField {\n                    id: customModelDescription\n                    Layout.fillWidth: true\n                    placeholderText: qsTr(\"模型描述\")\n                    wrapMode: TextEdit.Wrap\n                }\n\n                DialogButton {\n                    buttonText: qsTr(\"添加模型\")\n                    enabled: customModelName.text.length > 0 && customModelEndpoint.text.length > 0\n                    onClicked: {\n                        const modelConfig = {\n                            name: customModelName.text,\n                            displayName: customModelDisplayName.text || customModelName.text,\n                            endpoint: customModelEndpoint.text,\n                            modelId: customModelId.text || customModelName.text,\n                            description: customModelDescription.text,\n                            apiFormat: customApiFormat.currentValue,\n                            requiresKey: customRequiresKey.checked,\n                            keyId: \"custom-\" + customModelName.text.toLowerCase().replace(/[^a-z0-9]/g, '-')\n                        };\n\n                        Ai.addCustomModel(modelConfig);\n\n                        // 清空输入字段\n                        customModelName.text = \"\";\n                        customModelDisplayName.text = \"\";\n                        customModelEndpoint.text = \"\";\n                        customModelId.text = \"\";\n                        customModelDescription.text = \"\";\n                    }\n                }\n            }\n        }\n\n        ContentSubsection {\n            title: qsTr(\"模型管理\")\n            tooltip: qsTr(\"管理所有可用的模型\")\n\n            ColumnLayout {\n                Layout.fillWidth: true\n                spacing: 8\n\n                Repeater {\n                    model: Ai.modelList\n                    delegate: Rectangle {\n                        required property string modelData\n                        Layout.fillWidth: true\n                        implicitHeight: modelItemLayout.implicitHeight + 16\n                        color: Appearance.colors.colLayer2\n                        radius: Appearance.rounding.small\n                        border.color: modelData === Ai.currentModelId ? Appearance.colors.colPrimary : Appearance.colors.colOutlineVariant\n                        border.width: 1\n\n                        RowLayout {\n                            id: modelItemLayout\n                            anchors.fill: parent\n                            anchors.margins: 8\n                            spacing: 10\n\n                            MaterialSymbol {\n                                text: Ai.models[modelData]?.icon || \"smart_toy\"\n                                iconSize: Appearance.font.pixelSize.normal\n                                color: modelData === Ai.currentModelId ? Appearance.colors.colPrimary : Appearance.colors.colOnLayer2\n                            }\n\n                            ColumnLayout {\n                                Layout.fillWidth: true\n                                spacing: 2\n\n                                StyledText {\n                                    text: Ai.models[modelData]?.name || modelData\n                                    font.pixelSize: Appearance.font.pixelSize.small\n                                    font.weight: Font.Medium\n                                    color: Appearance.colors.colOnLayer2\n                                }\n\n                                StyledText {\n                                    text: {\n                                        const model = Ai.models[modelData];\n                                        let info = [];\n                                        if (model?.custom) info.push(qsTr(\"自定义\"));\n                                        if (model?.requires_key) info.push(qsTr(\"需要密钥\"));\n                                        if (model?.api_format) info.push(model.api_format.toUpperCase());\n                                        return info.join(\" • \");\n                                    }\n                                    font.pixelSize: Appearance.font.pixelSize.smaller\n                                    color: Appearance.colors.colSubtext\n                                    visible: text.length > 0\n                                }\n                            }\n\n                            DialogButton {\n                                buttonText: qsTr(\"选择\")\n                                visible: modelData !== Ai.currentModelId\n                                onClicked: {\n                                    Ai.setModel(modelData, true);\n                                }\n                            }\n\n                            DialogButton {\n                                buttonText: qsTr(\"删除\")\n                                visible: Ai.models[modelData]?.custom === true\n                                onClicked: {\n                                    Ai.removeCustomModel(modelData);\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    ContentSection {\n        title: qsTr(\"聊天管理\")\n\n        ConfigRow {\n            uniform: true\n\n            DialogButton {\n                buttonText: qsTr(\"清除聊天记录\")\n                onClicked: {\n                    Ai.clearMessages();\n                }\n            }\n\n            DialogButton {\n                buttonText: qsTr(\"显示当前温度\")\n                onClicked: {\n                    Ai.printTemperature();\n                }\n            }\n\n            DialogButton {\n                buttonText: qsTr(\"显示API密钥\")\n                visible: Ai.models[Ai.currentModelId]?.requires_key\n                onClicked: {\n                    Ai.printApiKey();\n                }\n            }\n        }\n    }\n}\n", "modifiedCode": "import QtQuick\nimport QtQuick.Controls\nimport QtQuick.Layouts\nimport \"root:/services/\"\nimport \"root:/modules/common/\"\nimport \"root:/modules/common/widgets/\"\n\nContentPage {\n    id: root\n    forceWidth: true\n\n    ConfirmDialog {\n        id: deleteConfirmDialog\n        property string modelToDelete: \"\"\n        title: qsTr(\"删除模型\")\n        message: qsTr(\"确定要删除模型 '%1' 吗？此操作无法撤销。\").arg(modelToDelete)\n        confirmText: qsTr(\"删除\")\n        cancelText: qsTr(\"取消\")\n\n        onConfirmed: {\n            if (modelToDelete.length > 0) {\n                Ai.removeCustomModel(modelToDelete);\n                modelToDelete = \"\";\n            }\n        }\n\n        onCancelled: {\n            modelToDelete = \"\";\n        }\n    }\n\n    ContentSection {\n        title: qsTr(\"模型配置\")\n\n        ContentSubsection {\n            title: qsTr(\"当前模型\")\n            tooltip: qsTr(\"选择要使用的AI模型\")\n\n            ConfigSelectionArray {\n                currentValue: Ai.currentModelId\n                configOptionName: \"ai.model\"\n                onSelected: (newValue) => {\n                    Ai.setModel(newValue, true);\n                }\n                options: Ai.modelList.map(modelId => {\n                    const model = Ai.models[modelId];\n                    return {\n                        displayName: model.name || modelId,\n                        value: modelId\n                    };\n                })\n            }\n        }\n\n        ContentSubsection {\n            title: qsTr(\"模型信息\")\n            visible: Ai.currentModelId && Ai.models[Ai.currentModelId]\n\n            Rectangle {\n                Layout.fillWidth: true\n                implicitHeight: modelInfoLayout.implicitHeight + 20\n                color: Appearance.colors.colLayer2\n                radius: Appearance.rounding.small\n                border.color: Appearance.colors.colOutlineVariant\n                border.width: 1\n\n                ColumnLayout {\n                    id: modelInfoLayout\n                    anchors.fill: parent\n                    anchors.margins: 10\n                    spacing: 8\n\n                    RowLayout {\n                        Layout.fillWidth: true\n                        MaterialSymbol {\n                            text: Ai.models[Ai.currentModelId]?.icon || \"smart_toy\"\n                            iconSize: Appearance.font.pixelSize.larger\n                            color: Appearance.colors.colPrimary\n                        }\n                        StyledText {\n                            Layout.fillWidth: true\n                            text: Ai.models[Ai.currentModelId]?.name || \"\"\n                            font.pixelSize: Appearance.font.pixelSize.normal\n                            font.weight: Font.Medium\n                            color: Appearance.colors.colOnLayer2\n                        }\n                    }\n\n                    StyledText {\n                        Layout.fillWidth: true\n                        text: Ai.models[Ai.currentModelId]?.description || \"\"\n                        font.pixelSize: Appearance.font.pixelSize.small\n                        color: Appearance.colors.colSubtext\n                        wrapMode: Text.Wrap\n                    }\n\n                    RowLayout {\n                        Layout.fillWidth: true\n                        visible: Ai.models[Ai.currentModelId]?.endpoint\n\n                        StyledText {\n                            text: qsTr(\"端点:\")\n                            font.pixelSize: Appearance.font.pixelSize.smaller\n                            color: Appearance.colors.colSubtext\n                        }\n                        StyledText {\n                            Layout.fillWidth: true\n                            text: Ai.models[Ai.currentModelId]?.endpoint || \"\"\n                            font.pixelSize: Appearance.font.pixelSize.smaller\n                            color: Appearance.colors.colOnLayer2\n                            elide: Text.ElideMiddle\n                        }\n                    }\n\n                    RowLayout {\n                        Layout.fillWidth: true\n                        visible: Ai.models[Ai.currentModelId]?.requires_key\n\n                        MaterialSymbol {\n                            text: \"key\"\n                            iconSize: Appearance.font.pixelSize.small\n                            color: Appearance.colors.colSubtext\n                        }\n                        StyledText {\n                            text: qsTr(\"需要API密钥\")\n                            font.pixelSize: Appearance.font.pixelSize.smaller\n                            color: Appearance.colors.colSubtext\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    ContentSection {\n        title: qsTr(\"API密钥管理\")\n        visible: Ai.models[Ai.currentModelId]?.requires_key\n\n        ContentSubsection {\n            title: qsTr(\"当前模型密钥\")\n            tooltip: qsTr(\"为当前选择的模型设置API密钥\")\n\n            ColumnLayout {\n                Layout.fillWidth: true\n                spacing: 8\n\n                RowLayout {\n                    Layout.fillWidth: true\n                    MaterialTextField {\n                        id: apiKeyField\n                        Layout.fillWidth: true\n                        placeholderText: qsTr(\"输入API密钥...\")\n                        echoMode: showApiKey.checked ? TextInput.Normal : TextInput.Password\n                        text: \"\"\n                        \n                        Component.onCompleted: {\n                            if (Ai.apiKeysLoaded && Ai.models[Ai.currentModelId]?.key_id) {\n                                text = Ai.apiKeys[Ai.models[Ai.currentModelId].key_id] || \"\";\n                            }\n                        }\n\n                        Connections {\n                            target: Ai\n                            function onCurrentModelIdChanged() {\n                                if (Ai.apiKeysLoaded && Ai.models[Ai.currentModelId]?.key_id) {\n                                    apiKeyField.text = Ai.apiKeys[Ai.models[Ai.currentModelId].key_id] || \"\";\n                                }\n                            }\n                        }\n                    }\n\n                    ConfigSwitch {\n                        id: showApiKey\n                        text: qsTr(\"显示\")\n                        checked: false\n                        Layout.fillWidth: false\n                    }\n                }\n\n                RowLayout {\n                    Layout.fillWidth: true\n                    spacing: 8\n\n                    DialogButton {\n                        buttonText: qsTr(\"保存密钥\")\n                        enabled: apiKeyField.text.length > 0\n                        onClicked: {\n                            Ai.setApiKey(apiKeyField.text);\n                        }\n                    }\n\n                    DialogButton {\n                        buttonText: qsTr(\"清除密钥\")\n                        enabled: apiKeyField.text.length > 0\n                        onClicked: {\n                            Ai.setApiKey(\"\");\n                            apiKeyField.text = \"\";\n                        }\n                    }\n\n                    DialogButton {\n                        buttonText: qsTr(\"获取密钥\")\n                        visible: Ai.models[Ai.currentModelId]?.key_get_link\n                        onClicked: {\n                            Qt.openUrlExternally(Ai.models[Ai.currentModelId].key_get_link);\n                        }\n                    }\n                }\n\n                Rectangle {\n                    Layout.fillWidth: true\n                    visible: Ai.models[Ai.currentModelId]?.key_get_description\n                    implicitHeight: keyHelpText.implicitHeight + 16\n                    color: Appearance.colors.colLayer2\n                    radius: Appearance.rounding.small\n                    border.color: Appearance.colors.colOutlineVariant\n                    border.width: 1\n\n                    StyledText {\n                        id: keyHelpText\n                        anchors.fill: parent\n                        anchors.margins: 8\n                        text: Ai.models[Ai.currentModelId]?.key_get_description || \"\"\n                        font.pixelSize: Appearance.font.pixelSize.smaller\n                        color: Appearance.colors.colOnLayer2\n                        wrapMode: Text.Wrap\n                        textFormat: Text.MarkdownText\n                    }\n                }\n            }\n        }\n    }\n\n    ContentSection {\n        title: qsTr(\"模型参数\")\n\n        ContentSubsection {\n            title: qsTr(\"温度设置\")\n            tooltip: qsTr(\"控制模型输出的随机性，值越高越随机\")\n\n            ConfigRow {\n                ConfigSpinBox {\n                    text: qsTr(\"温度\")\n                    value: Math.round(Ai.temperature * 100) / 100\n                    from: 0\n                    to: 200\n                    stepSize: 5\n                    onValueChanged: {\n                        Ai.setTemperature(value / 100);\n                    }\n                }\n\n                StyledText {\n                    Layout.fillWidth: true\n                    text: qsTr(\"当前值: %1\").arg(Ai.temperature.toFixed(2))\n                    font.pixelSize: Appearance.font.pixelSize.smaller\n                    color: Appearance.colors.colSubtext\n                    horizontalAlignment: Text.AlignRight\n                }\n            }\n        }\n\n        ContentSubsection {\n            title: qsTr(\"系统提示\")\n            tooltip: qsTr(\"设置AI助手的行为和角色\")\n\n            MaterialTextField {\n                Layout.fillWidth: true\n                placeholderText: qsTr(\"输入系统提示...\")\n                text: ConfigOptions.ai?.systemPrompt || \"\"\n                wrapMode: TextEdit.Wrap\n                onTextChanged: {\n                    ConfigLoader.setConfigValueAndSave(\"ai.systemPrompt\", text);\n                }\n            }\n        }\n    }\n\n    ContentSection {\n        title: qsTr(\"自定义模型\")\n\n        ContentSubsection {\n            title: qsTr(\"添加自定义模型\")\n            tooltip: qsTr(\"添加自己的AI模型配置\")\n\n            ColumnLayout {\n                Layout.fillWidth: true\n                spacing: 8\n\n                ConfigRow {\n                    MaterialTextField {\n                        id: customModelName\n                        Layout.fillWidth: true\n                        placeholderText: qsTr(\"模型名称\")\n                    }\n                    MaterialTextField {\n                        id: customModelDisplayName\n                        Layout.fillWidth: true\n                        placeholderText: qsTr(\"显示名称\")\n                    }\n                }\n\n                ConfigRow {\n                    MaterialTextField {\n                        id: customModelEndpoint\n                        Layout.fillWidth: true\n                        placeholderText: qsTr(\"API端点URL\")\n                    }\n                    MaterialTextField {\n                        id: customModelId\n                        Layout.fillWidth: true\n                        placeholderText: qsTr(\"模型ID\")\n                    }\n                }\n\n                ConfigRow {\n                    ConfigSelectionArray {\n                        id: customApiFormat\n                        Layout.fillWidth: true\n                        currentValue: \"openai\"\n                        options: [\n                            { displayName: \"OpenAI\", value: \"openai\" },\n                            { displayName: \"Gemini\", value: \"gemini\" }\n                        ]\n                    }\n                    ConfigSwitch {\n                        id: customRequiresKey\n                        text: qsTr(\"需要API密钥\")\n                        checked: true\n                    }\n                }\n\n                MaterialTextField {\n                    id: customModelDescription\n                    Layout.fillWidth: true\n                    placeholderText: qsTr(\"模型描述\")\n                    wrapMode: TextEdit.Wrap\n                }\n\n                DialogButton {\n                    buttonText: qsTr(\"添加模型\")\n                    enabled: customModelName.text.length > 0 && customModelEndpoint.text.length > 0\n                    onClicked: {\n                        const modelConfig = {\n                            name: customModelName.text,\n                            displayName: customModelDisplayName.text || customModelName.text,\n                            endpoint: customModelEndpoint.text,\n                            modelId: customModelId.text || customModelName.text,\n                            description: customModelDescription.text,\n                            apiFormat: customApiFormat.currentValue,\n                            requiresKey: customRequiresKey.checked,\n                            keyId: \"custom-\" + customModelName.text.toLowerCase().replace(/[^a-z0-9]/g, '-')\n                        };\n\n                        Ai.addCustomModel(modelConfig);\n\n                        // 清空输入字段\n                        customModelName.text = \"\";\n                        customModelDisplayName.text = \"\";\n                        customModelEndpoint.text = \"\";\n                        customModelId.text = \"\";\n                        customModelDescription.text = \"\";\n                    }\n                }\n            }\n        }\n\n        ContentSubsection {\n            title: qsTr(\"模型管理\")\n            tooltip: qsTr(\"管理所有可用的模型\")\n\n            ColumnLayout {\n                Layout.fillWidth: true\n                spacing: 8\n\n                Repeater {\n                    model: Ai.modelList\n                    delegate: Rectangle {\n                        required property string modelData\n                        Layout.fillWidth: true\n                        implicitHeight: modelItemLayout.implicitHeight + 16\n                        color: Appearance.colors.colLayer2\n                        radius: Appearance.rounding.small\n                        border.color: modelData === Ai.currentModelId ? Appearance.colors.colPrimary : Appearance.colors.colOutlineVariant\n                        border.width: 1\n\n                        RowLayout {\n                            id: modelItemLayout\n                            anchors.fill: parent\n                            anchors.margins: 8\n                            spacing: 10\n\n                            MaterialSymbol {\n                                text: Ai.models[modelData]?.icon || \"smart_toy\"\n                                iconSize: Appearance.font.pixelSize.normal\n                                color: modelData === Ai.currentModelId ? Appearance.colors.colPrimary : Appearance.colors.colOnLayer2\n                            }\n\n                            ColumnLayout {\n                                Layout.fillWidth: true\n                                spacing: 2\n\n                                StyledText {\n                                    text: Ai.models[modelData]?.name || modelData\n                                    font.pixelSize: Appearance.font.pixelSize.small\n                                    font.weight: Font.Medium\n                                    color: Appearance.colors.colOnLayer2\n                                }\n\n                                StyledText {\n                                    text: {\n                                        const model = Ai.models[modelData];\n                                        let info = [];\n                                        if (model?.custom) info.push(qsTr(\"自定义\"));\n                                        if (model?.requires_key) info.push(qsTr(\"需要密钥\"));\n                                        if (model?.api_format) info.push(model.api_format.toUpperCase());\n                                        return info.join(\" • \");\n                                    }\n                                    font.pixelSize: Appearance.font.pixelSize.smaller\n                                    color: Appearance.colors.colSubtext\n                                    visible: text.length > 0\n                                }\n                            }\n\n                            DialogButton {\n                                buttonText: qsTr(\"选择\")\n                                visible: modelData !== Ai.currentModelId\n                                onClicked: {\n                                    Ai.setModel(modelData, true);\n                                }\n                            }\n\n                            DialogButton {\n                                buttonText: qsTr(\"删除\")\n                                visible: Ai.models[modelData]?.custom === true\n                                onClicked: {\n                                    Ai.removeCustomModel(modelData);\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    ContentSection {\n        title: qsTr(\"聊天管理\")\n\n        ConfigRow {\n            uniform: true\n\n            DialogButton {\n                buttonText: qsTr(\"清除聊天记录\")\n                onClicked: {\n                    Ai.clearMessages();\n                }\n            }\n\n            DialogButton {\n                buttonText: qsTr(\"显示当前温度\")\n                onClicked: {\n                    Ai.printTemperature();\n                }\n            }\n\n            DialogButton {\n                buttonText: qsTr(\"显示API密钥\")\n                visible: Ai.models[Ai.currentModelId]?.requires_key\n                onClicked: {\n                    Ai.printApiKey();\n                }\n            }\n        }\n    }\n}\n"}