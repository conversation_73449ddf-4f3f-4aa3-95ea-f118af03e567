{"path": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/settings.qml"}, "originalCode": "//@ pragma UseQApplication\n//@ pragma Env QS_NO_RELOAD_POPUP=1\n//@ pragma Env QT_QUICK_CONTROLS_STYLE=Basic\n\n// Adjust this to make the app smaller or larger\n//@ pragma Env QT_SCALE_FACTOR=1\n\nimport Qt5Compat.GraphicalEffects\nimport QtQuick\nimport QtQuick.Controls\nimport QtQuick.Layouts\nimport QtQuick.Window\nimport Quickshell\nimport Quickshell.Io\nimport Quickshell.Hyprland\nimport \"root:/services/\"\nimport \"root:/modules/common/\"\nimport \"root:/modules/common/widgets/\"\nimport \"root:/modules/common/functions/color_utils.js\" as ColorUtils\nimport \"root:/modules/common/functions/file_utils.js\" as FileUtils\nimport \"root:/modules/common/functions/string_utils.js\" as StringUtils\n\nApplicationWindow {\n    id: root\n    property string firstRunFilePath: FileUtils.trimFileProtocol(`${Directories.state}/user/first_run.txt`)\n    property string firstRunFileContent: \"This file is just here to confirm you've been greeted :>\"\n    property real contentPadding: 8\n    property bool showNextTime: false\n    property var pages: [\n        {\n            name: \"Style\",\n            icon: \"palette\",\n            component: \"modules/settings/StyleConfig.qml\"\n        },\n        {\n            name: \"Interface\",\n            icon: \"cards\",\n            component: \"modules/settings/InterfaceConfig.qml\"\n        },\n        {\n            name: \"Services\",\n            icon: \"settings\",\n            component: \"modules/settings/ServicesConfig.qml\"\n        },\n        {\n            name: \"About\",\n            icon: \"info\",\n            component: \"modules/settings/About.qml\"\n        }\n    ]\n    property int currentPage: 0\n\n    visible: true\n    onClosing: Qt.quit()\n    title: \"illogical-impulse Settings\"\n\n    Component.onCompleted: {\n        MaterialThemeLoader.reapplyTheme()\n        ConfigLoader.loadConfig()\n    }\n\n    minimumWidth: 600\n    minimumHeight: 400\n    width: 1100\n    height: 750\n    color: Appearance.m3colors.m3background\n\n    ColumnLayout {\n        anchors {\n            fill: parent\n            margins: contentPadding\n        }\n\n        Keys.onPressed: (event) => {\n            if (event.modifiers === Qt.ControlModifier) {\n                if (event.key === Qt.Key_PageDown) {\n                    root.currentPage = Math.min(root.currentPage + 1, root.pages.length - 1)\n                    event.accepted = true;\n                } \n                else if (event.key === Qt.Key_PageUp) {\n                    root.currentPage = Math.max(root.currentPage - 1, 0)\n                    event.accepted = true;\n                }\n                else if (event.key === Qt.Key_Tab) {\n                    root.currentPage = (root.currentPage + 1) % root.pages.length;\n                    event.accepted = true;\n                }\n                else if (event.key === Qt.Key_Backtab) {\n                    root.currentPage = (root.currentPage - 1 + root.pages.length) % root.pages.length;\n                    event.accepted = true;\n                }\n            }\n        }\n\n        Item { // Titlebar\n            visible: ConfigOptions?.windows.showTitlebar\n            Layout.fillWidth: true\n            Layout.fillHeight: false\n            implicitHeight: Math.max(titleText.implicitHeight, windowControlsRow.implicitHeight)\n            StyledText {\n                id: titleText\n                anchors {\n                    left: ConfigOptions.windows.centerTitle ? undefined : parent.left\n                    horizontalCenter: ConfigOptions.windows.centerTitle ? parent.horizontalCenter : undefined\n                    verticalCenter: parent.verticalCenter\n                    leftMargin: 12\n                }\n                color: Appearance.colors.colOnLayer0\n                text: \"Settings\"\n                font.pixelSize: Appearance.font.pixelSize.title\n                font.family: Appearance.font.family.title\n            }\n            RowLayout { // Window controls row\n                id: windowControlsRow\n                anchors.verticalCenter: parent.verticalCenter\n                anchors.right: parent.right\n                RippleButton {\n                    buttonRadius: Appearance.rounding.full\n                    implicitWidth: 35\n                    implicitHeight: 35\n                    onClicked: root.close()\n                    contentItem: MaterialSymbol {\n                        anchors.centerIn: parent\n                        horizontalAlignment: Text.AlignHCenter\n                        text: \"close\"\n                        iconSize: 20\n                    }\n                }\n            }\n        }\n\n        RowLayout { // Window content with navigation rail and content pane\n            Layout.fillWidth: true\n            Layout.fillHeight: true\n            spacing: contentPadding\n            Item {\n                id: navRailWrapper\n                Layout.fillHeight: true\n                Layout.margins: 5\n                implicitWidth: navRail.expanded ? 150 : fab.baseSize\n                Behavior on implicitWidth {\n                    animation: Appearance.animation.elementMoveFast.numberAnimation.createObject(this)\n                }\n                NavigationRail { // Window content with navigation rail and content pane\n                    id: navRail\n                    anchors {\n                        left: parent.left\n                        top: parent.top\n                        bottom: parent.bottom\n                    }\n                    spacing: 10\n                    expanded: root.width > 900\n                    \n                    NavigationRailExpandButton {\n                        focus: root.visible\n                    }\n\n                    FloatingActionButton {\n                        id: fab\n                        iconText: \"edit\"\n                        buttonText: \"Edit config\"\n                        expanded: navRail.expanded\n                        onClicked: {\n                            Qt.openUrlExternally(`${Directories.config}/illogical-impulse/config.json`);\n                        }\n\n                        StyledToolTip {\n                            extraVisibleCondition: !navRail.expanded\n                            content: \"Edit shell config file\"\n                        }\n                    }\n\n                    NavigationRailTabArray {\n                        currentIndex: root.currentPage\n                        expanded: navRail.expanded\n                        Repeater {\n                            model: root.pages\n                            NavigationRailButton {\n                                required property var index\n                                required property var modelData\n                                toggled: root.currentPage === index\n                                onClicked: root.currentPage = index;\n                                expanded: navRail.expanded\n                                buttonIcon: modelData.icon\n                                buttonText: modelData.name\n                                showToggledHighlight: false\n                            }\n                        }\n                    }\n\n                    Item {\n                        Layout.fillHeight: true\n                    }\n                }\n            }\n            Rectangle { // Content container\n                Layout.fillWidth: true\n                Layout.fillHeight: true\n                color: Appearance.m3colors.m3surfaceContainerLow\n                radius: Appearance.rounding.windowRounding - root.contentPadding\n\n                Loader {\n                    id: pageLoader\n                    anchors.fill: parent\n                    opacity: 1.0\n                    source: root.pages[0].component\n                    Connections {\n                        target: root\n                        function onCurrentPageChanged() {\n                            if (pageLoader.sourceComponent !== root.pages[root.currentPage].component) {\n                                switchAnim.complete();\n                                switchAnim.start();\n                            }\n                        }\n                    }\n\n                    SequentialAnimation {\n                        id: switchAnim\n\n                        NumberAnimation {\n                            target: pageLoader\n                            properties: \"opacity\"\n                            from: 1\n                            to: 0\n                            duration: 100\n                            easing.type: Appearance.animation.elementMoveExit.type\n                            easing.bezierCurve: Appearance.animationCurves.emphasizedFirstHalf\n                        }\n                        PropertyAction {\n                            target: pageLoader\n                            property: \"source\"\n                            value: root.pages[root.currentPage].component\n                        }\n                        NumberAnimation {\n                            target: pageLoader\n                            properties: \"opacity\"\n                            from: 0\n                            to: 1\n                            duration: 200\n                            easing.type: Appearance.animation.elementMoveEnter.type\n                            easing.bezierCurve: Appearance.animationCurves.emphasizedLastHalf\n                        }\n                    }\n                }\n            }\n        }\n    }\n}\n", "modifiedCode": "//@ pragma UseQApplication\n//@ pragma Env QS_NO_RELOAD_POPUP=1\n//@ pragma Env QT_QUICK_CONTROLS_STYLE=Basic\n\n// Adjust this to make the app smaller or larger\n//@ pragma Env QT_SCALE_FACTOR=1\n\nimport Qt5Compat.GraphicalEffects\nimport QtQuick\nimport QtQuick.Controls\nimport QtQuick.Layouts\nimport QtQuick.Window\nimport Quickshell\nimport Quickshell.Io\nimport Quickshell.Hyprland\nimport \"root:/services/\"\nimport \"root:/modules/common/\"\nimport \"root:/modules/common/widgets/\"\nimport \"root:/modules/common/functions/color_utils.js\" as ColorUtils\nimport \"root:/modules/common/functions/file_utils.js\" as FileUtils\nimport \"root:/modules/common/functions/string_utils.js\" as StringUtils\n\nApplicationWindow {\n    id: root\n    property string firstRunFilePath: FileUtils.trimFileProtocol(`${Directories.state}/user/first_run.txt`)\n    property string firstRunFileContent: \"This file is just here to confirm you've been greeted :>\"\n    property real contentPadding: 8\n    property bool showNextTime: false\n    property var pages: [\n        {\n            name: \"Style\",\n            icon: \"palette\",\n            component: \"modules/settings/StyleConfig.qml\"\n        },\n        {\n            name: \"Interface\",\n            icon: \"cards\",\n            component: \"modules/settings/InterfaceConfig.qml\"\n        },\n        {\n            name: \"Services\",\n            icon: \"settings\",\n            component: \"modules/settings/ServicesConfig.qml\"\n        },\n        {\n            name: \"About\",\n            icon: \"info\",\n            component: \"modules/settings/About.qml\"\n        }\n    ]\n    property int currentPage: 0\n\n    visible: true\n    onClosing: Qt.quit()\n    title: \"illogical-impulse Settings\"\n\n    Component.onCompleted: {\n        MaterialThemeLoader.reapplyTheme()\n        ConfigLoader.loadConfig()\n    }\n\n    minimumWidth: 600\n    minimumHeight: 400\n    width: 1100\n    height: 750\n    color: Appearance.m3colors.m3background\n\n    ColumnLayout {\n        anchors {\n            fill: parent\n            margins: contentPadding\n        }\n\n        Keys.onPressed: (event) => {\n            if (event.modifiers === Qt.ControlModifier) {\n                if (event.key === Qt.Key_PageDown) {\n                    root.currentPage = Math.min(root.currentPage + 1, root.pages.length - 1)\n                    event.accepted = true;\n                } \n                else if (event.key === Qt.Key_PageUp) {\n                    root.currentPage = Math.max(root.currentPage - 1, 0)\n                    event.accepted = true;\n                }\n                else if (event.key === Qt.Key_Tab) {\n                    root.currentPage = (root.currentPage + 1) % root.pages.length;\n                    event.accepted = true;\n                }\n                else if (event.key === Qt.Key_Backtab) {\n                    root.currentPage = (root.currentPage - 1 + root.pages.length) % root.pages.length;\n                    event.accepted = true;\n                }\n            }\n        }\n\n        Item { // Titlebar\n            visible: ConfigOptions?.windows.showTitlebar\n            Layout.fillWidth: true\n            Layout.fillHeight: false\n            implicitHeight: Math.max(titleText.implicitHeight, windowControlsRow.implicitHeight)\n            StyledText {\n                id: titleText\n                anchors {\n                    left: ConfigOptions.windows.centerTitle ? undefined : parent.left\n                    horizontalCenter: ConfigOptions.windows.centerTitle ? parent.horizontalCenter : undefined\n                    verticalCenter: parent.verticalCenter\n                    leftMargin: 12\n                }\n                color: Appearance.colors.colOnLayer0\n                text: \"Settings\"\n                font.pixelSize: Appearance.font.pixelSize.title\n                font.family: Appearance.font.family.title\n            }\n            RowLayout { // Window controls row\n                id: windowControlsRow\n                anchors.verticalCenter: parent.verticalCenter\n                anchors.right: parent.right\n                RippleButton {\n                    buttonRadius: Appearance.rounding.full\n                    implicitWidth: 35\n                    implicitHeight: 35\n                    onClicked: root.close()\n                    contentItem: MaterialSymbol {\n                        anchors.centerIn: parent\n                        horizontalAlignment: Text.AlignHCenter\n                        text: \"close\"\n                        iconSize: 20\n                    }\n                }\n            }\n        }\n\n        RowLayout { // Window content with navigation rail and content pane\n            Layout.fillWidth: true\n            Layout.fillHeight: true\n            spacing: contentPadding\n            Item {\n                id: navRailWrapper\n                Layout.fillHeight: true\n                Layout.margins: 5\n                implicitWidth: navRail.expanded ? 150 : fab.baseSize\n                Behavior on implicitWidth {\n                    animation: Appearance.animation.elementMoveFast.numberAnimation.createObject(this)\n                }\n                NavigationRail { // Window content with navigation rail and content pane\n                    id: navRail\n                    anchors {\n                        left: parent.left\n                        top: parent.top\n                        bottom: parent.bottom\n                    }\n                    spacing: 10\n                    expanded: root.width > 900\n                    \n                    NavigationRailExpandButton {\n                        focus: root.visible\n                    }\n\n                    FloatingActionButton {\n                        id: fab\n                        iconText: \"edit\"\n                        buttonText: \"Edit config\"\n                        expanded: navRail.expanded\n                        onClicked: {\n                            Qt.openUrlExternally(`${Directories.config}/illogical-impulse/config.json`);\n                        }\n\n                        StyledToolTip {\n                            extraVisibleCondition: !navRail.expanded\n                            content: \"Edit shell config file\"\n                        }\n                    }\n\n                    NavigationRailTabArray {\n                        currentIndex: root.currentPage\n                        expanded: navRail.expanded\n                        Repeater {\n                            model: root.pages\n                            NavigationRailButton {\n                                required property var index\n                                required property var modelData\n                                toggled: root.currentPage === index\n                                onClicked: root.currentPage = index;\n                                expanded: navRail.expanded\n                                buttonIcon: modelData.icon\n                                buttonText: modelData.name\n                                showToggledHighlight: false\n                            }\n                        }\n                    }\n\n                    Item {\n                        Layout.fillHeight: true\n                    }\n                }\n            }\n            Rectangle { // Content container\n                Layout.fillWidth: true\n                Layout.fillHeight: true\n                color: Appearance.m3colors.m3surfaceContainerLow\n                radius: Appearance.rounding.windowRounding - root.contentPadding\n\n                Loader {\n                    id: pageLoader\n                    anchors.fill: parent\n                    opacity: 1.0\n                    source: root.pages[0].component\n                    Connections {\n                        target: root\n                        function onCurrentPageChanged() {\n                            if (pageLoader.sourceComponent !== root.pages[root.currentPage].component) {\n                                switchAnim.complete();\n                                switchAnim.start();\n                            }\n                        }\n                    }\n\n                    SequentialAnimation {\n                        id: switchAnim\n\n                        NumberAnimation {\n                            target: pageLoader\n                            properties: \"opacity\"\n                            from: 1\n                            to: 0\n                            duration: 100\n                            easing.type: Appearance.animation.elementMoveExit.type\n                            easing.bezierCurve: Appearance.animationCurves.emphasizedFirstHalf\n                        }\n                        PropertyAction {\n                            target: pageLoader\n                            property: \"source\"\n                            value: root.pages[root.currentPage].component\n                        }\n                        NumberAnimation {\n                            target: pageLoader\n                            properties: \"opacity\"\n                            from: 0\n                            to: 1\n                            duration: 200\n                            easing.type: Appearance.animation.elementMoveEnter.type\n                            easing.bezierCurve: Appearance.animationCurves.emphasizedLastHalf\n                        }\n                    }\n                }\n            }\n        }\n    }\n}\n"}