{"path": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/services/ConfigLoader.qml"}, "originalCode": "pragma Singleton\npragma ComponentBehavior: Bound\n\nimport \"root:/modules/common\"\nimport \"root:/modules/common/functions/file_utils.js\" as FileUtils\nimport \"root:/modules/common/functions/string_utils.js\" as StringUtils\nimport \"root:/modules/common/functions/object_utils.js\" as ObjectUtils\nimport QtQuick\nimport Quickshell\nimport Quickshell.Io\nimport Quickshell.Hyprland\nimport Qt.labs.platform\n\n/**\n * Loads and manages the shell configuration file.\n * The config file is by default at XDG_CONFIG_HOME/illogical-impulse/config.json.\n * Automatically reloaded when the file changes.\n */\nSingleton {\n    id: root\n    property string filePath: Directories.shellConfigPath\n    property bool firstLoad: true\n    property bool preventNextLoad: false\n    property var preventNextNotification: false\n\n    function loadConfig() {\n        configFileView.reload()\n    }\n\n    function applyConfig(fileContent) {\n        try {\n            if (fileContent.trim() === \"\") {\n                console.warn(\"[ConfigLoader] Config file is empty, skipping load.\");\n                return;\n            }\n\n            // Check if content appears to be truncated\n            const trimmedContent = fileContent.trim();\n            if (!trimmedContent.endsWith('}') && !trimmedContent.endsWith(']')) {\n                console.warn(\"[ConfigLoader] File content appears to be truncated, retrying in 100ms...\");\n                // Retry after a short delay\n                Qt.callLater(() => {\n                    delayedFileRead.interval = 100;\n                    delayedFileRead.start();\n                });\n                return;\n            }\n\n            const json = JSON.parse(fileContent);\n\n            ObjectUtils.applyToQtObject(ConfigOptions, json);\n            if (root.firstLoad) {\n                root.firstLoad = false;\n                root.preventNextLoad = true;\n                root.saveConfig(); // Make sure new properties are added to the user's config file\n            }\n        } catch (e) {\n            console.error(\"[ConfigLoader] Error reading file:\", e);\n            console.log(\"[ConfigLoader] File content was:\", fileContent);\n            console.log(\"[ConfigLoader] File content length:\", fileContent.length);\n            console.log(\"[ConfigLoader] File content ends with:\", fileContent.slice(-50));\n\n            // If it's a parse error and content seems truncated, retry\n            if (e.toString().includes(\"Parse error\") && !fileContent.trim().endsWith('}')) {\n                console.warn(\"[ConfigLoader] Retrying due to apparent truncation...\");\n                Qt.callLater(() => {\n                    delayedFileRead.interval = 200;\n                    delayedFileRead.start();\n                });\n                return;\n            }\n\n            Quickshell.execDetached([\"bash\", \"-c\", `notify-send '${qsTr(\"Shell configuration failed to load\")}' '${root.filePath}'`])\n            return;\n        }\n    }\n\n    function setLiveConfigValue(nestedKey, value) {\n        let keys = nestedKey.split(\".\");\n        let obj = ConfigOptions;\n        let parents = [obj];\n\n        // Traverse and collect parent objects\n        for (let i = 0; i < keys.length - 1; ++i) {\n            if (!obj[keys[i]] || typeof obj[keys[i]] !== \"object\") {\n                obj[keys[i]] = {};\n            }\n            obj = obj[keys[i]];\n            parents.push(obj);\n        }\n\n        // Convert value to correct type using JSON.parse when safe\n        let convertedValue = value;\n        if (typeof value === \"string\") {\n            let trimmed = value.trim();\n            if (trimmed === \"true\" || trimmed === \"false\" || !isNaN(Number(trimmed))) {\n                try {\n                    convertedValue = JSON.parse(trimmed);\n                } catch (e) {\n                    convertedValue = value;\n                }\n            }\n        }\n\n        obj[keys[keys.length - 1]] = convertedValue;\n    }\n\n    function saveConfig() {\n        const plainConfig = ObjectUtils.toPlainObject(ConfigOptions)\n        Quickshell.execDetached([\"bash\", \"-c\", `echo '${StringUtils.shellSingleQuoteEscape(JSON.stringify(plainConfig, null, 2))}' > '${FileUtils.trimFileProtocol(root.filePath)}'`])\n    }\n\n    function setConfigValueAndSave(nestedKey, value, preventNextNotification = true) {\n        setLiveConfigValue(nestedKey, value);\n        root.preventNextNotification = preventNextNotification;\n        saveConfig();\n    }\n\n    Timer {\n        id: delayedFileRead\n        interval: ConfigOptions.hacks.arbitraryRaceConditionDelay\n        running: false\n        onTriggered: {\n            if (root.preventNextLoad) {\n                root.preventNextLoad = false;\n                return;\n            }\n            if (root.firstLoad) {\n                root.applyConfig(configFileView.text())\n            } else {\n                root.applyConfig(configFileView.text())\n                if (!root.preventNextNotification) {\n                    // Quickshell.execDetached([\"bash\", \"-c\", `notify-send '${qsTr(\"Shell configuration reloaded\")}' '${root.filePath}'`])\n                } else {\n                    root.preventNextNotification = false;\n                }\n            }\n        }\n    }\n\n\tFileView { \n        id: configFileView\n        path: Qt.resolvedUrl(root.filePath)\n        watchChanges: true\n        onFileChanged: {\n            this.reload()\n            delayedFileRead.start()\n        }\n        onLoadedChanged: {\n            const fileContent = configFileView.text()\n            delayedFileRead.start()\n        }\n        onLoadFailed: (error) => {\n            if(error == FileViewError.FileNotFound) {\n                console.log(\"[ConfigLoader] File not found, creating new file.\")\n                root.saveConfig()\n                Quickshell.execDetached([\"bash\", \"-c\", `notify-send '${qsTr(\"Shell configuration created\")}' '${root.filePath}'`])\n            } else {\n                Quickshell.execDetached([\"bash\", \"-c\", `notify-send '${qsTr(\"Shell configuration failed to load\")}' '${root.filePath}'`])\n            }\n        }\n    }\n}\n", "modifiedCode": "pragma Singleton\npragma ComponentBehavior: Bound\n\nimport \"root:/modules/common\"\nimport \"root:/modules/common/functions/file_utils.js\" as FileUtils\nimport \"root:/modules/common/functions/string_utils.js\" as StringUtils\nimport \"root:/modules/common/functions/object_utils.js\" as ObjectUtils\nimport QtQuick\nimport Quickshell\nimport Quickshell.Io\nimport Quickshell.Hyprland\nimport Qt.labs.platform\n\n/**\n * Loads and manages the shell configuration file.\n * The config file is by default at XDG_CONFIG_HOME/illogical-impulse/config.json.\n * Automatically reloaded when the file changes.\n */\nSingleton {\n    id: root\n    property string filePath: Directories.shellConfigPath\n    property bool firstLoad: true\n    property bool preventNextLoad: false\n    property var preventNextNotification: false\n\n    function loadConfig() {\n        configFileView.reload()\n    }\n\n    function applyConfig(fileContent) {\n        try {\n            if (fileContent.trim() === \"\") {\n                console.warn(\"[ConfigLoader] Config file is empty, skipping load.\");\n                return;\n            }\n\n            // Check if content appears to be truncated\n            const trimmedContent = fileContent.trim();\n            if (!trimmedContent.endsWith('}') && !trimmedContent.endsWith(']')) {\n                console.warn(\"[ConfigLoader] File content appears to be truncated, retrying in 100ms...\");\n                // Retry after a short delay\n                Qt.callLater(() => {\n                    delayedFileRead.interval = 100;\n                    delayedFileRead.start();\n                });\n                return;\n            }\n\n            const json = JSON.parse(fileContent);\n\n            ObjectUtils.applyToQtObject(ConfigOptions, json);\n            if (root.firstLoad) {\n                root.firstLoad = false;\n                root.preventNextLoad = true;\n                root.saveConfig(); // Make sure new properties are added to the user's config file\n            }\n        } catch (e) {\n            console.error(\"[ConfigLoader] Error reading file:\", e);\n            console.log(\"[ConfigLoader] File content was:\", fileContent);\n            console.log(\"[ConfigLoader] File content length:\", fileContent.length);\n            console.log(\"[ConfigLoader] File content ends with:\", fileContent.slice(-50));\n\n            // If it's a parse error and content seems truncated, retry\n            if (e.toString().includes(\"Parse error\") && !fileContent.trim().endsWith('}')) {\n                console.warn(\"[ConfigLoader] Retrying due to apparent truncation...\");\n                Qt.callLater(() => {\n                    delayedFileRead.interval = 200;\n                    delayedFileRead.start();\n                });\n                return;\n            }\n\n            Quickshell.execDetached([\"bash\", \"-c\", `notify-send '${qsTr(\"Shell configuration failed to load\")}' '${root.filePath}'`])\n            return;\n        }\n    }\n\n    function setLiveConfigValue(nestedKey, value) {\n        let keys = nestedKey.split(\".\");\n        let obj = ConfigOptions;\n        let parents = [obj];\n\n        // Traverse and collect parent objects\n        for (let i = 0; i < keys.length - 1; ++i) {\n            if (!obj[keys[i]] || typeof obj[keys[i]] !== \"object\") {\n                obj[keys[i]] = {};\n            }\n            obj = obj[keys[i]];\n            parents.push(obj);\n        }\n\n        // Convert value to correct type using JSON.parse when safe\n        let convertedValue = value;\n        if (typeof value === \"string\") {\n            let trimmed = value.trim();\n            if (trimmed === \"true\" || trimmed === \"false\" || !isNaN(Number(trimmed))) {\n                try {\n                    convertedValue = JSON.parse(trimmed);\n                } catch (e) {\n                    convertedValue = value;\n                }\n            }\n        }\n\n        obj[keys[keys.length - 1]] = convertedValue;\n    }\n\n    function saveConfig() {\n        const plainConfig = ObjectUtils.toPlainObject(ConfigOptions)\n        Quickshell.execDetached([\"bash\", \"-c\", `echo '${StringUtils.shellSingleQuoteEscape(JSON.stringify(plainConfig, null, 2))}' > '${FileUtils.trimFileProtocol(root.filePath)}'`])\n    }\n\n    function setConfigValueAndSave(nestedKey, value, preventNextNotification = true) {\n        setLiveConfigValue(nestedKey, value);\n        root.preventNextNotification = preventNextNotification;\n        saveConfig();\n    }\n\n    Timer {\n        id: delayedFileRead\n        interval: ConfigOptions.hacks.arbitraryRaceConditionDelay\n        running: false\n        onTriggered: {\n            // Reset interval to default for next time\n            interval = ConfigOptions.hacks.arbitraryRaceConditionDelay;\n\n            if (root.preventNextLoad) {\n                root.preventNextLoad = false;\n                return;\n            }\n            if (root.firstLoad) {\n                root.applyConfig(configFileView.text())\n            } else {\n                root.applyConfig(configFileView.text())\n                if (!root.preventNextNotification) {\n                    // Quickshell.execDetached([\"bash\", \"-c\", `notify-send '${qsTr(\"Shell configuration reloaded\")}' '${root.filePath}'`])\n                } else {\n                    root.preventNextNotification = false;\n                }\n            }\n        }\n    }\n\n\tFileView { \n        id: configFileView\n        path: Qt.resolvedUrl(root.filePath)\n        watchChanges: true\n        onFileChanged: {\n            this.reload()\n            delayedFileRead.start()\n        }\n        onLoadedChanged: {\n            const fileContent = configFileView.text()\n            delayedFileRead.start()\n        }\n        onLoadFailed: (error) => {\n            if(error == FileViewError.FileNotFound) {\n                console.log(\"[ConfigLoader] File not found, creating new file.\")\n                root.saveConfig()\n                Quickshell.execDetached([\"bash\", \"-c\", `notify-send '${qsTr(\"Shell configuration created\")}' '${root.filePath}'`])\n            } else {\n                Quickshell.execDetached([\"bash\", \"-c\", `notify-send '${qsTr(\"Shell configuration failed to load\")}' '${root.filePath}'`])\n            }\n        }\n    }\n}\n"}