{"path": {"rootPath": "/home/<USER>/.config", "relPath": "test_ai_config.md"}, "modifiedCode": "# AI配置系统测试指南\n\n## 测试步骤\n\n### 1. 基本功能测试\n\n#### 启动设置窗口\n```bash\nqs -p quickshell/settings.qml\n```\n\n#### 验证AI页面\n- [ ] 设置窗口启动后自动导航到AI页面\n- [ ] 左侧导航栏显示\"AI\"选项（带神经网络图标）\n- [ ] AI页面正确加载，显示所有配置部分\n\n### 2. 模型配置测试\n\n#### 当前模型选择\n- [ ] 下拉选择器显示所有可用模型\n- [ ] 当前选择的模型正确高亮\n- [ ] 模型信息区域显示正确的图标、名称、描述\n- [ ] 端点URL和密钥要求状态正确显示\n\n#### 模型切换\n- [ ] 选择不同模型时界面立即更新\n- [ ] 模型信息区域反映新选择的模型\n- [ ] API密钥管理部分根据模型要求显示/隐藏\n\n### 3. API密钥管理测试\n\n#### 密钥输入\n- [ ] 密钥输入框默认为密码模式（隐藏文本）\n- [ ] \"显示\"开关可以切换密钥可见性\n- [ ] 输入密钥后\"保存密钥\"按钮变为可用\n\n#### 密钥操作\n- [ ] 保存密钥功能正常工作\n- [ ] 清除密钥功能正常工作\n- [ ] 获取密钥按钮（如果模型支持）正确跳转到外部链接\n\n### 4. 自定义模型测试\n\n#### 添加自定义模型\n- [ ] 所有输入字段正常工作\n- [ ] API格式选择器（OpenAI/Gemini）正常工作\n- [ ] \"需要API密钥\"开关正常工作\n- [ ] 填写必要信息后\"添加模型\"按钮变为可用\n- [ ] 成功添加模型后输入字段自动清空\n\n#### 模型管理\n- [ ] 模型管理列表显示所有模型\n- [ ] 自定义模型显示\"自定义\"标签\n- [ ] 当前模型正确高亮显示\n- [ ] \"选择\"按钮可以切换模型\n- [ ] \"删除\"按钮仅对自定义模型显示\n\n#### 删除确认\n- [ ] 点击删除按钮显示确认对话框\n- [ ] 确认对话框显示正确的模型名称\n- [ ] 确认删除后模型从列表中移除\n- [ ] 取消删除操作正常工作\n\n### 5. 模型参数测试\n\n#### 温度设置\n- [ ] 温度滑块正常工作（0-2.0范围）\n- [ ] 当前值实时显示\n- [ ] 设置值立即保存并生效\n\n#### 系统提示\n- [ ] 多行文本输入框正常工作\n- [ ] 文本自动换行\n- [ ] 输入内容自动保存\n\n### 6. 高级设置测试\n\n#### 请求配置\n- [ ] 请求超时设置正常工作\n- [ ] 最大重试次数设置正常工作\n- [ ] 流式响应开关正常工作\n- [ ] 保存聊天历史开关正常工作\n\n#### 安全设置\n- [ ] \"仅允许本地模型\"开关正常工作\n- [ ] \"验证SSL证书\"开关正常工作\n\n### 7. 聊天界面集成测试\n\n#### 模型信息显示\n- [ ] AI聊天界面显示当前模型信息\n- [ ] 模型图标、名称正确显示\n- [ ] 配置状态（自定义、密钥状态）正确显示\n\n#### 快速模型选择\n- [ ] 点击模型信息区域打开模型选择器\n- [ ] 模型选择器显示所有可用模型\n- [ ] 当前模型正确标识\n- [ ] 点击模型可以快速切换\n- [ ] \"管理模型\"按钮打开设置窗口\n\n#### 设置访问\n- [ ] 设置按钮正确显示\n- [ ] 点击设置按钮打开设置窗口\n- [ ] 设置窗口自动导航到AI页面\n\n### 8. 配置持久化测试\n\n#### 设置保存\n- [ ] 所有配置更改自动保存\n- [ ] 重启应用后配置保持不变\n- [ ] 自定义模型在重启后仍然可用\n\n#### 导入/导出\n- [ ] 导出配置功能正常工作\n- [ ] 重置为默认功能正常工作\n- [ ] 重置确认对话框正常显示\n\n### 9. 错误处理测试\n\n#### 输入验证\n- [ ] 空的模型名称无法添加\n- [ ] 空的端点URL无法添加\n- [ ] 无效的配置显示适当的错误信息\n\n#### 边界情况\n- [ ] 删除当前使用的自定义模型时自动切换到其他模型\n- [ ] 没有可用模型时界面正确处理\n- [ ] 网络错误时API密钥验证正确处理\n\n### 10. 用户体验测试\n\n#### 界面响应\n- [ ] 所有操作响应迅速\n- [ ] 动画和过渡效果流畅\n- [ ] 工具提示正确显示\n\n#### 可用性\n- [ ] 界面布局清晰易懂\n- [ ] 操作流程符合直觉\n- [ ] 错误信息清晰有用\n\n## 已知问题\n\n1. ~~`QtGraphicalEffects` 模块不可用~~ ✅ 已修复\n2. ~~`Qt.openUrlExternally()` 不能用于内部导航~~ ✅ 已修复\n\n## 测试结果\n\n- [ ] 所有基本功能正常工作\n- [ ] 模型管理功能完整\n- [ ] API密钥管理安全可靠\n- [ ] 配置持久化正常\n- [ ] 用户界面友好直观\n\n## 备注\n\n测试时请确保：\n1. QuickShell环境正确配置\n2. AI服务正常运行\n3. 配置文件权限正确\n4. 网络连接正常（用于API密钥验证）\n"}