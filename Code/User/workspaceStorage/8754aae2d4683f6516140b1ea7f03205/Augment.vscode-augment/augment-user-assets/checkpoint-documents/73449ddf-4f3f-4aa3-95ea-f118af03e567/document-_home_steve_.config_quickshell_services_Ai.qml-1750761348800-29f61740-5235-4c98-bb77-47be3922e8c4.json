{"path": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/services/Ai.qml"}, "originalCode": "pragma Singleton\npragma ComponentBehavior: Bound\n\nimport \"root:/modules/common/functions/string_utils.js\" as StringUtils\nimport \"root:/modules/common/functions/object_utils.js\" as ObjectUtils\nimport \"root:/modules/common\"\nimport Quickshell;\nimport Quickshell.Io;\nimport Qt.labs.platform\nimport QtQuick;\n\n/**\n * Basic service to handle LLM chats. Supports Google's and OpenAI's API formats.\n */\nSingleton {\n    id: root\n\n    readonly property string interfaceRole: \"interface\"\n    readonly property string apiKeyEnvVarName: \"API_KEY\"\n    property Component aiMessageComponent: AiMessageData {}\n    property string systemPrompt: ConfigOptions?.ai?.systemPrompt ?? \"\"\n    property var messages: []\n    property var messageIDs: []\n    property var messageByID: ({})\n    readonly property var apiKeys: KeyringStorage.keyringData?.apiKeys ?? {}\n    readonly property var apiKeysLoaded: KeyringStorage.loaded\n    property var postResponseHook\n    property real temperature: PersistentStates?.ai?.temperature ?? 0.5\n\n    function idForMessage(message) {\n        // Generate a unique ID using timestamp and random value\n        return Date.now().toString(36) + Math.random().toString(36).substr(2, 8);\n    }\n\n    function safeModelName(modelName) {\n        return modelName.replace(/:/g, \"_\").replace(/\\./g, \"_\")\n    }\n\n    // Model properties:\n    // - name: Name of the model\n    // - icon: Icon name of the model\n    // - description: Description of the model\n    // - endpoint: Endpoint of the model\n    // - model: Model name of the model\n    // - requires_key: Whether the model requires an API key\n    // - key_id: The identifier of the API key. Use the same identifier for models that can be accessed with the same key.\n    // - key_get_link: Link to get an API key\n    // - key_get_description: Description of pricing and how to get an API key\n    // - api_format: The API format of the model. Can be \"openai\" or \"gemini\". Default is \"openai\".\n    // - tools: List of tools that the model can use. Each tool is an object with the tool name as the key and an empty object as the value.\n    // - extraParams: Extra parameters to be passed to the model. This is a JSON object.\n    property var models: {\n        \"gemini-2.0-flash-search\": {\n            \"name\": \"Gemini 2.0 Flash (Search)\",\n            \"icon\": \"google-gemini-symbolic\",\n            \"description\": qsTr(\"Online | Google's model\\nGives up-to-date information with search.\"),\n            \"homepage\": \"https://aistudio.google.com\",\n            \"endpoint\": \"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:streamGenerateContent\",\n            \"model\": \"gemini-2.0-flash\",\n            \"requires_key\": true,\n            \"key_id\": \"gemini\",\n            \"key_get_link\": \"https://aistudio.google.com/app/apikey\",\n            \"key_get_description\": qsTr(\"**Pricing**: free. Data used for training.\\n\\n**Instructions**: Log into Google account, allow AI Studio to create Google Cloud project or whatever it asks, go back and click Get API key\"),\n            \"api_format\": \"gemini\",\n            \"tools\": [\n                {\n                    \"google_search\": {}\n                },\n            ]\n        },\n        \"gemini-2.0-flash-tools\": {\n            \"name\": \"Gemini 2.0 Flash (Tools)\",\n            \"icon\": \"google-gemini-symbolic\",\n            \"description\": qsTr(\"Experimental | Online | Google's model\\nCan do a little more but doesn't search quickly\"),\n            \"homepage\": \"https://aistudio.google.com\",\n            \"endpoint\": \"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:streamGenerateContent\",\n            \"model\": \"gemini-2.0-flash\",\n            \"requires_key\": true,\n            \"key_id\": \"gemini\",\n            \"key_get_link\": \"https://aistudio.google.com/app/apikey\",\n            \"key_get_description\": qsTr(\"**Pricing**: free. Data used for training.\\n\\n**Instructions**: Log into Google account, allow AI Studio to create Google Cloud project or whatever it asks, go back and click Get API key\"),\n            \"api_format\": \"gemini\",\n            \"tools\": [\n                {\n                    \"functionDeclarations\": [\n                        {\n                            \"name\": \"switch_to_search_mode\",\n                            \"description\": \"Search the web\",\n                        },\n                        {\n                            \"name\": \"get_shell_config\",\n                            \"description\": \"Get the desktop shell config file contents\",\n                        },\n                        {\n                            \"name\": \"set_shell_config\",\n                            \"description\": \"Set a field in the desktop graphical shell config file. Must only be used after `get_shell_config`.\",\n                            \"parameters\": {\n                                \"type\": \"object\",\n                                \"properties\": {\n                                    \"key\": {\n                                        \"type\": \"string\",\n                                        \"description\": \"The key to set, e.g. `bar.borderless`. MUST NOT BE GUESSED, use `get_shell_config` to see what keys are available before setting.\",\n                                    },\n                                    \"value\": {\n                                        \"type\": \"string\",\n                                        \"description\": \"The value to set, e.g. `true`\"\n                                    }\n                                },\n                                \"required\": [\"key\", \"value\"]\n                            }\n                        },\n                    ]\n                }\n            ]\n        },\n        \"gemini-2.5-flash-search\": {\n            \"name\": \"Gemini 2.5 Flash (Search)\",\n            \"icon\": \"google-gemini-symbolic\",\n            \"description\": qsTr(\"Online | Google's model\\nGives up-to-date information with search.\"),\n            \"homepage\": \"https://aistudio.google.com\",\n            \"endpoint\": \"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:streamGenerateContent\",\n            \"model\": \"gemini-2.5-flash-preview-05-20\",\n            \"requires_key\": true,\n            \"key_id\": \"gemini\",\n            \"key_get_link\": \"https://aistudio.google.com/app/apikey\",\n            \"key_get_description\": qsTr(\"**Pricing**: free. Data used for training.\\n\\n**Instructions**: Log into Google account, allow AI Studio to create Google Cloud project or whatever it asks, go back and click Get API key\"),\n            \"api_format\": \"gemini\",\n            \"tools\": [\n                {\n                    \"google_search\": ({})\n                },\n            ]\n        },\n        \"gemini-2.5-flash-tools\": {\n            \"name\": \"Gemini 2.5 Flash (Tools)\",\n            \"icon\": \"google-gemini-symbolic\",\n            \"description\": qsTr(\"Experimental | Online | Google's model\\nCan do a little more but doesn't search quickly\"),\n            \"homepage\": \"https://aistudio.google.com\",\n            \"endpoint\": \"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:streamGenerateContent\",\n            \"model\": \"gemini-2.5-flash-preview-05-20\",\n            \"requires_key\": true,\n            \"key_id\": \"gemini\",\n            \"key_get_link\": \"https://aistudio.google.com/app/apikey\",\n            \"key_get_description\": qsTr(\"**Pricing**: free. Data used for training.\\n\\n**Instructions**: Log into Google account, allow AI Studio to create Google Cloud project or whatever it asks, go back and click Get API key\"),\n            \"api_format\": \"gemini\",\n            \"tools\": [\n                {\n                    \"functionDeclarations\": [\n                        {\n                            \"name\": \"switch_to_search_mode\",\n                            \"description\": \"Search the web\",\n                        },\n                        {\n                            \"name\": \"get_shell_config\",\n                            \"description\": \"Get the desktop shell config file contents\",\n                        },\n                        {\n                            \"name\": \"set_shell_config\",\n                            \"description\": \"Set a field in the desktop graphical shell config file. Must only be used after `get_shell_config`.\",\n                            \"parameters\": {\n                                \"type\": \"object\",\n                                \"properties\": {\n                                    \"key\": {\n                                        \"type\": \"string\",\n                                        \"description\": \"The key to set, e.g. `bar.borderless`. MUST NOT BE GUESSED, use `get_shell_config` to see what keys are available before setting.\",\n                                    },\n                                    \"value\": {\n                                        \"type\": \"string\",\n                                        \"description\": \"The value to set, e.g. `true`\"\n                                    }\n                                },\n                                \"required\": [\"key\", \"value\"]\n                            }\n                        },\n                    ]\n                }\n            ]\n        },\n        \"openrouter-llama4-maverick\": {\n            \"name\": \"Llama 4 Maverick\",\n            \"icon\": \"ollama-symbolic\",\n            \"description\": StringUtils.format(qsTr(\"Online via {0} | {1}'s model\"), \"OpenRouter\", \"Meta\"),\n            \"homepage\": \"https://openrouter.ai/meta-llama/llama-4-maverick:free\",\n            \"endpoint\": \"https://openrouter.ai/api/v1/chat/completions\",\n            \"model\": \"meta-llama/llama-4-maverick:free\",\n            \"requires_key\": true,\n            \"key_id\": \"openrouter\",\n            \"key_get_link\": \"https://openrouter.ai/settings/keys\",\n            \"key_get_description\": qsTr(\"**Pricing**: free. Data use policy varies depending on your OpenRouter account settings.\\n\\n**Instructions**: Log into OpenRouter account, go to Keys on the topright menu, click Create API Key\"),\n        },\n        \"openrouter-deepseek-r1\": {\n            \"name\": \"DeepSeek R1\",\n            \"icon\": \"deepseek-symbolic\",\n            \"description\": StringUtils.format(qsTr(\"Online via {0} | {1}'s model\"), \"OpenRouter\", \"DeepSeek\"),\n            \"homepage\": \"https://openrouter.ai/deepseek/deepseek-r1:free\",\n            \"endpoint\": \"https://openrouter.ai/api/v1/chat/completions\",\n            \"model\": \"deepseek/deepseek-r1:free\",\n            \"requires_key\": true,\n            \"key_id\": \"openrouter\",\n            \"key_get_link\": \"https://openrouter.ai/settings/keys\",\n            \"key_get_description\": qsTr(\"**Pricing**: free. Data use policy varies depending on your OpenRouter account settings.\\n\\n**Instructions**: Log into OpenRouter account, go to Keys on the topright menu, click Create API Key\"),\n        },\n    }\n    property var modelList: Object.keys(root.models)\n    property var currentModelId: PersistentStates?.ai?.model || modelList[0]\n\n    Component.onCompleted: {\n        setModel(currentModelId, false); // Do necessary setup for model\n        getOllamaModels.running = true\n    }\n\n    function guessModelLogo(model) {\n        if (model.includes(\"llama\")) return \"ollama-symbolic\";\n        if (model.includes(\"gemma\")) return \"google-gemini-symbolic\";\n        if (model.includes(\"deepseek\")) return \"deepseek-symbolic\";\n        if (/^phi\\d*:/i.test(model)) return \"microsoft-symbolic\";\n        return \"ollama-symbolic\";\n    }\n\n    function guessModelName(model) {\n        const replaced = model.replace(/-/g, ' ').replace(/:/g, ' ');\n        let words = replaced.split(' ');\n        words[words.length - 1] = words[words.length - 1].replace(/(\\d+)b$/, (_, num) => `${num}B`)\n        words = words.map((word) => {\n            return (word.charAt(0).toUpperCase() + word.slice(1))\n        });\n        if (words[words.length - 1] === \"Latest\") words.pop();\n        else words[words.length - 1] = `(${words[words.length - 1]})`; // Surround the last word with square brackets\n        const result = words.join(' ');\n        return result;\n    }\n\n    Process {\n        id: getOllamaModels\n        command: [\"bash\", \"-c\", `${Directories.config}/quickshell/scripts/ai/show-installed-ollama-models.sh`.replace(/file:\\/\\//, \"\")]\n        stdout: SplitParser {\n            onRead: data => {\n                try {\n                    if (data.length === 0) return;\n                    const dataJson = JSON.parse(data);\n                    root.modelList = [...root.modelList, ...dataJson];\n                    dataJson.forEach(model => {\n                        const safeModelName = root.safeModelName(model);\n                        root.models[safeModelName] = {\n                            \"name\": guessModelName(model),\n                            \"icon\": guessModelLogo(model),\n                            \"description\": StringUtils.format(qsTr(\"Local Ollama model | {0}\"), model),\n                            \"homepage\": `https://ollama.com/library/${model}`,\n                            \"endpoint\": \"http://localhost:11434/v1/chat/completions\",\n                            \"model\": model,\n                        }\n                    });\n\n                    root.modelList = Object.keys(root.models);\n\n                } catch (e) {\n                    console.log(\"Could not fetch Ollama models:\", e);\n                }\n            }\n        }\n    }\n\n    function addMessage(message, role) {\n        if (message.length === 0) return;\n        const aiMessage = aiMessageComponent.createObject(root, {\n            \"role\": role,\n            \"content\": message,\n            \"thinking\": false,\n            \"done\": true,\n        });\n        const id = idForMessage(aiMessage);\n        root.messageIDs = [...root.messageIDs, id];\n        root.messageByID[id] = aiMessage;\n    }\n\n    function removeMessage(index) {\n        if (index < 0 || index >= messageIDs.length) return;\n        const id = root.messageIDs[index];\n        root.messageIDs.splice(index, 1);\n        root.messageIDs = [...root.messageIDs];\n        delete root.messageByID[id];\n    }\n\n    function addApiKeyAdvice(model) {\n        root.addMessage(\n            StringUtils.format(qsTr('To set an API key, pass it with the command\\n\\nTo view the key, pass \"get\" with the command<br/>\\n\\n### For {0}:\\n\\n**Link**: {1}\\n\\n{2}'), \n                model.name, model.key_get_link, model.key_get_description ?? qsTr(\"<i>No further instruction provided</i>\")), \n            Ai.interfaceRole\n        );\n    }\n\n    function getModel() {\n        return models[currentModelId];\n    }\n\n    function setModel(modelId, feedback = true) {\n        if (!modelId) modelId = \"\"\n        modelId = modelId.toLowerCase()\n        if (modelList.indexOf(modelId) !== -1) {\n            const model = models[modelId]\n            // Fetch API keys if needed\n            if (model?.requires_key) KeyringStorage.fetchKeyringData();\n            // See if policy prevents online models\n            if (ConfigOptions.policies.ai === 2 && !model.endpoint.includes(\"localhost\")) {\n                root.addMessage(StringUtils.format(StringUtils.format(\"Online models disallowed\\n\\nControlled by `policies.ai` config option\"), model.name), root.interfaceRole);\n                return;\n            }\n            PersistentStateManager.setState(\"ai.model\", modelId);\n            if (feedback) root.addMessage(StringUtils.format(StringUtils.format(\"Model set to {0}\"), model.name), root.interfaceRole);\n            if (model.requires_key) {\n                // If key not there show advice\n                if (root.apiKeysLoaded && (!root.apiKeys[model.key_id] || root.apiKeys[model.key_id].length === 0)) {\n                    root.addApiKeyAdvice(model)\n                }\n            }\n        } else {\n            if (feedback) root.addMessage(qsTr(\"Invalid model. Supported: \\n```\\n\") + modelList.join(\"\\n```\\n```\\n\"), Ai.interfaceRole) + \"\\n```\"\n        }\n    }\n    \n    function getTemperature() {\n        return root.temperature;\n    }\n\n    function setTemperature(value) {\n        if (value == NaN || value < 0 || value > 2) {\n            root.addMessage(qsTr(\"Temperature must be between 0 and 2\"), Ai.interfaceRole);\n            return;\n        }\n        PersistentStateManager.setState(\"ai.temperature\", value);\n        root.temperature = value;\n        root.addMessage(StringUtils.format(qsTr(\"Temperature set to {0}\"), value), Ai.interfaceRole);\n    }\n\n    function setApiKey(key) {\n        const model = models[currentModelId];\n        if (!model.requires_key) {\n            root.addMessage(StringUtils.format(qsTr(\"{0} does not require an API key\"), model.name), Ai.interfaceRole);\n            return;\n        }\n        if (!key || key.length === 0) {\n            const model = models[currentModelId];\n            root.addApiKeyAdvice(model)\n            return;\n        }\n        KeyringStorage.setNestedField([\"apiKeys\", model.key_id], key.trim());\n        root.addMessage(StringUtils.format(qsTr(\"API key set for {0}\"), model.name, Ai.interfaceRole));\n    }\n\n    function printApiKey() {\n        const model = models[currentModelId];\n        if (model.requires_key) {\n            const key = root.apiKeys[model.key_id];\n            if (key) {\n                root.addMessage(StringUtils.format(qsTr(\"API key:\\n\\n```txt\\n{0}\\n```\"), key), Ai.interfaceRole);\n            } else {\n                root.addMessage(StringUtils.format(qsTr(\"No API key set for {0}\"), model.name), Ai.interfaceRole);\n            }\n        } else {\n            root.addMessage(StringUtils.format(qsTr(\"{0} does not require an API key\"), model.name), Ai.interfaceRole);\n        }\n    }\n\n    function printTemperature() {\n        root.addMessage(StringUtils.format(qsTr(\"Temperature: {0}\"), root.temperature), Ai.interfaceRole);\n    }\n\n    function clearMessages() {\n        root.messageIDs = [];\n        root.messageByID = ({});\n    }\n\n    function addCustomModel(modelConfig) {\n        const modelId = modelConfig.name.toLowerCase().replace(/[^a-z0-9]/g, '-');\n        root.models[modelId] = {\n            \"name\": modelConfig.displayName || modelConfig.name,\n            \"icon\": modelConfig.icon || \"smart_toy\",\n            \"description\": modelConfig.description || qsTr(\"自定义模型\"),\n            \"endpoint\": modelConfig.endpoint,\n            \"model\": modelConfig.modelId || modelConfig.name,\n            \"requires_key\": modelConfig.requiresKey !== false,\n            \"key_id\": modelConfig.keyId || \"custom\",\n            \"api_format\": modelConfig.apiFormat || \"openai\",\n            \"custom\": true\n        };\n        root.modelList = Object.keys(root.models);\n\n        // 保存自定义模型到配置\n        const customModels = ConfigOptions.ai?.customModels || {};\n        customModels[modelId] = root.models[modelId];\n        ConfigLoader.setConfigValueAndSave(\"ai.customModels\", customModels);\n\n        root.addMessage(StringUtils.format(qsTr(\"已添加自定义模型: {0}\"), modelConfig.displayName || modelConfig.name), root.interfaceRole);\n        return modelId;\n    }\n\n    function removeCustomModel(modelId) {\n        if (!root.models[modelId]?.custom) {\n            root.addMessage(qsTr(\"只能删除自定义模型\"), root.interfaceRole);\n            return false;\n        }\n\n        delete root.models[modelId];\n        root.modelList = Object.keys(root.models);\n\n        // 从配置中移除\n        const customModels = ConfigOptions.ai?.customModels || {};\n        delete customModels[modelId];\n        ConfigLoader.setConfigValueAndSave(\"ai.customModels\", customModels);\n\n        // 如果当前模型被删除，切换到默认模型\n        if (root.currentModelId === modelId) {\n            root.setModel(root.modelList[0], true);\n        }\n\n        root.addMessage(StringUtils.format(qsTr(\"已删除自定义模型: {0}\"), modelId), root.interfaceRole);\n        return true;\n    }\n\n    Component.onCompleted: {\n        // 加载自定义模型\n        const customModels = ConfigOptions.ai?.customModels || {};\n        Object.keys(customModels).forEach(modelId => {\n            root.models[modelId] = customModels[modelId];\n        });\n\n        setModel(currentModelId, false); // Do necessary setup for model\n        getOllamaModels.running = true\n    }\n\n    Process {\n        id: requester\n        property var baseCommand: [\"bash\", \"-c\"]\n        property var message\n        property bool isReasoning\n        property string apiFormat: \"openai\"\n        property string geminiBuffer: \"\"\n\n        function buildGeminiEndpoint(model) {\n            // console.log(\"ENDPOINT: \" + model.endpoint + `?key=\\$\\{${root.apiKeyEnvVarName}\\}`)\n            return model.endpoint + `?key=\\$\\{${root.apiKeyEnvVarName}\\}`;\n        }\n\n        function buildOpenAIEndpoint(model) {\n            return model.endpoint;\n        }\n\n        function markDone() {\n            requester.message.done = true;\n            if (root.postResponseHook) {\n                root.postResponseHook();\n                root.postResponseHook = null; // Reset hook after use\n            }\n        }\n\n        function buildGeminiRequestData(model, messages) {\n            let baseData = {\n                \"contents\": messages.filter(message => (message.role != Ai.interfaceRole)).map(message => {\n                    const geminiApiRoleName = (message.role === \"assistant\") ? \"model\" : message.role;\n                    const usingSearch = model.tools[0].google_search != undefined                \n                    if (!usingSearch && message.functionCall != undefined && message.functionCall.length > 0) {\n                        return {\n                            \"role\": geminiApiRoleName,\n                            \"parts\": [{ \n                                functionCall: {\n                                    \"name\": message.functionName,\n                                }\n                            }]\n                        }\n                    }\n                    if (!usingSearch && message.functionResponse != undefined && message.functionResponse.length > 0) {\n                        return {\n                            \"role\": geminiApiRoleName,\n                            \"parts\": [{ \n                                functionResponse: {\n                                    \"name\": message.functionName,\n                                    \"response\": { \"content\": message.functionResponse }\n                                }\n                            }]\n                        }\n                    }\n                    return {\n                        \"role\": geminiApiRoleName,\n                        \"parts\": [{ \n                            text: message.content,\n                        }]\n                    }\n                }),\n                \"tools\": [\n                    ...model.tools,\n                ],\n                \"system_instruction\": {\n                    \"parts\": [{ text: root.systemPrompt }]\n                },\n                \"generationConfig\": {\n                    // \"temperature\": root.temperature,\n                },\n            };\n            return model.extraParams ? Object.assign({}, baseData, model.extraParams) : baseData;\n        }\n\n        function buildOpenAIRequestData(model, messages) {\n            let baseData = {\n                \"model\": model.model,\n                \"messages\": [\n                    {role: \"system\", content: root.systemPrompt},\n                    ...messages.filter(message => (message.role != Ai.interfaceRole)).map(message => {\n                        return {\n                            \"role\": message.role,\n                            \"content\": message.content,\n                        }\n                    }),\n                ],\n                \"stream\": true,\n                // \"temperature\": root.temperature,\n            };\n            return model.extraParams ? Object.assign({}, baseData, model.extraParams) : baseData;\n        }\n\n        function makeRequest() {\n            const model = models[currentModelId];\n            requester.apiFormat = model.api_format ?? \"openai\";\n\n            /* Put API key in environment variable */\n            if (model.requires_key) requester.environment[`${root.apiKeyEnvVarName}`] = root.apiKeys ? (root.apiKeys[model.key_id] ?? \"\") : \"\"\n\n            /* Build endpoint, request data */\n            const endpoint = (apiFormat === \"gemini\") ? buildGeminiEndpoint(model) : buildOpenAIEndpoint(model);\n            const messageArray = root.messageIDs.map(id => root.messageByID[id]);\n            const data = (apiFormat === \"gemini\") ? buildGeminiRequestData(model, messageArray) : buildOpenAIRequestData(model, messageArray);\n            // console.log(\"REQUEST DATA: \", JSON.stringify(data, null, 2));\n\n            let requestHeaders = {\n                \"Content-Type\": \"application/json\",\n            }\n            \n            /* Create local message object */\n            requester.message = root.aiMessageComponent.createObject(root, {\n                \"role\": \"assistant\",\n                \"model\": currentModelId,\n                \"content\": \"\",\n                \"thinking\": true,\n                \"done\": false,\n            });\n            const id = idForMessage(requester.message);\n            root.messageIDs = [...root.messageIDs, id];\n            root.messageByID[id] = requester.message;\n\n            /* Build header string for curl */ \n            let headerString = Object.entries(requestHeaders)\n                .filter(([k, v]) => v && v.length > 0)\n                .map(([k, v]) => `-H '${k}: ${v}'`)\n                .join(' ');\n\n            // console.log(\"Request headers: \", JSON.stringify(requestHeaders));\n            // console.log(\"Header string: \", headerString);\n\n            /* Create command string */\n            const requestCommandString = `curl --no-buffer \"${endpoint}\"`\n                + ` ${headerString}`\n                + ((apiFormat == \"gemini\") ? \"\" : ` -H \"Authorization: Bearer \\$\\{${root.apiKeyEnvVarName}\\}\"`)\n                + ` -d '${StringUtils.shellSingleQuoteEscape(JSON.stringify(data))}'`\n            // console.log(\"Request command: \", requestCommandString);\n            requester.command = baseCommand.concat([requestCommandString]);\n\n            /* Reset vars and make the request */\n            requester.isReasoning = false\n            requester.running = true\n        }\n\n        function parseGeminiBuffer() {\n            // console.log(\"BUFFER DATA: \", requester.geminiBuffer);\n            try {\n                if (requester.geminiBuffer.length === 0) return;\n                const dataJson = JSON.parse(requester.geminiBuffer);\n                if (!dataJson.candidates) return;\n                \n                if (dataJson.candidates[0]?.finishReason) {\n                    requester.markDone();\n                }\n                // Function call handling\n                if (dataJson.candidates[0]?.content?.parts[0]?.functionCall) {\n                    const functionCall = dataJson.candidates[0]?.content?.parts[0]?.functionCall;\n                    requester.message.functionName = functionCall.name;\n                    requester.message.functionCall = functionCall.name;\n                    requester.message.content += `\\n\\n[[ Function: ${functionCall.name}(${JSON.stringify(functionCall.args, null, 2)}) ]]\\n`;\n                    root.handleGeminiFunctionCall(functionCall.name, functionCall.args);\n                    return\n                }\n                // Normal text response\n                const responseContent = dataJson.candidates[0]?.content?.parts[0]?.text\n                requester.message.content += responseContent;\n                const annotationSources = dataJson.candidates[0]?.groundingMetadata?.groundingChunks?.map(chunk => {\n                    return {\n                        \"type\": \"url_citation\",\n                        \"text\": chunk?.web?.title,\n                        \"url\": chunk?.web?.uri,\n                    }\n                });\n                const annotations = dataJson.candidates[0]?.groundingMetadata?.groundingSupports?.map(citation => {\n                    return {\n                        \"type\": \"url_citation\",\n                        \"start_index\": citation.segment?.startIndex,\n                        \"end_index\": citation.segment?.endIndex,\n                        \"text\": citation?.segment.text,\n                        \"url\": annotationSources[citation.groundingChunkIndices[0]]?.url,\n                        \"sources\": citation.groundingChunkIndices\n                    }\n                });\n                requester.message.annotationSources = annotationSources;\n                requester.message.annotations = annotations;\n                // console.log(JSON.stringify(requester.message, null, 2));\n            } catch (e) {\n                console.log(\"[AI] Could not parse response from stream: \", e);\n                requester.message.content += requester.geminiBuffer\n            } finally {\n                requester.geminiBuffer = \"\";\n            }\n        }\n\n        function handleGeminiResponseLine(line) {\n            if (line.startsWith(\"[\")) {\n                requester.geminiBuffer += line.slice(1).trim();\n            } else if (line == \"]\") {\n                requester.geminiBuffer += line.slice(0, -1).trim();\n                parseGeminiBuffer();\n            } else if (line.startsWith(\",\")) { // end of one entry \n                parseGeminiBuffer();\n            } else {\n                requester.geminiBuffer += line.trim();\n            }\n        }\n\n        function handleOpenAIResponseLine(line) {\n            // Remove 'data: ' prefix if present and trim whitespace\n            let cleanData = line.trim();\n            if (cleanData.startsWith(\"data:\")) {\n                cleanData = cleanData.slice(5).trim();\n            }\n            // console.log(\"Clean data: \", cleanData);\n            if (!cleanData || cleanData.startsWith(\":\")) return;\n\n            if (cleanData === \"[DONE]\") {\n                requester.markDone();\n                return;\n            }\n            const dataJson = JSON.parse(cleanData);\n\n            let newContent = \"\";\n            const responseContent = dataJson.choices[0]?.delta?.content || dataJson.message?.content;\n            const responseReasoning = dataJson.choices[0]?.delta?.reasoning || dataJson.choices[0]?.delta?.reasoning_content;\n\n            if (responseContent && responseContent.length > 0) {\n                if (requester.isReasoning) {\n                    requester.isReasoning = false;\n                    requester.message.content += \"\\n\\n</think>\\n\\n\";\n                }\n                newContent = dataJson.choices[0]?.delta?.content || dataJson.message.content;\n            } else if (responseReasoning && responseReasoning.length > 0) {\n                // console.log(\"Reasoning content: \", dataJson.choices[0].delta.reasoning);\n                if (!requester.isReasoning) {\n                    requester.isReasoning = true;\n                    requester.message.content += \"\\n\\n<think>\\n\\n\";\n                } \n                newContent = dataJson.choices[0].delta.reasoning || dataJson.choices[0].delta.reasoning_content;\n            }\n\n            requester.message.content += newContent;\n\n            if (dataJson.done) {\n                requester.markDone();\n            }\n        }\n\n        stdout: SplitParser {\n            onRead: data => {\n                // console.log(\"RAW DATA: \", data);\n                if (data.length === 0) return;\n\n                // Handle response line\n                if (requester.message.thinking) requester.message.thinking = false;\n                try {\n                    if (requester.apiFormat === \"gemini\") {\n                        requester.handleGeminiResponseLine(data);\n                    }\n                    else if (requester.apiFormat === \"openai\") {\n                        requester.handleOpenAIResponseLine(data);\n                    }\n                    else {\n                        console.log(\"Unknown API format: \", requester.apiFormat);\n                        requester.message.content += data;\n                    }\n                } catch (e) {\n                    console.log(\"[AI] Could not parse response from stream: \", e);\n                    requester.message.content += data;\n                }\n            }\n        }\n\n        onExited: (exitCode, exitStatus) => {\n            if (requester.apiFormat == \"gemini\") requester.parseGeminiBuffer();\n            else requester.markDone();\n\n            try { // to parse full response into json for error handling\n                // console.log(\"Full response: \", requester.message.content + \"]\"); \n                const parsedResponse = JSON.parse(requester.message.content + \"]\");\n                requester.message.content = `\\`\\`\\`json\\n${JSON.stringify(parsedResponse, null, 2)}\\n\\`\\`\\``;\n            } catch (e) { \n                // console.log(\"[AI] Could not parse response on exit: \", e);\n            }\n\n            if (requester.message.content.includes(\"API key not valid\")) {\n                root.addApiKeyAdvice(models[requester.message.model]);\n            }\n        }\n    }\n\n    function sendUserMessage(message) {\n        if (message.length === 0) return;\n        root.addMessage(message, \"user\");\n        requester.makeRequest();\n    }\n\n    function addFunctionOutputMessage(name, output) {\n        const aiMessage = aiMessageComponent.createObject(root, {\n            \"role\": \"user\",\n            \"content\": `[[ Output of ${name} ]]`,\n            \"functionName\": name,\n            \"functionResponse\": output,\n            \"thinking\": false,\n            \"done\": true,\n            \"visibleToUser\": false,\n        });\n        // console.log(\"Adding function output message: \", JSON.stringify(aiMessage));\n        const id = idForMessage(aiMessage);\n        root.messageIDs = [...root.messageIDs, id];\n        root.messageByID[id] = aiMessage;\n    }\n\n    function buildGeminiFunctionOutput(name, output) {\n        const functionResponsePart = {\n            \"name\": name,\n            \"response\": { \"content\": output }\n        }\n        return {\n            \"role\": \"user\",\n            \"parts\": [{ \n                functionResponse: functionResponsePart,\n            }]\n        }\n    }\n\n    function handleGeminiFunctionCall(name, args) {\n        if (name === \"switch_to_search_mode\") {\n            if (root.currentModelId === \"gemini-2.5-flash-tools\") {\n                root.setModel(\"gemini-2.5-flash-search\", false);\n                root.postResponseHook = () => root.setModel(\"gemini-2.5-flash-tools\", false);\n            } else if (root.currentModelId === \"gemini-2.0-flash-tools\") {\n                root.setModel(\"gemini-2.0-flash-search\", false);\n                root.postResponseHook = () => root.setModel(\"gemini-2.0-flash-tools\", false);\n            }\n            addFunctionOutputMessage(name, qsTr(\"Switched to search mode. Continue with the user's request.\"))\n            requester.makeRequest();\n        } else if (name === \"get_shell_config\") {\n            const configJson = ObjectUtils.toPlainObject(ConfigOptions)\n            addFunctionOutputMessage(name, JSON.stringify(configJson));\n            requester.makeRequest();\n        } else if (name === \"set_shell_config\") {\n            if (!args.key || !args.value) {\n                addFunctionOutputMessage(name, qsTr(\"Invalid arguments. Must provide `key` and `value`.\"));\n                return;\n            }\n            const key = args.key;\n            const value = args.value;\n            ConfigLoader.setLiveConfigValue(key, value);\n            ConfigLoader.saveConfig();\n        }\n        else root.addMessage(qsTr(\"Unknown function call: {0}\"), \"assistant\");\n    }\n\n}\n", "modifiedCode": "pragma Singleton\npragma ComponentBehavior: Bound\n\nimport \"root:/modules/common/functions/string_utils.js\" as StringUtils\nimport \"root:/modules/common/functions/object_utils.js\" as ObjectUtils\nimport \"root:/modules/common\"\nimport Quickshell;\nimport Quickshell.Io;\nimport Qt.labs.platform\nimport QtQuick;\n\n/**\n * Basic service to handle LLM chats. Supports Google's and OpenAI's API formats.\n */\nSingleton {\n    id: root\n\n    readonly property string interfaceRole: \"interface\"\n    readonly property string apiKeyEnvVarName: \"API_KEY\"\n    property Component aiMessageComponent: AiMessageData {}\n    property string systemPrompt: ConfigOptions?.ai?.systemPrompt ?? \"\"\n    property var messages: []\n    property var messageIDs: []\n    property var messageByID: ({})\n    readonly property var apiKeys: KeyringStorage.keyringData?.apiKeys ?? {}\n    readonly property var apiKeysLoaded: KeyringStorage.loaded\n    property var postResponseHook\n    property real temperature: PersistentStates?.ai?.temperature ?? 0.5\n\n    function idForMessage(message) {\n        // Generate a unique ID using timestamp and random value\n        return Date.now().toString(36) + Math.random().toString(36).substr(2, 8);\n    }\n\n    function safeModelName(modelName) {\n        return modelName.replace(/:/g, \"_\").replace(/\\./g, \"_\")\n    }\n\n    // Model properties:\n    // - name: Name of the model\n    // - icon: Icon name of the model\n    // - description: Description of the model\n    // - endpoint: Endpoint of the model\n    // - model: Model name of the model\n    // - requires_key: Whether the model requires an API key\n    // - key_id: The identifier of the API key. Use the same identifier for models that can be accessed with the same key.\n    // - key_get_link: Link to get an API key\n    // - key_get_description: Description of pricing and how to get an API key\n    // - api_format: The API format of the model. Can be \"openai\" or \"gemini\". Default is \"openai\".\n    // - tools: List of tools that the model can use. Each tool is an object with the tool name as the key and an empty object as the value.\n    // - extraParams: Extra parameters to be passed to the model. This is a JSON object.\n    property var models: {\n        \"gemini-2.0-flash-search\": {\n            \"name\": \"Gemini 2.0 Flash (Search)\",\n            \"icon\": \"google-gemini-symbolic\",\n            \"description\": qsTr(\"Online | Google's model\\nGives up-to-date information with search.\"),\n            \"homepage\": \"https://aistudio.google.com\",\n            \"endpoint\": \"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:streamGenerateContent\",\n            \"model\": \"gemini-2.0-flash\",\n            \"requires_key\": true,\n            \"key_id\": \"gemini\",\n            \"key_get_link\": \"https://aistudio.google.com/app/apikey\",\n            \"key_get_description\": qsTr(\"**Pricing**: free. Data used for training.\\n\\n**Instructions**: Log into Google account, allow AI Studio to create Google Cloud project or whatever it asks, go back and click Get API key\"),\n            \"api_format\": \"gemini\",\n            \"tools\": [\n                {\n                    \"google_search\": {}\n                },\n            ]\n        },\n        \"gemini-2.0-flash-tools\": {\n            \"name\": \"Gemini 2.0 Flash (Tools)\",\n            \"icon\": \"google-gemini-symbolic\",\n            \"description\": qsTr(\"Experimental | Online | Google's model\\nCan do a little more but doesn't search quickly\"),\n            \"homepage\": \"https://aistudio.google.com\",\n            \"endpoint\": \"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:streamGenerateContent\",\n            \"model\": \"gemini-2.0-flash\",\n            \"requires_key\": true,\n            \"key_id\": \"gemini\",\n            \"key_get_link\": \"https://aistudio.google.com/app/apikey\",\n            \"key_get_description\": qsTr(\"**Pricing**: free. Data used for training.\\n\\n**Instructions**: Log into Google account, allow AI Studio to create Google Cloud project or whatever it asks, go back and click Get API key\"),\n            \"api_format\": \"gemini\",\n            \"tools\": [\n                {\n                    \"functionDeclarations\": [\n                        {\n                            \"name\": \"switch_to_search_mode\",\n                            \"description\": \"Search the web\",\n                        },\n                        {\n                            \"name\": \"get_shell_config\",\n                            \"description\": \"Get the desktop shell config file contents\",\n                        },\n                        {\n                            \"name\": \"set_shell_config\",\n                            \"description\": \"Set a field in the desktop graphical shell config file. Must only be used after `get_shell_config`.\",\n                            \"parameters\": {\n                                \"type\": \"object\",\n                                \"properties\": {\n                                    \"key\": {\n                                        \"type\": \"string\",\n                                        \"description\": \"The key to set, e.g. `bar.borderless`. MUST NOT BE GUESSED, use `get_shell_config` to see what keys are available before setting.\",\n                                    },\n                                    \"value\": {\n                                        \"type\": \"string\",\n                                        \"description\": \"The value to set, e.g. `true`\"\n                                    }\n                                },\n                                \"required\": [\"key\", \"value\"]\n                            }\n                        },\n                    ]\n                }\n            ]\n        },\n        \"gemini-2.5-flash-search\": {\n            \"name\": \"Gemini 2.5 Flash (Search)\",\n            \"icon\": \"google-gemini-symbolic\",\n            \"description\": qsTr(\"Online | Google's model\\nGives up-to-date information with search.\"),\n            \"homepage\": \"https://aistudio.google.com\",\n            \"endpoint\": \"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:streamGenerateContent\",\n            \"model\": \"gemini-2.5-flash-preview-05-20\",\n            \"requires_key\": true,\n            \"key_id\": \"gemini\",\n            \"key_get_link\": \"https://aistudio.google.com/app/apikey\",\n            \"key_get_description\": qsTr(\"**Pricing**: free. Data used for training.\\n\\n**Instructions**: Log into Google account, allow AI Studio to create Google Cloud project or whatever it asks, go back and click Get API key\"),\n            \"api_format\": \"gemini\",\n            \"tools\": [\n                {\n                    \"google_search\": ({})\n                },\n            ]\n        },\n        \"gemini-2.5-flash-tools\": {\n            \"name\": \"Gemini 2.5 Flash (Tools)\",\n            \"icon\": \"google-gemini-symbolic\",\n            \"description\": qsTr(\"Experimental | Online | Google's model\\nCan do a little more but doesn't search quickly\"),\n            \"homepage\": \"https://aistudio.google.com\",\n            \"endpoint\": \"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:streamGenerateContent\",\n            \"model\": \"gemini-2.5-flash-preview-05-20\",\n            \"requires_key\": true,\n            \"key_id\": \"gemini\",\n            \"key_get_link\": \"https://aistudio.google.com/app/apikey\",\n            \"key_get_description\": qsTr(\"**Pricing**: free. Data used for training.\\n\\n**Instructions**: Log into Google account, allow AI Studio to create Google Cloud project or whatever it asks, go back and click Get API key\"),\n            \"api_format\": \"gemini\",\n            \"tools\": [\n                {\n                    \"functionDeclarations\": [\n                        {\n                            \"name\": \"switch_to_search_mode\",\n                            \"description\": \"Search the web\",\n                        },\n                        {\n                            \"name\": \"get_shell_config\",\n                            \"description\": \"Get the desktop shell config file contents\",\n                        },\n                        {\n                            \"name\": \"set_shell_config\",\n                            \"description\": \"Set a field in the desktop graphical shell config file. Must only be used after `get_shell_config`.\",\n                            \"parameters\": {\n                                \"type\": \"object\",\n                                \"properties\": {\n                                    \"key\": {\n                                        \"type\": \"string\",\n                                        \"description\": \"The key to set, e.g. `bar.borderless`. MUST NOT BE GUESSED, use `get_shell_config` to see what keys are available before setting.\",\n                                    },\n                                    \"value\": {\n                                        \"type\": \"string\",\n                                        \"description\": \"The value to set, e.g. `true`\"\n                                    }\n                                },\n                                \"required\": [\"key\", \"value\"]\n                            }\n                        },\n                    ]\n                }\n            ]\n        },\n        \"openrouter-llama4-maverick\": {\n            \"name\": \"Llama 4 Maverick\",\n            \"icon\": \"ollama-symbolic\",\n            \"description\": StringUtils.format(qsTr(\"Online via {0} | {1}'s model\"), \"OpenRouter\", \"Meta\"),\n            \"homepage\": \"https://openrouter.ai/meta-llama/llama-4-maverick:free\",\n            \"endpoint\": \"https://openrouter.ai/api/v1/chat/completions\",\n            \"model\": \"meta-llama/llama-4-maverick:free\",\n            \"requires_key\": true,\n            \"key_id\": \"openrouter\",\n            \"key_get_link\": \"https://openrouter.ai/settings/keys\",\n            \"key_get_description\": qsTr(\"**Pricing**: free. Data use policy varies depending on your OpenRouter account settings.\\n\\n**Instructions**: Log into OpenRouter account, go to Keys on the topright menu, click Create API Key\"),\n        },\n        \"openrouter-deepseek-r1\": {\n            \"name\": \"DeepSeek R1\",\n            \"icon\": \"deepseek-symbolic\",\n            \"description\": StringUtils.format(qsTr(\"Online via {0} | {1}'s model\"), \"OpenRouter\", \"DeepSeek\"),\n            \"homepage\": \"https://openrouter.ai/deepseek/deepseek-r1:free\",\n            \"endpoint\": \"https://openrouter.ai/api/v1/chat/completions\",\n            \"model\": \"deepseek/deepseek-r1:free\",\n            \"requires_key\": true,\n            \"key_id\": \"openrouter\",\n            \"key_get_link\": \"https://openrouter.ai/settings/keys\",\n            \"key_get_description\": qsTr(\"**Pricing**: free. Data use policy varies depending on your OpenRouter account settings.\\n\\n**Instructions**: Log into OpenRouter account, go to Keys on the topright menu, click Create API Key\"),\n        },\n    }\n    property var modelList: Object.keys(root.models)\n    property var currentModelId: PersistentStates?.ai?.model || modelList[0]\n\n\n\n    function guessModelLogo(model) {\n        if (model.includes(\"llama\")) return \"ollama-symbolic\";\n        if (model.includes(\"gemma\")) return \"google-gemini-symbolic\";\n        if (model.includes(\"deepseek\")) return \"deepseek-symbolic\";\n        if (/^phi\\d*:/i.test(model)) return \"microsoft-symbolic\";\n        return \"ollama-symbolic\";\n    }\n\n    function guessModelName(model) {\n        const replaced = model.replace(/-/g, ' ').replace(/:/g, ' ');\n        let words = replaced.split(' ');\n        words[words.length - 1] = words[words.length - 1].replace(/(\\d+)b$/, (_, num) => `${num}B`)\n        words = words.map((word) => {\n            return (word.charAt(0).toUpperCase() + word.slice(1))\n        });\n        if (words[words.length - 1] === \"Latest\") words.pop();\n        else words[words.length - 1] = `(${words[words.length - 1]})`; // Surround the last word with square brackets\n        const result = words.join(' ');\n        return result;\n    }\n\n    Process {\n        id: getOllamaModels\n        command: [\"bash\", \"-c\", `${Directories.config}/quickshell/scripts/ai/show-installed-ollama-models.sh`.replace(/file:\\/\\//, \"\")]\n        stdout: SplitParser {\n            onRead: data => {\n                try {\n                    if (data.length === 0) return;\n                    const dataJson = JSON.parse(data);\n                    root.modelList = [...root.modelList, ...dataJson];\n                    dataJson.forEach(model => {\n                        const safeModelName = root.safeModelName(model);\n                        root.models[safeModelName] = {\n                            \"name\": guessModelName(model),\n                            \"icon\": guessModelLogo(model),\n                            \"description\": StringUtils.format(qsTr(\"Local Ollama model | {0}\"), model),\n                            \"homepage\": `https://ollama.com/library/${model}`,\n                            \"endpoint\": \"http://localhost:11434/v1/chat/completions\",\n                            \"model\": model,\n                        }\n                    });\n\n                    root.modelList = Object.keys(root.models);\n\n                } catch (e) {\n                    console.log(\"Could not fetch Ollama models:\", e);\n                }\n            }\n        }\n    }\n\n    function addMessage(message, role) {\n        if (message.length === 0) return;\n        const aiMessage = aiMessageComponent.createObject(root, {\n            \"role\": role,\n            \"content\": message,\n            \"thinking\": false,\n            \"done\": true,\n        });\n        const id = idForMessage(aiMessage);\n        root.messageIDs = [...root.messageIDs, id];\n        root.messageByID[id] = aiMessage;\n    }\n\n    function removeMessage(index) {\n        if (index < 0 || index >= messageIDs.length) return;\n        const id = root.messageIDs[index];\n        root.messageIDs.splice(index, 1);\n        root.messageIDs = [...root.messageIDs];\n        delete root.messageByID[id];\n    }\n\n    function addApiKeyAdvice(model) {\n        root.addMessage(\n            StringUtils.format(qsTr('To set an API key, pass it with the command\\n\\nTo view the key, pass \"get\" with the command<br/>\\n\\n### For {0}:\\n\\n**Link**: {1}\\n\\n{2}'), \n                model.name, model.key_get_link, model.key_get_description ?? qsTr(\"<i>No further instruction provided</i>\")), \n            Ai.interfaceRole\n        );\n    }\n\n    function getModel() {\n        return models[currentModelId];\n    }\n\n    function setModel(modelId, feedback = true) {\n        if (!modelId) modelId = \"\"\n        modelId = modelId.toLowerCase()\n        if (modelList.indexOf(modelId) !== -1) {\n            const model = models[modelId]\n            // Fetch API keys if needed\n            if (model?.requires_key) KeyringStorage.fetchKeyringData();\n            // See if policy prevents online models\n            if (ConfigOptions.policies.ai === 2 && !model.endpoint.includes(\"localhost\")) {\n                root.addMessage(StringUtils.format(StringUtils.format(\"Online models disallowed\\n\\nControlled by `policies.ai` config option\"), model.name), root.interfaceRole);\n                return;\n            }\n            PersistentStateManager.setState(\"ai.model\", modelId);\n            if (feedback) root.addMessage(StringUtils.format(StringUtils.format(\"Model set to {0}\"), model.name), root.interfaceRole);\n            if (model.requires_key) {\n                // If key not there show advice\n                if (root.apiKeysLoaded && (!root.apiKeys[model.key_id] || root.apiKeys[model.key_id].length === 0)) {\n                    root.addApiKeyAdvice(model)\n                }\n            }\n        } else {\n            if (feedback) root.addMessage(qsTr(\"Invalid model. Supported: \\n```\\n\") + modelList.join(\"\\n```\\n```\\n\"), Ai.interfaceRole) + \"\\n```\"\n        }\n    }\n    \n    function getTemperature() {\n        return root.temperature;\n    }\n\n    function setTemperature(value) {\n        if (value == NaN || value < 0 || value > 2) {\n            root.addMessage(qsTr(\"Temperature must be between 0 and 2\"), Ai.interfaceRole);\n            return;\n        }\n        PersistentStateManager.setState(\"ai.temperature\", value);\n        root.temperature = value;\n        root.addMessage(StringUtils.format(qsTr(\"Temperature set to {0}\"), value), Ai.interfaceRole);\n    }\n\n    function setApiKey(key) {\n        const model = models[currentModelId];\n        if (!model.requires_key) {\n            root.addMessage(StringUtils.format(qsTr(\"{0} does not require an API key\"), model.name), Ai.interfaceRole);\n            return;\n        }\n        if (!key || key.length === 0) {\n            const model = models[currentModelId];\n            root.addApiKeyAdvice(model)\n            return;\n        }\n        KeyringStorage.setNestedField([\"apiKeys\", model.key_id], key.trim());\n        root.addMessage(StringUtils.format(qsTr(\"API key set for {0}\"), model.name, Ai.interfaceRole));\n    }\n\n    function printApiKey() {\n        const model = models[currentModelId];\n        if (model.requires_key) {\n            const key = root.apiKeys[model.key_id];\n            if (key) {\n                root.addMessage(StringUtils.format(qsTr(\"API key:\\n\\n```txt\\n{0}\\n```\"), key), Ai.interfaceRole);\n            } else {\n                root.addMessage(StringUtils.format(qsTr(\"No API key set for {0}\"), model.name), Ai.interfaceRole);\n            }\n        } else {\n            root.addMessage(StringUtils.format(qsTr(\"{0} does not require an API key\"), model.name), Ai.interfaceRole);\n        }\n    }\n\n    function printTemperature() {\n        root.addMessage(StringUtils.format(qsTr(\"Temperature: {0}\"), root.temperature), Ai.interfaceRole);\n    }\n\n    function clearMessages() {\n        root.messageIDs = [];\n        root.messageByID = ({});\n    }\n\n    function addCustomModel(modelConfig) {\n        const modelId = modelConfig.name.toLowerCase().replace(/[^a-z0-9]/g, '-');\n        root.models[modelId] = {\n            \"name\": modelConfig.displayName || modelConfig.name,\n            \"icon\": modelConfig.icon || \"smart_toy\",\n            \"description\": modelConfig.description || qsTr(\"自定义模型\"),\n            \"endpoint\": modelConfig.endpoint,\n            \"model\": modelConfig.modelId || modelConfig.name,\n            \"requires_key\": modelConfig.requiresKey !== false,\n            \"key_id\": modelConfig.keyId || \"custom\",\n            \"api_format\": modelConfig.apiFormat || \"openai\",\n            \"custom\": true\n        };\n        root.modelList = Object.keys(root.models);\n\n        // 保存自定义模型到配置\n        const customModels = ConfigOptions.ai?.customModels || {};\n        customModels[modelId] = root.models[modelId];\n        ConfigLoader.setConfigValueAndSave(\"ai.customModels\", customModels);\n\n        root.addMessage(StringUtils.format(qsTr(\"已添加自定义模型: {0}\"), modelConfig.displayName || modelConfig.name), root.interfaceRole);\n        return modelId;\n    }\n\n    function removeCustomModel(modelId) {\n        if (!root.models[modelId]?.custom) {\n            root.addMessage(qsTr(\"只能删除自定义模型\"), root.interfaceRole);\n            return false;\n        }\n\n        delete root.models[modelId];\n        root.modelList = Object.keys(root.models);\n\n        // 从配置中移除\n        const customModels = ConfigOptions.ai?.customModels || {};\n        delete customModels[modelId];\n        ConfigLoader.setConfigValueAndSave(\"ai.customModels\", customModels);\n\n        // 如果当前模型被删除，切换到默认模型\n        if (root.currentModelId === modelId) {\n            root.setModel(root.modelList[0], true);\n        }\n\n        root.addMessage(StringUtils.format(qsTr(\"已删除自定义模型: {0}\"), modelId), root.interfaceRole);\n        return true;\n    }\n\n    Component.onCompleted: {\n        // 加载自定义模型\n        const customModels = ConfigOptions.ai?.customModels || {};\n        Object.keys(customModels).forEach(modelId => {\n            root.models[modelId] = customModels[modelId];\n        });\n\n        setModel(currentModelId, false); // Do necessary setup for model\n        getOllamaModels.running = true\n    }\n\n    Process {\n        id: requester\n        property var baseCommand: [\"bash\", \"-c\"]\n        property var message\n        property bool isReasoning\n        property string apiFormat: \"openai\"\n        property string geminiBuffer: \"\"\n\n        function buildGeminiEndpoint(model) {\n            // console.log(\"ENDPOINT: \" + model.endpoint + `?key=\\$\\{${root.apiKeyEnvVarName}\\}`)\n            return model.endpoint + `?key=\\$\\{${root.apiKeyEnvVarName}\\}`;\n        }\n\n        function buildOpenAIEndpoint(model) {\n            return model.endpoint;\n        }\n\n        function markDone() {\n            requester.message.done = true;\n            if (root.postResponseHook) {\n                root.postResponseHook();\n                root.postResponseHook = null; // Reset hook after use\n            }\n        }\n\n        function buildGeminiRequestData(model, messages) {\n            let baseData = {\n                \"contents\": messages.filter(message => (message.role != Ai.interfaceRole)).map(message => {\n                    const geminiApiRoleName = (message.role === \"assistant\") ? \"model\" : message.role;\n                    const usingSearch = model.tools[0].google_search != undefined                \n                    if (!usingSearch && message.functionCall != undefined && message.functionCall.length > 0) {\n                        return {\n                            \"role\": geminiApiRoleName,\n                            \"parts\": [{ \n                                functionCall: {\n                                    \"name\": message.functionName,\n                                }\n                            }]\n                        }\n                    }\n                    if (!usingSearch && message.functionResponse != undefined && message.functionResponse.length > 0) {\n                        return {\n                            \"role\": geminiApiRoleName,\n                            \"parts\": [{ \n                                functionResponse: {\n                                    \"name\": message.functionName,\n                                    \"response\": { \"content\": message.functionResponse }\n                                }\n                            }]\n                        }\n                    }\n                    return {\n                        \"role\": geminiApiRoleName,\n                        \"parts\": [{ \n                            text: message.content,\n                        }]\n                    }\n                }),\n                \"tools\": [\n                    ...model.tools,\n                ],\n                \"system_instruction\": {\n                    \"parts\": [{ text: root.systemPrompt }]\n                },\n                \"generationConfig\": {\n                    // \"temperature\": root.temperature,\n                },\n            };\n            return model.extraParams ? Object.assign({}, baseData, model.extraParams) : baseData;\n        }\n\n        function buildOpenAIRequestData(model, messages) {\n            let baseData = {\n                \"model\": model.model,\n                \"messages\": [\n                    {role: \"system\", content: root.systemPrompt},\n                    ...messages.filter(message => (message.role != Ai.interfaceRole)).map(message => {\n                        return {\n                            \"role\": message.role,\n                            \"content\": message.content,\n                        }\n                    }),\n                ],\n                \"stream\": true,\n                // \"temperature\": root.temperature,\n            };\n            return model.extraParams ? Object.assign({}, baseData, model.extraParams) : baseData;\n        }\n\n        function makeRequest() {\n            const model = models[currentModelId];\n            requester.apiFormat = model.api_format ?? \"openai\";\n\n            /* Put API key in environment variable */\n            if (model.requires_key) requester.environment[`${root.apiKeyEnvVarName}`] = root.apiKeys ? (root.apiKeys[model.key_id] ?? \"\") : \"\"\n\n            /* Build endpoint, request data */\n            const endpoint = (apiFormat === \"gemini\") ? buildGeminiEndpoint(model) : buildOpenAIEndpoint(model);\n            const messageArray = root.messageIDs.map(id => root.messageByID[id]);\n            const data = (apiFormat === \"gemini\") ? buildGeminiRequestData(model, messageArray) : buildOpenAIRequestData(model, messageArray);\n            // console.log(\"REQUEST DATA: \", JSON.stringify(data, null, 2));\n\n            let requestHeaders = {\n                \"Content-Type\": \"application/json\",\n            }\n            \n            /* Create local message object */\n            requester.message = root.aiMessageComponent.createObject(root, {\n                \"role\": \"assistant\",\n                \"model\": currentModelId,\n                \"content\": \"\",\n                \"thinking\": true,\n                \"done\": false,\n            });\n            const id = idForMessage(requester.message);\n            root.messageIDs = [...root.messageIDs, id];\n            root.messageByID[id] = requester.message;\n\n            /* Build header string for curl */ \n            let headerString = Object.entries(requestHeaders)\n                .filter(([k, v]) => v && v.length > 0)\n                .map(([k, v]) => `-H '${k}: ${v}'`)\n                .join(' ');\n\n            // console.log(\"Request headers: \", JSON.stringify(requestHeaders));\n            // console.log(\"Header string: \", headerString);\n\n            /* Create command string */\n            const requestCommandString = `curl --no-buffer \"${endpoint}\"`\n                + ` ${headerString}`\n                + ((apiFormat == \"gemini\") ? \"\" : ` -H \"Authorization: Bearer \\$\\{${root.apiKeyEnvVarName}\\}\"`)\n                + ` -d '${StringUtils.shellSingleQuoteEscape(JSON.stringify(data))}'`\n            // console.log(\"Request command: \", requestCommandString);\n            requester.command = baseCommand.concat([requestCommandString]);\n\n            /* Reset vars and make the request */\n            requester.isReasoning = false\n            requester.running = true\n        }\n\n        function parseGeminiBuffer() {\n            // console.log(\"BUFFER DATA: \", requester.geminiBuffer);\n            try {\n                if (requester.geminiBuffer.length === 0) return;\n                const dataJson = JSON.parse(requester.geminiBuffer);\n                if (!dataJson.candidates) return;\n                \n                if (dataJson.candidates[0]?.finishReason) {\n                    requester.markDone();\n                }\n                // Function call handling\n                if (dataJson.candidates[0]?.content?.parts[0]?.functionCall) {\n                    const functionCall = dataJson.candidates[0]?.content?.parts[0]?.functionCall;\n                    requester.message.functionName = functionCall.name;\n                    requester.message.functionCall = functionCall.name;\n                    requester.message.content += `\\n\\n[[ Function: ${functionCall.name}(${JSON.stringify(functionCall.args, null, 2)}) ]]\\n`;\n                    root.handleGeminiFunctionCall(functionCall.name, functionCall.args);\n                    return\n                }\n                // Normal text response\n                const responseContent = dataJson.candidates[0]?.content?.parts[0]?.text\n                requester.message.content += responseContent;\n                const annotationSources = dataJson.candidates[0]?.groundingMetadata?.groundingChunks?.map(chunk => {\n                    return {\n                        \"type\": \"url_citation\",\n                        \"text\": chunk?.web?.title,\n                        \"url\": chunk?.web?.uri,\n                    }\n                });\n                const annotations = dataJson.candidates[0]?.groundingMetadata?.groundingSupports?.map(citation => {\n                    return {\n                        \"type\": \"url_citation\",\n                        \"start_index\": citation.segment?.startIndex,\n                        \"end_index\": citation.segment?.endIndex,\n                        \"text\": citation?.segment.text,\n                        \"url\": annotationSources[citation.groundingChunkIndices[0]]?.url,\n                        \"sources\": citation.groundingChunkIndices\n                    }\n                });\n                requester.message.annotationSources = annotationSources;\n                requester.message.annotations = annotations;\n                // console.log(JSON.stringify(requester.message, null, 2));\n            } catch (e) {\n                console.log(\"[AI] Could not parse response from stream: \", e);\n                requester.message.content += requester.geminiBuffer\n            } finally {\n                requester.geminiBuffer = \"\";\n            }\n        }\n\n        function handleGeminiResponseLine(line) {\n            if (line.startsWith(\"[\")) {\n                requester.geminiBuffer += line.slice(1).trim();\n            } else if (line == \"]\") {\n                requester.geminiBuffer += line.slice(0, -1).trim();\n                parseGeminiBuffer();\n            } else if (line.startsWith(\",\")) { // end of one entry \n                parseGeminiBuffer();\n            } else {\n                requester.geminiBuffer += line.trim();\n            }\n        }\n\n        function handleOpenAIResponseLine(line) {\n            // Remove 'data: ' prefix if present and trim whitespace\n            let cleanData = line.trim();\n            if (cleanData.startsWith(\"data:\")) {\n                cleanData = cleanData.slice(5).trim();\n            }\n            // console.log(\"Clean data: \", cleanData);\n            if (!cleanData || cleanData.startsWith(\":\")) return;\n\n            if (cleanData === \"[DONE]\") {\n                requester.markDone();\n                return;\n            }\n            const dataJson = JSON.parse(cleanData);\n\n            let newContent = \"\";\n            const responseContent = dataJson.choices[0]?.delta?.content || dataJson.message?.content;\n            const responseReasoning = dataJson.choices[0]?.delta?.reasoning || dataJson.choices[0]?.delta?.reasoning_content;\n\n            if (responseContent && responseContent.length > 0) {\n                if (requester.isReasoning) {\n                    requester.isReasoning = false;\n                    requester.message.content += \"\\n\\n</think>\\n\\n\";\n                }\n                newContent = dataJson.choices[0]?.delta?.content || dataJson.message.content;\n            } else if (responseReasoning && responseReasoning.length > 0) {\n                // console.log(\"Reasoning content: \", dataJson.choices[0].delta.reasoning);\n                if (!requester.isReasoning) {\n                    requester.isReasoning = true;\n                    requester.message.content += \"\\n\\n<think>\\n\\n\";\n                } \n                newContent = dataJson.choices[0].delta.reasoning || dataJson.choices[0].delta.reasoning_content;\n            }\n\n            requester.message.content += newContent;\n\n            if (dataJson.done) {\n                requester.markDone();\n            }\n        }\n\n        stdout: SplitParser {\n            onRead: data => {\n                // console.log(\"RAW DATA: \", data);\n                if (data.length === 0) return;\n\n                // Handle response line\n                if (requester.message.thinking) requester.message.thinking = false;\n                try {\n                    if (requester.apiFormat === \"gemini\") {\n                        requester.handleGeminiResponseLine(data);\n                    }\n                    else if (requester.apiFormat === \"openai\") {\n                        requester.handleOpenAIResponseLine(data);\n                    }\n                    else {\n                        console.log(\"Unknown API format: \", requester.apiFormat);\n                        requester.message.content += data;\n                    }\n                } catch (e) {\n                    console.log(\"[AI] Could not parse response from stream: \", e);\n                    requester.message.content += data;\n                }\n            }\n        }\n\n        onExited: (exitCode, exitStatus) => {\n            if (requester.apiFormat == \"gemini\") requester.parseGeminiBuffer();\n            else requester.markDone();\n\n            try { // to parse full response into json for error handling\n                // console.log(\"Full response: \", requester.message.content + \"]\"); \n                const parsedResponse = JSON.parse(requester.message.content + \"]\");\n                requester.message.content = `\\`\\`\\`json\\n${JSON.stringify(parsedResponse, null, 2)}\\n\\`\\`\\``;\n            } catch (e) { \n                // console.log(\"[AI] Could not parse response on exit: \", e);\n            }\n\n            if (requester.message.content.includes(\"API key not valid\")) {\n                root.addApiKeyAdvice(models[requester.message.model]);\n            }\n        }\n    }\n\n    function sendUserMessage(message) {\n        if (message.length === 0) return;\n        root.addMessage(message, \"user\");\n        requester.makeRequest();\n    }\n\n    function addFunctionOutputMessage(name, output) {\n        const aiMessage = aiMessageComponent.createObject(root, {\n            \"role\": \"user\",\n            \"content\": `[[ Output of ${name} ]]`,\n            \"functionName\": name,\n            \"functionResponse\": output,\n            \"thinking\": false,\n            \"done\": true,\n            \"visibleToUser\": false,\n        });\n        // console.log(\"Adding function output message: \", JSON.stringify(aiMessage));\n        const id = idForMessage(aiMessage);\n        root.messageIDs = [...root.messageIDs, id];\n        root.messageByID[id] = aiMessage;\n    }\n\n    function buildGeminiFunctionOutput(name, output) {\n        const functionResponsePart = {\n            \"name\": name,\n            \"response\": { \"content\": output }\n        }\n        return {\n            \"role\": \"user\",\n            \"parts\": [{ \n                functionResponse: functionResponsePart,\n            }]\n        }\n    }\n\n    function handleGeminiFunctionCall(name, args) {\n        if (name === \"switch_to_search_mode\") {\n            if (root.currentModelId === \"gemini-2.5-flash-tools\") {\n                root.setModel(\"gemini-2.5-flash-search\", false);\n                root.postResponseHook = () => root.setModel(\"gemini-2.5-flash-tools\", false);\n            } else if (root.currentModelId === \"gemini-2.0-flash-tools\") {\n                root.setModel(\"gemini-2.0-flash-search\", false);\n                root.postResponseHook = () => root.setModel(\"gemini-2.0-flash-tools\", false);\n            }\n            addFunctionOutputMessage(name, qsTr(\"Switched to search mode. Continue with the user's request.\"))\n            requester.makeRequest();\n        } else if (name === \"get_shell_config\") {\n            const configJson = ObjectUtils.toPlainObject(ConfigOptions)\n            addFunctionOutputMessage(name, JSON.stringify(configJson));\n            requester.makeRequest();\n        } else if (name === \"set_shell_config\") {\n            if (!args.key || !args.value) {\n                addFunctionOutputMessage(name, qsTr(\"Invalid arguments. Must provide `key` and `value`.\"));\n                return;\n            }\n            const key = args.key;\n            const value = args.value;\n            ConfigLoader.setLiveConfigValue(key, value);\n            ConfigLoader.saveConfig();\n        }\n        else root.addMessage(qsTr(\"Unknown function call: {0}\"), \"assistant\");\n    }\n\n}\n"}