{"path": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/common/ConfigOptions.qml"}, "originalCode": "pragma Singleton\npragma ComponentBehavior: Bound\nimport QtQuick\nimport Quickshell\n\nSingleton {\n    property QtObject policies: QtObject {\n        property int ai: 1 // 0: No | 1: Yes | 2: Local\n        property int weeb: 1 // 0: No | 1: Open | 2: Closet\n    }\n\n    property QtObject ai: QtObject {\n        property string systemPrompt: qsTr(\"Use casual tone. No user knowledge is to be assumed except basic Linux literacy. Be brief and concise: When explaining concepts, use bullet points (prefer minus sign (-) over asterisk (*)) and highlight keywords in bold to pinpoint the main concepts instead of long paragraphs. You are also encouraged to split your response with h2 headers, each header title beginning with an emoji, like `## 🐧 Linux`. When making changes to the user's config, you must get the config to know what values there are before setting.\")\n    }\n\n    property QtObject appearance: QtObject {\n        property int fakeScreenRounding: 2 // 0: None | 1: Always | 2: When not fullscreen\n        property bool transparency: false\n        property QtObject palette: QtObject {\n            property string type: \"auto\" // Allowed: auto, scheme-content, scheme-expressive, scheme-fidelity, scheme-fruit-salad, scheme-monochrome, scheme-neutral, scheme-rainbow, scheme-tonal-spot\n        }\n    }\n\n    property QtObject audio: QtObject {\n        // Values in %\n        property QtObject protection: QtObject {\n            // Prevent sudden bangs\n            property bool enable: true\n            property real maxAllowedIncrease: 10\n            property real maxAllowed: 90 // Realistically should already provide some protection when it's 99...\n        }\n    }\n\n    property QtObject apps: QtObject {\n        property string bluetooth: \"kcmshell6 kcm_bluetooth\"\n        property string network: \"plasmawindowed org.kde.plasma.networkmanagement\"\n        property string networkEthernet: \"kcmshell6 kcm_networkmanagement\"\n        property string taskManager: \"plasma-systemmonitor --page-name Processes\"\n        property string terminal: \"kitty -1\" // This is only for shell actions\n    }\n\n    property QtObject background: QtObject {\n        property bool fixedClockPosition: false\n        property real clockX: -500\n        property real clockY: -500\n    }\n\n    property QtObject bar: QtObject {\n        property bool bottom: false // Instead of top\n        property bool borderless: false // true for no grouping of items\n        property string topLeftIcon: \"spark\" // Options: distro, spark\n        property bool showBackground: true\n        property bool verbose: true\n        property QtObject resources: QtObject {\n            property bool alwaysShowSwap: true\n            property bool alwaysShowCpu: false\n        }\n        property list<string> screenList: [] // List of names, like \"eDP-1\", find out with 'hyprctl monitors' command\n        property QtObject utilButtons: QtObject {\n            property bool showScreenSnip: true\n            property bool showColorPicker: false\n            property bool showMicToggle: false\n            property bool showKeyboardToggle: true\n            property bool showDarkModeToggle: true\n        }\n        property QtObject tray: QtObject {\n            property bool monochromeIcons: true\n        }\n        property QtObject workspaces: QtObject {\n            property int shown: 10\n            property bool showAppIcons: true\n            property bool alwaysShowNumbers: false\n            property int showNumberDelay: 300 // milliseconds\n        }\n    }\n\n    property QtObject battery: QtObject {\n        property int low: 20\n        property int critical: 5\n        property bool automaticSuspend: true\n        property int suspend: 3\n    }\n\n    property QtObject dock: QtObject {\n        property real height: 60\n        property real hoverRegionHeight: 3\n        property bool pinnedOnStartup: false\n        property bool hoverToReveal: false // When false, only reveals on empty workspace\n        property list<string> pinnedApps: [ // IDs of pinned entries\n            \"org.kde.dolphin\", \"kitty\",]\n    }\n\n    property QtObject language: QtObject {\n        property QtObject translator: QtObject {\n            property string engine: \"auto\" // Run `trans -list-engines` for available engines. auto should use google\n            property string targetLanguage: \"auto\" // Run `trans -list-all` for available languages\n            property string sourceLanguage: \"auto\"\n        }\n    }\n\n    property QtObject networking: QtObject {\n        property string userAgent: \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36\"\n    }\n\n    property QtObject osd: QtObject {\n        property int timeout: 1000\n    }\n\n    property QtObject osk: QtObject {\n        property string layout: \"qwerty_full\"\n        property bool pinnedOnStartup: false\n    }\n\n    property QtObject overview: QtObject {\n        property real scale: 0.18 // Relative to screen size\n        property real rows: 2\n        property real columns: 5\n    }\n\n    property QtObject resources: QtObject {\n        property int updateInterval: 3000\n    }\n\n    property QtObject search: QtObject {\n        property int nonAppResultDelay: 30 // This prevents lagging when typing\n        property string engineBaseUrl: \"https://www.google.com/search?q=\"\n        property list<string> excludedSites: [\"quora.com\"]\n        property bool sloppy: false // Uses levenshtein distance based scoring instead of fuzzy sort. Very weird.\n        property QtObject prefix: QtObject {\n            property string action: \"/\"\n            property string clipboard: \";\"\n            property string emojis: \":\"\n        }\n    }\n\n    property QtObject sidebar: QtObject {\n        property QtObject translator: QtObject {\n            property int delay: 300 // Delay before sending request. Reduces (potential) rate limits and lag.\n        }\n        property QtObject booru: QtObject {\n            property bool allowNsfw: false\n            property string defaultProvider: \"yandere\"\n            property int limit: 20\n            property QtObject zerochan: QtObject {\n                property string username: \"[unset]\"\n            }\n        }\n    }\n\n    property QtObject time: QtObject {\n        // https://doc.qt.io/qt-6/qtime.html#toString\n        property string format: \"hh:mm\"\n        property string dateFormat: \"dddd, dd/MM\"\n    }\n\n    property QtObject windows: QtObject {\n        property bool showTitlebar: true // Client-side decoration for shell apps\n        property bool centerTitle: true\n    }\n\n    property QtObject hacks: QtObject {\n        property int arbitraryRaceConditionDelay: 20 // milliseconds\n    }\n}\n", "modifiedCode": "pragma Singleton\npragma ComponentBehavior: Bound\nimport QtQuick\nimport Quickshell\n\nSingleton {\n    property QtObject policies: QtObject {\n        property int ai: 1 // 0: No | 1: Yes | 2: Local\n        property int weeb: 1 // 0: No | 1: Open | 2: Closet\n    }\n\n    property QtObject ai: QtObject {\n        property string systemPrompt: qsTr(\"Use casual tone. No user knowledge is to be assumed except basic Linux literacy. Be brief and concise: When explaining concepts, use bullet points (prefer minus sign (-) over asterisk (*)) and highlight keywords in bold to pinpoint the main concepts instead of long paragraphs. You are also encouraged to split your response with h2 headers, each header title beginning with an emoji, like `## 🐧 Linux`. When making changes to the user's config, you must get the config to know what values there are before setting.\")\n        property int requestTimeout: 60\n        property int maxRetries: 3\n        property bool streamResponse: true\n        property bool saveHistory: true\n        property bool verifySSL: true\n    }\n\n    property QtObject appearance: QtObject {\n        property int fakeScreenRounding: 2 // 0: None | 1: Always | 2: When not fullscreen\n        property bool transparency: false\n        property QtObject palette: QtObject {\n            property string type: \"auto\" // Allowed: auto, scheme-content, scheme-expressive, scheme-fidelity, scheme-fruit-salad, scheme-monochrome, scheme-neutral, scheme-rainbow, scheme-tonal-spot\n        }\n    }\n\n    property QtObject audio: QtObject {\n        // Values in %\n        property QtObject protection: QtObject {\n            // Prevent sudden bangs\n            property bool enable: true\n            property real maxAllowedIncrease: 10\n            property real maxAllowed: 90 // Realistically should already provide some protection when it's 99...\n        }\n    }\n\n    property QtObject apps: QtObject {\n        property string bluetooth: \"kcmshell6 kcm_bluetooth\"\n        property string network: \"plasmawindowed org.kde.plasma.networkmanagement\"\n        property string networkEthernet: \"kcmshell6 kcm_networkmanagement\"\n        property string taskManager: \"plasma-systemmonitor --page-name Processes\"\n        property string terminal: \"kitty -1\" // This is only for shell actions\n    }\n\n    property QtObject background: QtObject {\n        property bool fixedClockPosition: false\n        property real clockX: -500\n        property real clockY: -500\n    }\n\n    property QtObject bar: QtObject {\n        property bool bottom: false // Instead of top\n        property bool borderless: false // true for no grouping of items\n        property string topLeftIcon: \"spark\" // Options: distro, spark\n        property bool showBackground: true\n        property bool verbose: true\n        property QtObject resources: QtObject {\n            property bool alwaysShowSwap: true\n            property bool alwaysShowCpu: false\n        }\n        property list<string> screenList: [] // List of names, like \"eDP-1\", find out with 'hyprctl monitors' command\n        property QtObject utilButtons: QtObject {\n            property bool showScreenSnip: true\n            property bool showColorPicker: false\n            property bool showMicToggle: false\n            property bool showKeyboardToggle: true\n            property bool showDarkModeToggle: true\n        }\n        property QtObject tray: QtObject {\n            property bool monochromeIcons: true\n        }\n        property QtObject workspaces: QtObject {\n            property int shown: 10\n            property bool showAppIcons: true\n            property bool alwaysShowNumbers: false\n            property int showNumberDelay: 300 // milliseconds\n        }\n    }\n\n    property QtObject battery: QtObject {\n        property int low: 20\n        property int critical: 5\n        property bool automaticSuspend: true\n        property int suspend: 3\n    }\n\n    property QtObject dock: QtObject {\n        property real height: 60\n        property real hoverRegionHeight: 3\n        property bool pinnedOnStartup: false\n        property bool hoverToReveal: false // When false, only reveals on empty workspace\n        property list<string> pinnedApps: [ // IDs of pinned entries\n            \"org.kde.dolphin\", \"kitty\",]\n    }\n\n    property QtObject language: QtObject {\n        property QtObject translator: QtObject {\n            property string engine: \"auto\" // Run `trans -list-engines` for available engines. auto should use google\n            property string targetLanguage: \"auto\" // Run `trans -list-all` for available languages\n            property string sourceLanguage: \"auto\"\n        }\n    }\n\n    property QtObject networking: QtObject {\n        property string userAgent: \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36\"\n    }\n\n    property QtObject osd: QtObject {\n        property int timeout: 1000\n    }\n\n    property QtObject osk: QtObject {\n        property string layout: \"qwerty_full\"\n        property bool pinnedOnStartup: false\n    }\n\n    property QtObject overview: QtObject {\n        property real scale: 0.18 // Relative to screen size\n        property real rows: 2\n        property real columns: 5\n    }\n\n    property QtObject resources: QtObject {\n        property int updateInterval: 3000\n    }\n\n    property QtObject search: QtObject {\n        property int nonAppResultDelay: 30 // This prevents lagging when typing\n        property string engineBaseUrl: \"https://www.google.com/search?q=\"\n        property list<string> excludedSites: [\"quora.com\"]\n        property bool sloppy: false // Uses levenshtein distance based scoring instead of fuzzy sort. Very weird.\n        property QtObject prefix: QtObject {\n            property string action: \"/\"\n            property string clipboard: \";\"\n            property string emojis: \":\"\n        }\n    }\n\n    property QtObject sidebar: QtObject {\n        property QtObject translator: QtObject {\n            property int delay: 300 // Delay before sending request. Reduces (potential) rate limits and lag.\n        }\n        property QtObject booru: QtObject {\n            property bool allowNsfw: false\n            property string defaultProvider: \"yandere\"\n            property int limit: 20\n            property QtObject zerochan: QtObject {\n                property string username: \"[unset]\"\n            }\n        }\n    }\n\n    property QtObject time: QtObject {\n        // https://doc.qt.io/qt-6/qtime.html#toString\n        property string format: \"hh:mm\"\n        property string dateFormat: \"dddd, dd/MM\"\n    }\n\n    property QtObject windows: QtObject {\n        property bool showTitlebar: true // Client-side decoration for shell apps\n        property bool centerTitle: true\n    }\n\n    property QtObject hacks: QtObject {\n        property int arbitraryRaceConditionDelay: 20 // milliseconds\n    }\n}\n"}