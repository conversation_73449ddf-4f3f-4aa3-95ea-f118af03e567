{"path": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/backgroundWidgets/BackgroundWidgets.qml"}, "originalCode": "import \"root:/\"\nimport \"root:/modules/common\"\nimport \"root:/modules/common/widgets\"\nimport \"root:/services\"\nimport \"root:/modules/common/functions/color_utils.js\" as ColorUtils\nimport QtQuick\nimport QtQuick.Controls\nimport QtQuick.Layouts\nimport Quickshell\nimport Quickshell.Io\nimport Quickshell.Wayland\nimport Quickshell.Hyprland\nimport Quickshell.Services.UPower\n\nScope {\n    id: root\n    property string filePath: `${Directories.state}/user/generated/wallpaper/least_busy_region.json`\n    property real defaultX: (ConfigOptions?.background.clockX ?? -500)\n    property real defaultY: (ConfigOptions?.background.clockY ?? -500)\n    property real centerX: defaultX\n    property real centerY: defaultY\n    property real effectiveCenterX: ConfigOptions?.background.fixedClockPosition ? defaultX : centerX\n    property real effectiveCenterY: ConfigOptions?.background.fixedClockPosition ? defaultY : centerY\n    property color dominantColor: Appearance.colors.colPrimary\n    property bool dominantColorIsDark: dominantColor.hslLightness < 0.5\n    property color colBackground: ColorUtils.transparentize(ColorUtils.mix(Appearance.colors.colPrimary, Appearance.colors.colSecondaryContainer), 1)\n    property color colText: ColorUtils.colorWithLightness(Appearance.colors.colPrimary, (root.dominantColorIsDark ? 0.8 : 0.12))\n\n    function updateWidgetPosition(fileContent) {\n        // console.log(\"[BackgroundWidgets] Updating widget position with content:\", fileContent)\n        const parsedContent = JSON.parse(fileContent)\n        root.centerX = parsedContent.center_x\n        root.centerY = parsedContent.center_y\n        root.dominantColor = parsedContent.dominant_color || Appearance.colors.colPrimary\n    }\n    \n    Timer {\n        id: delayedFileRead\n        interval: ConfigOptions.hacks.arbitraryRaceConditionDelay\n        running: false\n        onTriggered: {\n            root.updateWidgetPosition(leastBusyRegionFileView.text())\n        }\n    }\n\n    FileView { \n        id: leastBusyRegionFileView\n        path: Qt.resolvedUrl(root.filePath)\n        watchChanges: !ConfigOptions?.background.fixedClockPosition\n        onFileChanged: {\n            this.reload()\n            delayedFileRead.start()\n        }\n        onLoadedChanged: {\n            const fileContent = leastBusyRegionFileView.text()\n            root.updateWidgetPosition(fileContent)\n        }\n    }\n\n    Variants { // For each monitor\n        model: Quickshell.screens\n\n        LazyLoader {\n            required property var modelData\n            readonly property HyprlandMonitor monitor: Hyprland.monitorFor(modelData)\n            activeAsync: !ToplevelManager.activeToplevel?.activated\n            component: PanelWindow { // Window\n                id: windowRoot\n                screen: modelData\n                property var textHorizontalAlignment: root.effectiveCenterX / monitor.scale < windowRoot.width / 3 ? Text.AlignLeft :\n                    (root.effectiveCenterX / monitor.scale > windowRoot.width * 2 / 3 ? Text.AlignRight : Text.AlignHCenter)\n\n                WlrLayershell.layer: WlrLayer.Bottom\n                WlrLayershell.namespace: \"quickshell:backgroundWidgets\"\n                \n                anchors {\n                    top: true\n                    bottom:true\n                    left: true\n                    right: true\n                }\n                color: \"transparent\"\n                HyprlandWindow.visibleMask: Region {\n                    item: widgetBackground\n                }\n\n                Rectangle {\n                    id: widgetBackground\n                    property real verticalPadding: 20\n                    property real horizontalPadding: 30\n                    radius: 40\n                    color: root.colBackground\n                    implicitHeight: columnLayout.implicitHeight + verticalPadding * 2\n                    implicitWidth: columnLayout.implicitWidth + horizontalPadding * 2\n                    anchors {\n                        left: parent.left\n                        top: parent.top\n                        leftMargin: (root.effectiveCenterX / monitor.scale - implicitWidth / 2)\n                        topMargin: (root.effectiveCenterY / monitor.scale - implicitHeight / 2)\n                        Behavior on leftMargin {\n                            animation: Appearance.animation.elementMove.numberAnimation.createObject(this)\n                        }\n                        Behavior on topMargin {\n                            animation: Appearance.animation.elementMove.numberAnimation.createObject(this)\n                        }\n                    }\n\n                    ColumnLayout {\n                        id: columnLayout\n                        anchors.centerIn: parent\n                        spacing: -5\n\n                        StyledText {\n                            Layout.fillWidth: true\n                            horizontalAlignment: windowRoot.textHorizontalAlignment\n                            font.pixelSize: 95\n                            color: root.colText\n                            style: Text.Raised\n                            styleColor: Appearance.colors.colShadow\n                            text: DateTime.time\n                        }\n                        StyledText {\n                            Layout.fillWidth: true\n                            horizontalAlignment: windowRoot.textHorizontalAlignment\n                            font.pixelSize: 25\n                            color: root.colText\n                            style: Text.Raised\n                            styleColor: Appearance.colors.colShadow\n                            text: DateTime.date\n                        }\n                    }\n                }\n\n            }\n        }\n\n    }\n\n}\n", "modifiedCode": "import \"root:/\"\nimport \"root:/modules/common\"\nimport \"root:/modules/common/widgets\"\nimport \"root:/services\"\nimport \"root:/modules/common/functions/color_utils.js\" as ColorUtils\nimport QtQuick\nimport QtQuick.Controls\nimport QtQuick.Layouts\nimport Quickshell\nimport Quickshell.Io\nimport Quickshell.Wayland\nimport Quickshell.Hyprland\nimport Quickshell.Services.UPower\n\nScope {\n    id: root\n    property string filePath: `${Directories.state}/user/generated/wallpaper/least_busy_region.json`\n    property real defaultX: (ConfigOptions?.background.clockX ?? -500)\n    property real defaultY: (ConfigOptions?.background.clockY ?? -500)\n    property real centerX: defaultX\n    property real centerY: defaultY\n    property real effectiveCenterX: ConfigOptions?.background.fixedClockPosition ? defaultX : centerX\n    property real effectiveCenterY: ConfigOptions?.background.fixedClockPosition ? defaultY : centerY\n    property color dominantColor: Appearance.colors.colPrimary\n    property bool dominantColorIsDark: dominantColor.hslLightness < 0.5\n    property color colBackground: ColorUtils.transparentize(ColorUtils.mix(Appearance.colors.colPrimary, Appearance.colors.colSecondaryContainer), 1)\n    property color colText: ColorUtils.colorWithLightness(Appearance.colors.colPrimary, (root.dominantColorIsDark ? 0.8 : 0.12))\n\n    function updateWidgetPosition(fileContent) {\n        // console.log(\"[BackgroundWidgets] Updating widget position with content:\", fileContent)\n        const parsedContent = JSON.parse(fileContent)\n        root.centerX = parsedContent.center_x\n        root.centerY = parsedContent.center_y\n        root.dominantColor = parsedContent.dominant_color || Appearance.colors.colPrimary\n    }\n    \n    Timer {\n        id: delayedFileRead\n        interval: ConfigOptions.hacks.arbitraryRaceConditionDelay\n        running: false\n        onTriggered: {\n            root.updateWidgetPosition(leastBusyRegionFileView.text())\n        }\n    }\n\n    FileView { \n        id: leastBusyRegionFileView\n        path: Qt.resolvedUrl(root.filePath)\n        watchChanges: !ConfigOptions?.background.fixedClockPosition\n        onFileChanged: {\n            this.reload()\n            delayedFileRead.start()\n        }\n        onLoadedChanged: {\n            const fileContent = leastBusyRegionFileView.text()\n            root.updateWidgetPosition(fileContent)\n        }\n    }\n\n    Variants { // For each monitor\n        model: Quickshell.screens\n\n        LazyLoader {\n            required property var modelData\n            readonly property HyprlandMonitor monitor: Hyprland.monitorFor(modelData)\n            activeAsync: !ToplevelManager.activeToplevel?.activated\n            component: PanelWindow { // Window\n                id: windowRoot\n                screen: modelData\n                property var textHorizontalAlignment: root.effectiveCenterX / monitor.scale < windowRoot.width / 3 ? Text.AlignLeft :\n                    (root.effectiveCenterX / monitor.scale > windowRoot.width * 2 / 3 ? Text.AlignRight : Text.AlignHCenter)\n\n                WlrLayershell.layer: WlrLayer.Bottom\n                WlrLayershell.namespace: \"quickshell:backgroundWidgets\"\n                \n                anchors {\n                    top: true\n                    bottom:true\n                    left: true\n                    right: true\n                }\n                color: \"transparent\"\n                HyprlandWindow.visibleMask: Region {\n                    item: widgetBackground\n                }\n\n                Rectangle {\n                    id: widgetBackground\n                    property real verticalPadding: 20\n                    property real horizontalPadding: 30\n                    radius: 40\n                    color: root.colBackground\n                    implicitHeight: columnLayout.implicitHeight + verticalPadding * 2\n                    implicitWidth: columnLayout.implicitWidth + horizontalPadding * 2\n                    anchors {\n                        left: parent.left\n                        top: parent.top\n                        leftMargin: (root.effectiveCenterX / monitor.scale - implicitWidth / 2)\n                        topMargin: (root.effectiveCenterY / monitor.scale - implicitHeight / 2)\n                        Behavior on leftMargin {\n                            animation: Appearance.animation.elementMove.numberAnimation.createObject(this)\n                        }\n                        Behavior on topMargin {\n                            animation: Appearance.animation.elementMove.numberAnimation.createObject(this)\n                        }\n                    }\n\n                    ColumnLayout {\n                        id: columnLayout\n                        anchors.centerIn: parent\n                        spacing: -5\n\n                        StyledText {\n                            Layout.fillWidth: true\n                            horizontalAlignment: windowRoot.textHorizontalAlignment\n                            font.pixelSize: 95\n                            color: root.colText\n                            style: Text.Raised\n                            styleColor: Appearance.colors.colShadow\n                            text: DateTime.time\n                        }\n                        StyledText {\n                            Layout.fillWidth: true\n                            horizontalAlignment: windowRoot.textHorizontalAlignment\n                            font.pixelSize: 25\n                            color: root.colText\n                            style: Text.Raised\n                            styleColor: Appearance.colors.colShadow\n                            text: DateTime.date\n                        }\n                    }\n                }\n\n            }\n        }\n\n    }\n\n}\n"}