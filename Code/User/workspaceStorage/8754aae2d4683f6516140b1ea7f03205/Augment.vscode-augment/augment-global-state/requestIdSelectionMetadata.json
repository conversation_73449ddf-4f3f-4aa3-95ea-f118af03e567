[["f0202a74-893d-4cc9-a387-6939d90fb1e5", {"value": {"selectedCode": "", "prefix": "{\n", "suffix": "  \"policies\": {\n    \"ai\": 1,\n    \"weeb\": 1\n  },\n  \"ai\": {\n    \"systemPrompt\": \"Use casual tone. No user knowledge is to be assumed except basic Linux literacy. Be brief and concise: When explaining concepts, use bullet points (prefer minus sign (-) over asterisk (*)) and highlight keywords in bold to pinpoint the main concepts instead of long paragraphs. You are also encouraged to split your response with h2 headers, each header title beginning with an emoji, like `## 🐧 Linux`. When making changes to the user's config, you must get the config to know what values there are before setting.\"\n  },\n  \"appearance\": {\n    \"fakeScreenRounding\": 2,\n    \"transparency\": true,\n    \"palette\": {\n      \"type\": \"auto\"\n    }\n  },\n  \"audio\": {\n    \"protection\": {\n      \"enable\": true,\n      \"maxAllowedIncrease\": 10,\n      \"maxAllowed\": 90\n    }\n  },\n  \"apps\": {\n    \"bluetooth\": \"kcmshell6 kcm_bluetooth\",\n    \"network\": \"plasmawindowed org.kde.plasma.networkmanagement\",\n    \"networkEthernet\": \"kcmshell6 kcm_networkmanagement\",\n    \"taskManager\": \"plasma-systemmonitor --page-name Processes\",\n    \"terminal\": \"kitty -1\"\n  },\n  \"background\": {\n    \"fixedClockPosition\": false,\n    \"clockX\": -500,\n    \"clockY\": -500\n  },\n  \"bar\": {\n    \"bottom\": false,\n    \"borderless\": true,\n    \"topLeftIcon\": \"spark\",\n    \"showBackground\": true,\n    \"verbose\": true,\n    \"resources\": {\n      \"alwaysShowSwap\": true,\n      \"alwaysShowCpu\": true\n    },\n    \"screenList\": {},\n    \"utilButtons\": {\n      \"showScreenSnip\": true,\n      \"showColorPicker\": true,\n      \"showMicToggle\": true,\n      \"showKeyboardToggle\": true,\n      \"showDarkModeToggle\": true\n    },\n    \"tray\": {\n      \"monochromeIcons\": true\n    },\n    \"workspaces\": {\n      \"shown\": 10,\n      \"showAppIcons\": true,\n      \"alwaysShowNumbers\": false,\n      \"showNumberDelay\": 300\n    }\n  },\n  \"battery\": {\n    \"low\": 20,\n    \"critical\": 5,\n    \"automaticSuspend\": true,\n    \"suspend\": 3\n  },\n  \"dock\": {\n    \"height\": 60,\n    \"hoverRegionHeight\": 3,\n    \"pinnedOnStartup\": false,\n    \"hoverToReveal\": false,\n    \"pinnedApps\": [\n      \"org.kde.dolphin\",\n      \"kitty\"\n    ]\n  },\n  \"language\": {\n    \"translator\": {\n      \"engine\": \"auto\",\n      \"targetLanguage\": \"auto\",\n      \"sourceLanguage\": \"auto\"\n    }\n  },\n  \"networking\": {\n    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36\"\n  },\n  \"osd\": {\n    \"timeout\": 1000\n  },\n  \"osk\": {\n    \"layout\": \"qwerty_full\",\n    \"pinnedOnStartup\": false\n  },\n  \"overview\": {\n    \"scale\": 0.18,\n    \"rows\": 2,\n    \"columns\": 5\n  },\n  \"resources\": {\n    \"updateInterval\": 3000\n  },\n  \"search\": {\n    \"nonAppResultDelay\": 30,\n    \"engineBaseUrl\": \"https://www.google.com/search?q=\",\n    \"excludedSites\": [\n      \"quora.com\"\n    ],\n    \"sloppy\": false,\n    \"prefix\": {\n      \"action\": \"/\",\n      \"clipboard\": \";\",\n      \"emojis\": \":\"\n    }\n  },\n  \"sidebar\": {\n    \"translator\": {\n      \"delay\": 300\n    },\n    \"booru\": {\n      \"allowNsfw\": true,\n      \"defaultProvider\": \"yandere\",\n      \"limit\": 20,\n      \"zerochan\": {\n        \"username\": \"[unset]\"\n      }\n    }\n  },\n  \"time\": {\n    \"format\": \"hh:mm\",\n    \"dateFormat\": \"dddd, dd/MM\"\n  },\n  \"windows\": {\n    \"showTitlebar\": true,\n    \"centerTitle\": true\n  },\n  \"hacks\": {\n    \"arbitraryRaceConditionDelay\": 20\n  }\n}\n", "path": "/home/<USER>/.config/illogical-impulse/config.json", "language": "json", "prefixBegin": 0, "suffixEnd": 0}}], ["43288bd3-a4da-40d2-a226-8ba6038da827", {"value": {"selectedCode": "", "prefix": "{\n", "suffix": "  \"policies\": {\n    \"ai\": 1,\n    \"weeb\": 1\n  },\n  \"ai\": {\n    \"systemPrompt\": \"Use casual tone. No user knowledge is to be assumed except basic Linux literacy. Be brief and concise: When explaining concepts, use bullet points (prefer minus sign (-) over asterisk (*)) and highlight keywords in **bold** to pinpoint the main concepts instead of long paragraphs. You are also encouraged to split your response with h2 headers, each header title beginning with an emoji, like `## 🐧 Linux`. When making changes to the user's config, you must get the config to know what values there are before setting.\"\n  },\n  \"appearance\": {\n    \"fakeScreenRounding\": 2,\n    \"transparency\": true,\n    \"palette\": {\n      \"type\": \"auto\"\n    }\n  },\n  \"audio\": {\n    \"protection\": {\n      \"enable\": true,\n      \"maxAllowedIncrease\": 10,\n      \"maxAllowed\": 90\n    }\n  },\n  \"apps\": {\n    \"bluetooth\": \"kcmshell6 kcm_bluetooth\",\n    \"network\": \"plasmawindowed org.kde.plasma.networkmanagement\",\n    \"networkEthernet\": \"kcmshell6 kcm_networkmanagement\",\n    \"taskManager\": \"plasma-systemmonitor --page-name Processes\",\n    \"terminal\": \"kitty -1\"\n  },\n  \"background\": {\n    \"fixedClockPosition\": false,\n    \"clockX\": -500,\n    \"clockY\": -500\n  },\n  \"bar\": {\n    \"bottom\": false,\n    \"borderless\": true,\n    \"topLeftIcon\": \"spark\",\n    \"showBackground\": true,\n    \"verbose\": true,\n    \"resources\": {\n      \"alwaysShowSwap\": true,\n      \"alwaysShowCpu\": true\n    },\n    \"screenList\": {},\n    \"utilButtons\": {\n      \"showScreenSnip\": true,\n      \"showColorPicker\": true,\n      \"showMicToggle\": true,\n      \"showKeyboardToggle\": true,\n      \"showDarkModeToggle\": true\n    },\n    \"tray\": {\n      \"monochromeIcons\": true\n    },\n    \"workspaces\": {\n      \"shown\": 10,\n      \"showAppIcons\": true,\n      \"alwaysShowNumbers\": false,\n      \"showNumberDelay\": 300\n    }\n  },\n  \"battery\": {\n    \"low\": 20,\n    \"critical\": 5,\n    \"automaticSuspend\": true,\n    \"suspend\": 3\n  },\n  \"dock\": {\n    \"height\": 60,\n    \"hoverRegionHeight\": 3,\n    \"pinnedOnStartup\": false,\n    \"hoverToReveal\": false,\n    \"pinnedApps\": [\n      \"org.kde.dolphin\",\n      \"kitty\"\n    ]\n  },\n  \"language\": {\n    \"translator\": {\n      \"engine\": \"auto\",\n      \"targetLanguage\": \"auto\",\n      \"sourceLanguage\": \"auto\"\n    }\n  },\n  \"networking\": {\n    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36\"\n  },\n  \"osd\": {\n    \"timeout\": 1000\n  },\n  \"osk\": {\n    \"layout\": \"qwerty_full\",\n    \"pinnedOnStartup\": false\n  },\n  \"overview\": {\n    \"scale\": 0.18,\n    \"rows\": 2,\n    \"columns\": 5\n  },\n  \"resources\": {\n    \"updateInterval\": 3000\n  },\n  \"search\": {\n    \"nonAppResultDelay\": 30,\n    \"engineBaseUrl\": \"https://www.google.com/search?q=\",\n    \"excludedSites\": [\n      \"quora.com\"\n    ],\n    \"sloppy\": false,\n    \"prefix\": {\n      \"action\": \"/\",\n      \"clipboard\": \";\",\n      \"emojis\": \":\"\n    }\n  },\n  \"sidebar\": {\n    \"translator\": {\n      \"delay\": 300\n    },\n    \"booru\": {\n      \"allowNsfw\": true,\n      \"defaultProvider\": \"yandere\",\n      \"limit\": 20,\n      \"zerochan\": {\n        \"username\": \"[unset]\"\n      }\n    }\n  },\n  \"time\": {\n    \"format\": \"hh:mm\",\n    \"dateFormat\": \"dddd, dd/MM\"\n  },\n  \"windows\": {\n    \"showTitlebar\": true,\n    \"centerTitle\": true\n  },\n  \"hacks\": {\n    \"arbitraryRaceConditionDelay\": 20\n  }\n}\n", "path": "illogical-impulse/config.json", "language": "json", "prefixBegin": 0, "suffixEnd": 0}}], ["cda5bd49-5a8a-46a1-8a24-e120bd2c5d76", {"value": {"selectedCode": "", "prefix": "import \"root:/\"\nimport \"root:/services\"\nimport \"root:/modules/common/\"\nimport \"root:/modules/common/widgets\"\nimport \"root:/modules/common/functions/color_utils.js\" as ColorUtils\nimport QtQuick\nimport QtQuick.Controls\nimport QtQuick.Layouts\nimport Qt5Compat.GraphicalEffects\nimport Quickshell\nimport Quickshell.Wayland\nimport Quickshell.Hyprland\nimport Quickshell.Services.UPower\n\nScope {\n    id: bar\n\n    readonly property int barHeight: Appearance.sizes.barHeight\n    readonly property int osdHideMouseMoveThreshold: 20\n    property bool showBarBackground: ConfigOptions.bar.showBackground\n\n    component VerticalBarSeparator: Rectangle {\n        Layout.topMargin: barHeight / 3\n        Layout.bottomMargin: barHeight / 3\n        Layout.fillHeight: true\n        implicitWidth: 1\n        color: Appearance.colors.colOutlineVariant\n    }\n\n    Variants { // For each monitor\n        model: {\n            const screens = Quickshell.screens;\n            const list = ConfigOptions.bar.screenList;\n            if (!list || list.length === 0)\n                return screens;\n            return screens.filter(screen => list.includes(screen.name));\n        }\n\n        PanelWindow { // Bar window\n            id: barRoot\n            screen: modelData\n\n            property ShellScreen modelData\n            property var brightnessMonitor: Brightness.getMonitorForScreen(modelData)\n            property real useShortenedForm: (Appearance.sizes.barHellaShortenScreenWidthThreshold >= screen.width) ? 2 :\n                (Appearance.sizes.barShortenScreenWidthThreshold >= screen.width) ? 1 : 0\n            readonly property int centerSideModuleWidth: \n                (useShortenedForm == 2) ? Appearance.sizes.barCenterSideModuleWidthHellaShortened :\n                (useShortenedForm == 1) ? Appearance.sizes.barCenterSideModuleWidthShortened : \n                    Appearance.sizes.barCenterSideModuleWidth\n\n            WlrLayershell.namespace: \"quickshell:bar\"\n            implicitHeight: barHeight + Appearance.rounding.screenRounding\n            exclusiveZone: showBarBackground ? barHeight : (barHeight - 4)\n            mask: Region {\n                item: barContent\n            }\n            color: \"transparent\"\n\n            anchors {\n                top: !ConfigOptions.bar.bottom\n                bottom: ConfigOptions.bar.bottom\n                left: true\n                right: true\n            }\n\n            Rectangle { // Bar background\n                id: barContent\n                anchors {\n                    right: parent.right\n                    left: parent.left\n                    top: !ConfigOptions.bar.bottom ? parent.top : undefined\n                    bottom: ConfigOptions.bar.bottom ? parent.bottom : undefined\n                }\n                color: showBarBackground ? Appearance.colors.colLayer0 : \"transparent\"\n                height: barHeight\n                \n                MouseArea { // Left side | scroll to change brightness\n                    id: barLeftSideMouseArea\n                    anchors.left: parent.left\n                    implicitHeight: barHeight\n                    width: (barRoot.width - middleSection.width) / 2\n                    property bool hovered: false\n                    property real lastScrollX: 0\n                    property real lastScrollY: 0\n                    property bool trackingScroll: false\n                    acceptedButtons: Qt.LeftButton\n                    hoverEnabled: true\n                    propagateComposedEvents: true\n                    onEntered: (event) => {\n                        barLeftSideMouseArea.hovered = true\n                    }\n                    onExited: (event) => {\n                        barLeftSideMouseArea.hovered = false\n                        barLeftSideMouseArea.trackingScroll = false\n                    }\n                    onPressed: (event) => {\n                        if (event.button === Qt.LeftButton) {\n                            Hyprland.dispatch('global quickshell:sidebarLeftOpen')\n                        }\n                    }\n                    // Scroll to change brightness\n                    WheelHandler {\n                        onWheel: (event) => {\n                            if (event.angleDelta.y < 0)\n                                barRoot.brightnessMonitor.setBrightness(barRoot.brightnessMonitor.brightness - 0.05);\n                            else if (event.angleDelta.y > 0)\n                                barRoot.brightnessMonitor.setBrightness(barRoot.brightnessMonitor.brightness + 0.05);\n                            // Store the mouse position and start tracking\n                            barLeftSideMouseArea.lastScrollX = event.x;\n                            barLeftSideMouseArea.lastScrollY = event.y;\n                            barLeftSideMouseArea.trackingScroll = true;\n                        }\n                        acceptedDevices: PointerDevice.Mouse | PointerDevice.TouchPad\n                    }\n                    onPositionChanged: (mouse) => {\n                        if (barLeftSideMouseArea.trackingScroll) {\n                            const dx = mouse.x - barLeftSideMouseArea.lastScrollX;\n                            const dy = mouse.y - barLeftSideMouseArea.lastScrollY;\n                            if (Math.sqrt(dx*dx + dy*dy) > osdHideMouseMoveThreshold) {\n                                Hyprland.dispatch('global quickshell:osdBrightnessHide')\n                                barLeftSideMouseArea.trackingScroll = false;\n                            }\n                        }\n                    }\n                    Item {  // Left section\n                        anchors.fill: parent\n                        implicitHeight: leftSectionRowLayout.implicitHeight\n                        implicitWidth: leftSectionRowLayout.implicitWidth\n\n                        ScrollHint {\n                            reveal: barLeftSideMouseArea.hovered\n                            icon: \"light_mode\"\n", "suffix": "                            tooltipText: qsTr(\"滑动以更改亮度\")\n                            side: \"left\"\n                            anchors.left: parent.left\n                            anchors.verticalCenter: parent.verticalCenter\n                            \n                        }\n                        \n                        RowLayout { // Content\n                            id: leftSectionRowLayout\n                            anchors.fill: parent\n                            spacing: 10\n\n                            RippleButton { // Left sidebar button\n                                Layout.alignment: Qt.AlignLeft | Qt.AlignVCenter\n                                Layout.leftMargin: Appearance.rounding.screenRounding\n                                Layout.fillWidth: false\n                                property real buttonPadding: 5\n                                implicitWidth: distroIcon.width + buttonPadding * 2\n                                implicitHeight: distroIcon.height + buttonPadding * 2\n                                \n                                buttonRadius: Appearance.rounding.full\n                                colBackground: barLeftSideMouseArea.hovered ? Appearance.colors.colLayer1Hover : ColorUtils.transparentize(Appearance.colors.colLayer1Hover, 1)\n                                colBackgroundHover: Appearance.colors.colLayer1Hover\n                                colRipple: Appearance.colors.colLayer1Active\n                                colBackgroundToggled: Appearance.colors.colSecondaryContainer\n                                colBackgroundToggledHover: Appearance.colors.colSecondaryContainerHover\n                                colRippleToggled: Appearance.colors.colSecondaryContainerActive\n                                toggled: GlobalStates.sidebarLeftOpen\n                                    property color colText: toggled ? Appearance.m3colors.m3onSecondaryContainer : Appearance.colors.colOnLayer0\n\n                                onPressed: {\n                                    Hyprland.dispatch('global quickshell:sidebarLeftToggle')\n                                }\n\n                                CustomIcon {\n                                    id: distroIcon\n                                    anchors.centerIn: parent\n                                    width: 19.5\n                                    height: 19.5\n                                    source: ConfigOptions.bar.topLeftIcon == 'distro' ? \n                                        SystemInfo.distroIcon : \"spark-symbolic\"\n                                }\n                                \n                                ColorOverlay {\n                                    anchors.fill: distroIcon\n                                    source: distroIcon\n                                    color: Appearance.colors.colOnLayer0\n                                }\n                            }\n\n                            ActiveWindow {\n                                visible: barRoot.useShortenedForm === 0\n                                Layout.rightMargin: Appearance.rounding.screenRounding\n                                Layout.fillWidth: true\n                                Layout.fillHeight: true\n                                bar: barRoot\n                            }\n                        }\n                    }\n                }\n\n                RowLayout { // Middle section\n                    id: middleSection\n                    anchors.centerIn: parent\n                    spacing: ConfigOptions?.bar.borderless ? 4 : 8\n\n                    BarGroup {\n                        id: leftCenterGroup\n                        Layout.preferredWidth: barRoot.centerSideModuleWidth\n                        Layout.fillHeight: true\n\n                        Resources {\n                            alwaysShowAllResources: barRoot.useShortenedForm === 2\n                            Layout.fillWidth: barRoot.useShortenedForm === 2\n                        }\n\n                        Media {\n                            visible: barRoot.useShortenedForm < 2\n                            Layout.fillWidth: true\n                        }\n\n                    }\n\n                    VerticalBarSeparator {visible: ConfigOptions?.bar.borderless}\n\n                    BarGroup {\n                        id: middleCenterGroup\n                        padding: workspacesWidget.widgetPadding\n                        Layout.fillHeight: true\n                        \n                        Workspaces {\n                            id: workspacesWidget\n                            bar: barRoot\n                            Layout.fillHeight: true\n                            MouseArea { // Right-click to toggle overview\n                                anchors.fill: parent\n                                acceptedButtons: Qt.RightButton\n                                \n                                onPressed: (event) => {\n                                    if (event.button === Qt.RightButton) {\n                                        Hyprland.dispatch('global quickshell:overviewToggle')\n                                    }\n                                }\n                            }\n                        }\n                    }\n\n                    VerticalBarSeparator {visible: ConfigOptions?.bar.borderless}\n\n                    MouseArea {\n                        id: rightCenterGroup\n                        implicitWidth: rightCenterGroupContent.implicitWidth\n                        implicitHeight: rightCenterGroupContent.implicitHeight\n                        Layout.preferredWidth: barRoot.centerSideModuleWidth\n                        Layout.fillHeight: true\n\n                        onPressed: {\n                            Hyprland.dispatch('global quickshell:sidebarRightToggle')\n                        }\n\n                        BarGroup {\n                            id: rightCenterGroupContent\n                            anchors.fill: parent\n                            \n                            ClockWidget {\n                                showDate: (ConfigOptions.bar.verbose && barRoot.useShortenedForm < 2)\n                                Layout.alignment: Qt.AlignVCenter\n                                Layout.fillWidth: true\n                            }\n\n                            UtilButtons {\n                                visible: (ConfigOptions.bar.verbose && barRoot.useShortenedForm === 0)\n                                Layout.alignment: Qt.AlignVCenter\n                            }\n\n                            BatteryIndicator {\n                                visible: (barRoot.useShortenedForm < 2 && UPower.displayDevice.isLaptopBattery)\n                                Layout.alignment: Qt.AlignVCenter\n                            }\n                        }\n                    }\n\n                }\n\n                MouseArea { // Right side | scroll to change volume\n                    id: barRightSideMouseArea\n\n                    anchors.right: parent.right\n                    implicitHeight: barHeight\n                    width: (barRoot.width - middleSection.width) / 2\n\n                    property bool hovered: false\n                    property real lastScrollX: 0\n                    property real lastScrollY: 0\n                    property bool trackingScroll: false\n                    \n                    acceptedButtons: Qt.LeftButton\n                    hoverEnabled: true\n                    propagateComposedEvents: true\n                    onEntered: (event) => {\n                        barRightSideMouseArea.hovered = true\n                    }\n                    onExited: (event) => {\n                        barRightSideMouseArea.hovered = false\n                        barRightSideMouseArea.trackingScroll = false\n                    }\n                    onPressed: (event) => {\n                        if (event.button === Qt.LeftButton) {\n                            Hyprland.dispatch('global quickshell:sidebarRightOpen')\n                        }\n                        else if (event.button === Qt.RightButton) {\n                            MprisController.activePlayer.next()\n                        }\n                    }\n                    // Scroll to change volume\n                    WheelHandler {\n                        onWheel: (event) => {\n                            const currentVolume = Audio.value;\n                            const step = currentVolume < 0.1 ? 0.01 : 0.02 || 0.2;\n                            if (event.angleDelta.y < 0)\n                                Audio.sink.audio.volume -= step;\n                            else if (event.angleDelta.y > 0)\n                                Audio.sink.audio.volume = Math.min(1, Audio.sink.audio.volume + step);\n                            // Store the mouse position and start tracking\n                            barRightSideMouseArea.lastScrollX = event.x;\n                            barRightSideMouseArea.lastScrollY = event.y;\n                            barRightSideMouseArea.trackingScroll = true;\n                        }\n                        acceptedDevices: PointerDevice.Mouse | PointerDevice.TouchPad\n                    }\n                    onPositionChanged: (mouse) => {\n                        if (barRightSideMouseArea.trackingScroll) {\n                            const dx = mouse.x - barRightSideMouseArea.lastScrollX;\n                            const dy = mouse.y - barRightSideMouseArea.lastScrollY;\n                            if (Math.sqrt(dx*dx + dy*dy) > osdHideMouseMoveThreshold) {\n                                Hyprland.dispatch('global quickshell:osdVolumeHide')\n                                barRightSideMouseArea.trackingScroll = false;\n                            }\n                        }\n                    }\n\n                    Item {\n                        anchors.fill: parent\n                        implicitHeight: rightSectionRowLayout.implicitHeight\n                        implicitWidth: rightSectionRowLayout.implicitWidth\n                        \n                        ScrollHint {\n                            reveal: barRightSideMouseArea.hovered\n                            icon: \"volume_up\"\n                            tooltipText: qsTr(\"滑动以更改音量\")\n                            side: \"right\"\n                            anchors.right: parent.right\n                            anchors.verticalCenter: parent.verticalCenter\n                        }\n\n                        RowLayout {\n                            id: rightSectionRowLayout\n                            anchors.fill: parent\n                            spacing: 5\n                            layoutDirection: Qt.RightToLeft\n                    \n                            RippleButton { // Right sidebar button\n                                id: rightSidebarButton\n                                Layout.margins: 4\n                                Layout.rightMargin: Appearance.rounding.screenRounding\n                                Layout.fillHeight: true\n                                implicitWidth: indicatorsRowLayout.implicitWidth + 10*2\n                                buttonRadius: Appearance.rounding.full\n                                colBackground: barRightSideMouseArea.hovered ? Appearance.colors.colLayer1Hover : ColorUtils.transparentize(Appearance.colors.colLayer1Hover, 1)\n                                colBackgroundHover: Appearance.colors.colLayer1Hover\n                                colRipple: Appearance.colors.colLayer1Active\n                                colBackgroundToggled: Appearance.colors.colSecondaryContainer\n                                colBackgroundToggledHover: Appearance.colors.colSecondaryContainerHover\n                                colRippleToggled: Appearance.colors.colSecondaryContainerActive\n                                toggled: GlobalStates.sidebarRightOpen\n                                property color colText: toggled ? Appearance.m3colors.m3onSecondaryContainer : Appearance.colors.colOnLayer0\n\n                                Behavior on colText {\n                                    animation: Appearance.animation.elementMoveFast.colorAnimation.createObject(this)\n                                }\n\n                                onPressed: {\n                                    Hyprland.dispatch('global quickshell:sidebarRightToggle')\n                                }\n\n                                RowLayout {\n                                    id: indicatorsRowLayout\n                                    anchors.centerIn: parent\n                                    property real realSpacing: 15\n                                    spacing: 0\n                                    \n                                    Revealer {\n                                        reveal: Audio.sink?.audio?.muted ?? false\n                                        Layout.fillHeight: true\n                                        Layout.rightMargin: reveal ? indicatorsRowLayout.realSpacing : 0\n                                        Behavior on Layout.rightMargin {\n                                            NumberAnimation {\n                                                duration: Appearance.animation.elementMoveFast.duration\n                                                easing.type: Appearance.animation.elementMoveFast.type\n                                                easing.bezierCurve: Appearance.animation.elementMoveFast.bezierCurve\n                                            }\n                                        }\n                                        MaterialSymbol {\n                                            text: \"静音\"\n                                            iconSize: Appearance.font.pixelSize.larger\n                                            color: rightSidebarButton.colText\n                                        }\n                                    }\n                                    Revealer {\n                                        reveal: Audio.source?.audio?.muted ?? false\n                                        Layout.fillHeight: true\n                                        Layout.rightMargin: reveal ? indicatorsRowLayout.realSpacing : 0\n                                        Behavior on Layout.rightMargin {\n                                            NumberAnimation {\n                                                duration: Appearance.animation.elementMoveFast.duration\n                                                easing.type: Appearance.animation.elementMoveFast.type\n                                                easing.bezierCurve: Appearance.animation.elementMoveFast.bezierCurve\n                                            }\n                                        }\n                                        MaterialSymbol {\n                                            text: \"麦克风关闭\"\n                                            iconSize: Appearance.font.pixelSize.larger\n                                            color: rightSidebarButton.colText\n                                        }\n                                    }\n                                    MaterialSymbol {\n                                        Layout.rightMargin: indicatorsRowLayout.realSpacing\n                                        text: Network.materialSymbol\n                                        iconSize: Appearance.font.pixelSize.larger\n                                        color: rightSidebarButton.colText\n                                    }\n                                    MaterialSymbol {\n                                        text: Bluetooth.bluetoothConnected ? \"bluetooth_connected\" : Bluetooth.bluetoothEnabled ? \"蓝牙\" : \"蓝牙已禁用\"\n                                        iconSize: Appearance.font.pixelSize.larger\n                                        color: rightSidebarButton.colText\n                                    }\n                                }\n                            }\n\n                            SysTray {\n                                bar: barRoot\n                                visible: barRoot.useShortenedForm === 0\n                                Layout.fillWidth: false\n                                Layout.fillHeight: true\n                            }\n\n                            Item {\n                                Layout.fillWidth: true\n                                Layout.fillHeight: true\n                            }\n                        }\n                    }\n                }\n            }\n\n            // Round decorators\n            Item {\n                anchors {\n                    left: parent.left\n                    right: parent.right\n                    // top: barContent.bottom\n                    top: ConfigOptions.bar.bottom ? undefined : barContent.bottom\n                    bottom: ConfigOptions.bar.bottom ? barContent.top : undefined\n                }\n                height: Appearance.rounding.screenRounding\n                visible: showBarBackground\n\n                RoundCorner {\n                    anchors.top: parent.top\n                    anchors.left: parent.left\n                    size: Appearance.rounding.screenRounding\n                    corner: ConfigOptions.bar.bottom ? cornerEnum.bottomLeft : cornerEnum.topLeft\n                    color: showBarBackground ? Appearance.colors.colLayer0 : \"transparent\"\n                    opacity: 1.0 - Appearance.transparency\n                }\n                RoundCorner {\n                    anchors.top: parent.top\n                    anchors.right: parent.right\n                    size: Appearance.rounding.screenRounding\n                    corner: ConfigOptions.bar.bottom ? cornerEnum.bottomRight : cornerEnum.topRight\n                    color: showBarBackground ? Appearance.colors.colLayer0 : \"transparent\"\n                    opacity: 1.0 - Appearance.transparency\n                }\n            }\n\n        }\n\n    }\n\n}\n", "path": "quickshell/modules/bar/Bar.qml", "language": "plaintext", "prefixBegin": 0, "suffixEnd": 0}}], ["eba13306-8063-461b-90df-06cb4515d20c", {"value": {"selectedCode": "", "prefix": "import \"root:/\"\nimport \"root:/services\"\nimport \"root:/modules/common\"\nimport \"root:/modules/common/widgets\"\nimport QtQuick\nimport QtQuick.Controls\nimport QtQuick.Effects\nimport QtQuick.Layouts\nimport Quickshell.Io\nimport Quickshell\nimport Quickshell.Widgets\nimport Quickshell.Wayland\nimport Quickshell.Hyprland\n\nScope { // Scope\n    id: root\n    property bool pinned: ConfigOptions?.dock.pinnedOnStartup ?? false\n\n    Variants { // For each monitor\n        model: Quickshell.screens\n\n        LazyLoader {\n            id: dockLoader\n            required property var modelData\n            activeAsync: ConfigOptions?.dock.hoverToReveal || (!ToplevelManager.activeToplevel?.activated)\n\n            component: PanelWindow { // Window\n                id: dockRoot\n                screen: dockLoader.modelData\n                \n                property bool reveal: root.pinned \n                    || (ConfigOptions?.dock.hoverToReveal && dockMouseArea.containsMouse) \n                    || dockApps.requestDockShow \n                    || (!ToplevelManager.activeToplevel?.activated)\n\n                anchors {\n                    bottom: true\n                    left: true\n                    right: true\n                }\n\n                exclusiveZone: root.pinned ? implicitHeight \n                    - (Appearance.sizes.hyprlandGapsOut) \n                    - (Appearance.sizes.elevationMargin - Appearance.sizes.hyprlandGapsOut) : 0\n\n                implicitWidth: dockBackground.implicitWidth\n                WlrLayershell.namespace: \"quickshell:dock\"\n                color: \"transparent\"\n\n                implicitHeight: (ConfigOptions?.dock.height ?? 70) + Appearance.sizes.elevationMargin + Appearance.sizes.hyprlandGapsOut\n\n                mask: Region {\n                    item: dockMouseArea\n                }\n\n                MouseArea {\n                    id: dockMouseArea\n                    anchors.top: parent.top\n                    height: parent.height\n                    anchors.topMargin: dockRoot.reveal ? 0 : \n                        ConfigOptions?.dock.hoverToReveal ? (dockRoot.implicitHeight - ConfigOptions.dock.hoverRegionHeight) :\n                        (dockRoot.implicitHeight + 1)\n                        \n                    anchors.left: parent.left\n                    anchors.right: parent.right\n                    hoverEnabled: true\n\n                    Behavior on anchors.topMargin {\n                        animation: Appearance.animation.elementMoveFast.numberAnimation.createObject(this)\n                    }\n\n                    Item {\n                        id: dockHoverRegion\n                        anchors.fill: parent\n\n                        Item { // Wrapper for the dock background\n                            id: dockBackground\n                            anchors {\n                                top: parent.top\n                                bottom: parent.bottom\n                                horizontalCenter: parent.horizontalCenter\n                            }\n\n                            implicitWidth: dockRow.implicitWidth + 5 * 2\n                            height: parent.height - Appearance.sizes.elevationMargin - Appearance.sizes.hyprlandGapsOut\n\n                            StyledRectangularShadow {\n                                target: dockVisualBackground\n                            }\n                            Rectangle { // The real rectangle that is visible\n                                id: dockVisualBackground\n                                property real margin: Appearance.sizes.elevationMargin\n                                anchors.fill: parent\n                                anchors.topMargin: Appearance.sizes.elevationMargin\n                                anchors.bottomMargin: Appearance.sizes.hyprlandGapsOut\n                                color: Appearance.colors.colLayer0\n                                radius: Appearance.rounding.large\n                            }\n\n                            RowLayout {\n                                id: dockRow\n                                anchors.top: parent.top\n                                anchors.bottom: parent.bottom\n                                anchors.horizontalCenter: parent.horizontalCenter\n                                spacing: 3\n                                property real padding: 5\n\n                                VerticalButtonGroup {\n                                    Layout.topMargin: Appearance.sizes.hyprlandGapsOut // why does this work\n                                    GroupButton { // Pin button\n                                        baseWidth: 35\n                                        baseHeight: 35\n                                        clickedWidth: baseWidth\n                                        clickedHeight: baseHeight + 20\n                                        buttonRadius: Appearance.rounding.normal\n                                        toggled: root.pinned\n                                        onClicked: root.pinned = !root.pinned\n                                        contentItem: MaterialSymbol {\n                                            text: \"保持\"\n", "suffix": "                                            horizontalAlignment: Text.AlignHCenter\n                                            iconSize: Appearance.font.pixelSize.larger\n                                            color: root.pinned ? Appearance.m3colors.m3onPrimary : Appearance.colors.colOnLayer0\n                                        }\n                                    }\n                                }\n                                DockSeparator {}\n                                DockApps { id: dockApps; }\n                                DockSeparator {}\n                                DockButton {\n                                    Layout.fillHeight: true\n                                    onClicked: Hyprland.dispatch(\"global quickshell:overviewToggle\")\n                                    contentItem: MaterialSymbol {\n                                        anchors.fill: parent\n                                        horizontalAlignment: Text.AlignHCenter\n                                        font.pixelSize: parent.width / 2\n                                        text: \"应用\"\n                                        color: Appearance.colors.colOnLayer0\n                                    }\n                                }\n                            }\n                        }    \n                    }\n\n                }\n            }\n        }\n    }\n}\n", "path": "quickshell/modules/dock/Dock.qml", "language": "plaintext", "prefixBegin": 0, "suffixEnd": 0}}], ["de982f66-eb3d-4f3e-87d6-3b3b2c5909c6", {"value": {"selectedCode": "", "prefix": "pragma Singleton\npragma ComponentBehavior: Bound\n\nimport Quickshell\nimport Quickshell.Io\nimport QtQuick\n\n/**\n * Simple polled network state service.\n */\nSingleton {\n    id: root\n\n    property bool wifi: true\n    property bool ethernet: false\n    property int updateInterval: 1000\n    property string networkName: \"\"\n    property int networkStrength\n    property string materialSymbol: ethernet ? \"lan\" :\n", "suffix": "        (Network.networkName.length > 0 && Network.networkName != \"lo\") ? (\n        Network.networkStrength > 80 ? \"signal_wifi_4_bar\" :\n        Network.networkStrength > 60 ? \"network_wifi_3_bar\" :\n        Network.networkStrength > 40 ? \"network_wifi_2_bar\" :\n        Network.networkStrength > 20 ? \"network_wifi_1_bar\" :\n        \"signal_wifi_0_bar\"\n    ) : \"signal_wifi_off\"\n    function update() {\n        updateConnectionType.startCheck();\n        updateNetworkName.running = true;\n        updateNetworkStrength.running = true;\n    }\n\n    Timer {\n        interval: 10\n        running: true\n        repeat: true\n        onTriggered: {\n            root.update();\n            interval = root.updateInterval;\n        }\n    }\n\n    Process {\n        id: updateConnectionType\n        property string buffer\n        command: [\"sh\", \"-c\", \"nmcli -t -f NAME,TYPE,DEVICE c show --active\"]\n        running: true\n        function startCheck() {\n            buffer = \"\";\n            updateConnectionType.running = true;\n        }\n        stdout: SplitParser {\n            onRead: data => {\n                updateConnectionType.buffer += data + \"\\n\";\n            }\n        }\n        onExited: (exitCode, exitStatus) => {\n            const lines = updateConnectionType.buffer.trim().split('\\n');\n            let hasEthernet = false;\n            let hasWifi = false;\n            lines.forEach(line => {\n                if (line.includes(\"ethernet\"))\n                    hasEthernet = true;\n                else if (line.includes(\"wireless\"))\n                    hasWifi = true;\n            });\n            root.ethernet = hasEthernet;\n            root.wifi = hasWifi;\n        }\n    }\n\n    Process {\n        id: updateNetworkName\n        command: [\"sh\", \"-c\", \"nmcli -t -f NAME c show --active | head -1\"]\n        running: true\n        stdout: SplitParser {\n            onRead: data => {\n                root.networkName = data;\n            }\n        }\n    }\n\n    Process {\n        id: updateNetworkStrength\n        running: true\n        command: [\"sh\", \"-c\", \"nmcli -f IN-USE,SIGNAL,SSID device wifi | awk '/^\\*/{if (NR!=1) {print $2}}'\"]\n        stdout: SplitParser {\n            onRead: data => {\n                root.networkStrength = parseInt(data);\n            }\n        }\n    }\n}\n", "path": "quickshell/services/Network.qml", "language": "plaintext", "prefixBegin": 0, "suffixEnd": 0}}], ["1301f117-943d-445a-81e7-dd286f656e42", {"value": {"selectedCode": "", "prefix": "//@ pragma UseQApplication\n", "suffix": "//@ pragma Env QS_NO_RELOAD_POPUP=1\n//@ pragma Env QT_QUICK_CONTROLS_STYLE=Basic\n\n// Adjust this to make the shell smaller or larger\n//@ pragma Env QT_SCALE_FACTOR=1\n\nimport \"./modules/common/\"\nimport \"./modules/backgroundWidgets/\"\nimport \"./modules/bar/\"\nimport \"./modules/cheatsheet/\"\nimport \"./modules/dock/\"\nimport \"./modules/mediaControls/\"\nimport \"./modules/notificationPopup/\"\nimport \"./modules/onScreenDisplay/\"\nimport \"./modules/onScreenKeyboard/\"\nimport \"./modules/overview/\"\nimport \"./modules/screenCorners/\"\nimport \"./modules/session/\"\nimport \"./modules/sidebarLeft/\"\nimport \"./modules/sidebarRight/\"\nimport QtQuick\nimport QtQuick.Controls\nimport QtQuick.Layouts\nimport QtQuick.Window\nimport Quickshell\nimport \"./services/\"\n\nShellRoot {\n    // Enable/disable modules here. False = not loaded at all, so rest assured\n    // no unnecessary stuff will take up memory if you decide to only use, say, the overview.\n    property bool enableBar: true\n    property bool enableBackgroundWidgets: true\n    property bool enableCheatsheet: true\n    property bool enableDock: false\n    property bool enableMediaControls: true\n    property bool enableNotificationPopup: true\n    property bool enableOnScreenDisplayBrightness: true\n    property bool enableOnScreenDisplayVolume: true\n    property bool enableOnScreenKeyboard: true\n    property bool enableOverview: true\n    property bool enableReloadPopup: true\n    property bool enableScreenCorners: true\n    property bool enableSession: true\n    property bool enableSidebarLeft: true\n    property bool enableSidebarRight: true\n\n    // Force initialization of some singletons\n    Component.onCompleted: {\n        MaterialThemeLoader.reapplyTheme()\n        ConfigLoader.loadConfig()\n        PersistentStateManager.loadStates()\n        Cliphist.refresh()\n        FirstRunExperience.load()\n    }\n\n    LazyLoader { active: enableBar; component: Bar {} }\n    LazyLoader { active: enableBackgroundWidgets; component: BackgroundWidgets {} }\n    LazyLoader { active: enableCheatsheet; component: Cheatsheet {} }\n    LazyLoader { active: enableDock; component: Dock {} }\n    LazyLoader { active: enableMediaControls; component: MediaControls {} }\n    LazyLoader { active: enableNotificationPopup; component: NotificationPopup {} }\n    LazyLoader { active: enableOnScreenDisplayBrightness; component: OnScreenDisplayBrightness {} }\n    LazyLoader { active: enableOnScreenDisplayVolume; component: OnScreenDisplayVolume {} }\n    LazyLoader { active: enableOnScreenKeyboard; component: OnScreenKeyboard {} }\n    LazyLoader { active: enableOverview; component: Overview {} }\n    LazyLoader { active: enableReloadPopup; component: ReloadPopup {} }\n    LazyLoader { active: enableScreenCorners; component: ScreenCorners {} }\n    LazyLoader { active: enableSession; component: Session {} }\n    LazyLoader { active: enableSidebarLeft; component: SidebarLeft {} }\n    LazyLoader { active: enableSidebarRight; component: SidebarRight {} }\n}\n\n", "path": "quickshell/shell.qml", "language": "plaintext", "prefixBegin": 0, "suffixEnd": 0}}], ["f4dc5ae2-e97d-45d3-a28c-f8fa57337513", {"value": {"selectedCode": "", "prefix": "pragma Singleton\npragma ComponentBehavior: Bound\n\nimport \"root:/modules/common/functions/string_utils.js\" as StringUtils\nimport \"root:/modules/common/functions/object_utils.js\" as ObjectUtils\nimport \"root:/modules/common\"\nimport Quickshell;\nimport Quickshell.Io;\nimport Qt.labs.platform\nimport QtQuick;\n\n/**\n * Basic service to handle LLM chats. Supports Google's and OpenAI's API formats.\n */\nSingleton {\n    id: root\n\n    readonly property string interfaceRole: \"interface\"\n    readonly property string apiKeyEnvVarName: \"API_KEY\"\n    property Component aiMessageComponent: AiMessageData {}\n    property string systemPrompt: ConfigOptions?.ai?.systemPrompt ?? \"\"\n    property var messages: []\n    property var messageIDs: []\n    property var messageByID: ({})\n    readonly property var apiKeys: KeyringStorage.keyringData?.apiKeys ?? {}\n    readonly property var apiKeysLoaded: KeyringStorage.loaded\n    property var postResponseHook\n    property real temperature: PersistentStates?.ai?.temperature ?? 0.5\n\n    function idForMessage(message) {\n        // Generate a unique ID using timestamp and random value\n        return Date.now().toString(36) + Math.random().toString(36).substr(2, 8);\n    }\n\n    function safeModelName(modelName) {\n        return modelName.replace(/:/g, \"_\").replace(/\\./g, \"_\")\n    }\n\n    // Model properties:\n    // - name: Name of the model\n    // - icon: Icon name of the model\n    // - description: Description of the model\n    // - endpoint: Endpoint of the model\n    // - model: Model name of the model\n    // - requires_key: Whether the model requires an API key\n    // - key_id: The identifier of the API key. Use the same identifier for models that can be accessed with the same key.\n    // - key_get_link: Link to get an API key\n    // - key_get_description: Description of pricing and how to get an API key\n    // - api_format: The API format of the model. Can be \"openai\" or \"gemini\". Default is \"openai\".\n    // - tools: List of tools that the model can use. Each tool is an object with the tool name as the key and an empty object as the value.\n    // - extraParams: Extra parameters to be passed to the model. This is a JSON object.\n    property var models: {\n        \"gemini-2.0-flash-search\": {\n            \"name\": \"Gemini 2.0 Flash (Search)\",\n            \"icon\": \"google-gemini-symbolic\",\n            \"description\": qsTr(\"Online | Google's model\\nGives up-to-date information with search.\"),\n            \"homepage\": \"https://aistudio.google.com\",\n            \"endpoint\": \"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:streamGenerateContent\",\n            \"model\": \"gemini-2.0-flash\",\n            \"requires_key\": true,\n            \"key_id\": \"gemini\",\n            \"key_get_link\": \"https://aistudio.google.com/app/apikey\",\n            \"key_get_description\": qsTr(\"**Pricing**: free. Data used for training.\\n\\n**Instructions**: Log into Google account, allow AI Studio to create Google Cloud project or whatever it asks, go back and click Get API key\"),\n            \"api_format\": \"gemini\",\n            \"tools\": [\n                {\n                    \"google_search\": {}\n                },\n            ]\n        },\n        \"gemini-2.0-flash-tools\": {\n            \"name\": \"Gemini 2.0 Flash (Tools)\",\n            \"icon\": \"google-gemini-symbolic\",\n            \"description\": qsTr(\"Experimental | Online | Google's model\\nCan do a little more but doesn't search quickly\"),\n            \"homepage\": \"https://aistudio.google.com\",\n            \"endpoint\": \"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:streamGenerateContent\",\n            \"model\": \"gemini-2.0-flash\",\n            \"requires_key\": true,\n            \"key_id\": \"gemini\",\n            \"key_get_link\": \"https://aistudio.google.com/app/apikey\",\n            \"key_get_description\": qsTr(\"**Pricing**: free. Data used for training.\\n\\n**Instructions**: Log into Google account, allow AI Studio to create Google Cloud project or whatever it asks, go back and click Get API key\"),\n            \"api_format\": \"gemini\",\n            \"tools\": [\n                {\n                    \"functionDeclarations\": [\n                        {\n                            \"name\": \"switch_to_search_mode\",\n                            \"description\": \"Search the web\",\n                        },\n                        {\n                            \"name\": \"get_shell_config\",\n                            \"description\": \"Get the desktop shell config file contents\",\n                        },\n                        {\n                            \"name\": \"set_shell_config\",\n                            \"description\": \"Set a field in the desktop graphical shell config file. Must only be used after `get_shell_config`.\",\n                            \"parameters\": {\n                                \"type\": \"object\",\n                                \"properties\": {\n                                    \"key\": {\n                                        \"type\": \"string\",\n                                        \"description\": \"The key to set, e.g. `bar.borderless`. MUST NOT BE GUESSED, use `get_shell_config` to see what keys are available before setting.\",\n                                    },\n                                    \"value\": {\n                                        \"type\": \"string\",\n                                        \"description\": \"The value to set, e.g. `true`\"\n                                    }\n                                },\n                                \"required\": [\"key\", \"value\"]\n                            }\n                        },\n                    ]\n                }\n            ]\n        },\n", "suffix": "        \"gemini-2.5-flash-search\": {\n            \"name\": \"Gemini 2.5 Flash (Search)\",\n            \"icon\": \"google-gemini-symbolic\",\n            \"description\": qsTr(\"Online | Google's model\\nGives up-to-date information with search.\"),\n            \"homepage\": \"https://aistudio.google.com\",\n            \"endpoint\": \"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:streamGenerateContent\",\n            \"model\": \"gemini-2.5-flash-preview-05-20\",\n            \"requires_key\": true,\n            \"key_id\": \"gemini\",\n            \"key_get_link\": \"https://aistudio.google.com/app/apikey\",\n            \"key_get_description\": qsTr(\"**Pricing**: free. Data used for training.\\n\\n**Instructions**: Log into Google account, allow AI Studio to create Google Cloud project or whatever it asks, go back and click Get API key\"),\n            \"api_format\": \"gemini\",\n            \"tools\": [\n                {\n                    \"google_search\": ({})\n                },\n            ]\n        },\n        \"gemini-2.5-flash-tools\": {\n            \"name\": \"Gemini 2.5 Flash (Tools)\",\n            \"icon\": \"google-gemini-symbolic\",\n            \"description\": qsTr(\"Experimental | Online | Google's model\\nCan do a little more but doesn't search quickly\"),\n            \"homepage\": \"https://aistudio.google.com\",\n            \"endpoint\": \"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:streamGenerateContent\",\n            \"model\": \"gemini-2.5-flash-preview-05-20\",\n            \"requires_key\": true,\n            \"key_id\": \"gemini\",\n            \"key_get_link\": \"https://aistudio.google.com/app/apikey\",\n            \"key_get_description\": qsTr(\"**Pricing**: free. Data used for training.\\n\\n**Instructions**: Log into Google account, allow AI Studio to create Google Cloud project or whatever it asks, go back and click Get API key\"),\n            \"api_format\": \"gemini\",\n            \"tools\": [\n                {\n                    \"functionDeclarations\": [\n                        {\n                            \"name\": \"switch_to_search_mode\",\n                            \"description\": \"Search the web\",\n                        },\n                        {\n                            \"name\": \"get_shell_config\",\n                            \"description\": \"Get the desktop shell config file contents\",\n                        },\n                        {\n                            \"name\": \"set_shell_config\",\n                            \"description\": \"Set a field in the desktop graphical shell config file. Must only be used after `get_shell_config`.\",\n                            \"parameters\": {\n                                \"type\": \"object\",\n                                \"properties\": {\n                                    \"key\": {\n                                        \"type\": \"string\",\n                                        \"description\": \"The key to set, e.g. `bar.borderless`. MUST NOT BE GUESSED, use `get_shell_config` to see what keys are available before setting.\",\n                                    },\n                                    \"value\": {\n                                        \"type\": \"string\",\n                                        \"description\": \"The value to set, e.g. `true`\"\n                                    }\n                                },\n                                \"required\": [\"key\", \"value\"]\n                            }\n                        },\n                    ]\n                }\n            ]\n        },\n        \"openrouter-llama4-maverick\": {\n            \"name\": \"Llama 4 Maverick\",\n            \"icon\": \"ollama-symbolic\",\n            \"description\": StringUtils.format(qsTr(\"Online via {0} | {1}'s model\"), \"OpenRouter\", \"Meta\"),\n            \"homepage\": \"https://openrouter.ai/meta-llama/llama-4-maverick:free\",\n            \"endpoint\": \"https://openrouter.ai/api/v1/chat/completions\",\n            \"model\": \"meta-llama/llama-4-maverick:free\",\n            \"requires_key\": true,\n            \"key_id\": \"openrouter\",\n            \"key_get_link\": \"https://openrouter.ai/settings/keys\",\n            \"key_get_description\": qsTr(\"**Pricing**: free. Data use policy varies depending on your OpenRouter account settings.\\n\\n**Instructions**: Log into OpenRouter account, go to Keys on the topright menu, click Create API Key\"),\n        },\n        \"openrouter-deepseek-r1\": {\n            \"name\": \"DeepSeek R1\",\n            \"icon\": \"deepseek-symbolic\",\n            \"description\": StringUtils.format(qsTr(\"Online via {0} | {1}'s model\"), \"OpenRouter\", \"DeepSeek\"),\n            \"homepage\": \"https://openrouter.ai/deepseek/deepseek-r1:free\",\n            \"endpoint\": \"https://openrouter.ai/api/v1/chat/completions\",\n            \"model\": \"deepseek/deepseek-r1:free\",\n            \"requires_key\": true,\n            \"key_id\": \"openrouter\",\n            \"key_get_link\": \"https://openrouter.ai/settings/keys\",\n            \"key_get_description\": qsTr(\"**Pricing**: free. Data use policy varies depending on your OpenRouter account settings.\\n\\n**Instructions**: Log into OpenRouter account, go to Keys on the topright menu, click Create API Key\"),\n        },\n    }\n    property var modelList: Object.keys(root.models)\n    property var currentModelId: PersistentStates?.ai?.model || modelList[0]\n\n    Component.onCompleted: {\n        setModel(currentModelId, false); // Do necessary setup for model\n        getOllamaModels.running = true\n    }\n\n    function guessModelLogo(model) {\n        if (model.includes(\"llama\")) return \"ollama-symbolic\";\n        if (model.includes(\"gemma\")) return \"google-gemini-symbolic\";\n        if (model.includes(\"deepseek\")) return \"deepseek-symbolic\";\n        if (/^phi\\d*:/i.test(model)) return \"microsoft-symbolic\";\n        return \"ollama-symbolic\";\n    }\n\n    function guessModelName(model) {\n        const replaced = model.replace(/-/g, ' ').replace(/:/g, ' ');\n        let words = replaced.split(' ');\n        words[words.length - 1] = words[words.length - 1].replace(/(\\d+)b$/, (_, num) => `${num}B`)\n        words = words.map((word) => {\n            return (word.charAt(0).toUpperCase() + word.slice(1))\n        });\n        if (words[words.length - 1] === \"Latest\") words.pop();\n        else words[words.length - 1] = `(${words[words.length - 1]})`; // Surround the last word with square brackets\n        const result = words.join(' ');\n        return result;\n    }\n\n    Process {\n        id: getOllamaModels\n        command: [\"bash\", \"-c\", `${Directories.config}/quickshell/scripts/ai/show-installed-ollama-models.sh`.replace(/file:\\/\\//, \"\")]\n        stdout: SplitParser {\n            onRead: data => {\n                try {\n                    if (data.length === 0) return;\n                    const dataJson = JSON.parse(data);\n                    root.modelList = [...root.modelList, ...dataJson];\n                    dataJson.forEach(model => {\n                        const safeModelName = root.safeModelName(model);\n                        root.models[safeModelName] = {\n                            \"name\": guessModelName(model),\n                            \"icon\": guessModelLogo(model),\n                            \"description\": StringUtils.format(qsTr(\"Local Ollama model | {0}\"), model),\n                            \"homepage\": `https://ollama.com/library/${model}`,\n                            \"endpoint\": \"http://localhost:11434/v1/chat/completions\",\n                            \"model\": model,\n                        }\n                    });\n\n                    root.modelList = Object.keys(root.models);\n\n                } catch (e) {\n                    console.log(\"Could not fetch Ollama models:\", e);\n                }\n            }\n        }\n    }\n\n    function addMessage(message, role) {\n        if (message.length === 0) return;\n        const aiMessage = aiMessageComponent.createObject(root, {\n            \"role\": role,\n            \"content\": message,\n            \"thinking\": false,\n            \"done\": true,\n        });\n        const id = idForMessage(aiMessage);\n        root.messageIDs = [...root.messageIDs, id];\n        root.messageByID[id] = aiMessage;\n    }\n\n    function removeMessage(index) {\n        if (index < 0 || index >= messageIDs.length) return;\n        const id = root.messageIDs[index];\n        root.messageIDs.splice(index, 1);\n        root.messageIDs = [...root.messageIDs];\n        delete root.messageByID[id];\n    }\n\n    function addApiKeyAdvice(model) {\n        root.addMessage(\n            StringUtils.format(qsTr('To set an API key, pass it with the command\\n\\nTo view the key, pass \"get\" with the command<br/>\\n\\n### For {0}:\\n\\n**Link**: {1}\\n\\n{2}'), \n                model.name, model.key_get_link, model.key_get_description ?? qsTr(\"<i>No further instruction provided</i>\")), \n            Ai.interfaceRole\n        );\n    }\n\n    function getModel() {\n        return models[currentModelId];\n    }\n\n    function setModel(modelId, feedback = true) {\n        if (!modelId) modelId = \"\"\n        modelId = modelId.toLowerCase()\n        if (modelList.indexOf(modelId) !== -1) {\n            const model = models[modelId]\n            // Fetch API keys if needed\n            if (model?.requires_key) KeyringStorage.fetchKeyringData();\n            // See if policy prevents online models\n            if (ConfigOptions.policies.ai === 2 && !model.endpoint.includes(\"localhost\")) {\n                root.addMessage(StringUtils.format(StringUtils.format(\"Online models disallowed\\n\\nControlled by `policies.ai` config option\"), model.name), root.interfaceRole);\n                return;\n            }\n            PersistentStateManager.setState(\"ai.model\", modelId);\n            if (feedback) root.addMessage(StringUtils.format(StringUtils.format(\"Model set to {0}\"), model.name), root.interfaceRole);\n            if (model.requires_key) {\n                // If key not there show advice\n                if (root.apiKeysLoaded && (!root.apiKeys[model.key_id] || root.apiKeys[model.key_id].length === 0)) {\n                    root.addApiKeyAdvice(model)\n                }\n            }\n        } else {\n            if (feedback) root.addMessage(qsTr(\"Invalid model. Supported: \\n```\\n\") + modelList.join(\"\\n```\\n```\\n\"), Ai.interfaceRole) + \"\\n```\"\n        }\n    }\n    \n    function getTemperature() {\n        return root.temperature;\n    }\n\n    function setTemperature(value) {\n        if (value == NaN || value < 0 || value > 2) {\n            root.addMessage(qsTr(\"Temperature must be between 0 and 2\"), Ai.interfaceRole);\n            return;\n        }\n        PersistentStateManager.setState(\"ai.temperature\", value);\n        root.temperature = value;\n        root.addMessage(StringUtils.format(qsTr(\"Temperature set to {0}\"), value), Ai.interfaceRole);\n    }\n\n    function setApiKey(key) {\n        const model = models[currentModelId];\n        if (!model.requires_key) {\n            root.addMessage(StringUtils.format(qsTr(\"{0} does not require an API key\"), model.name), Ai.interfaceRole);\n            return;\n        }\n        if (!key || key.length === 0) {\n            const model = models[currentModelId];\n            root.addApiKeyAdvice(model)\n            return;\n        }\n        KeyringStorage.setNestedField([\"apiKeys\", model.key_id], key.trim());\n        root.addMessage(StringUtils.format(qsTr(\"API key set for {0}\"), model.name, Ai.interfaceRole));\n    }\n\n    function printApiKey() {\n        const model = models[currentModelId];\n        if (model.requires_key) {\n            const key = root.apiKeys[model.key_id];\n            if (key) {\n                root.addMessage(StringUtils.format(qsTr(\"API key:\\n\\n```txt\\n{0}\\n```\"), key), Ai.interfaceRole);\n            } else {\n                root.addMessage(StringUtils.format(qsTr(\"No API key set for {0}\"), model.name), Ai.interfaceRole);\n            }\n        } else {\n            root.addMessage(StringUtils.format(qsTr(\"{0} does not require an API key\"), model.name), Ai.interfaceRole);\n        }\n    }\n\n    function printTemperature() {\n        root.addMessage(StringUtils.format(qsTr(\"Temperature: {0}\"), root.temperature), Ai.interfaceRole);\n    }\n\n    function clearMessages() {\n        root.messageIDs = [];\n        root.messageByID = ({});\n    }\n\n    Process {\n        id: requester\n        property var baseCommand: [\"bash\", \"-c\"]\n        property var message\n        property bool isReasoning\n        property string apiFormat: \"openai\"\n        property string geminiBuffer: \"\"\n\n        function buildGeminiEndpoint(model) {\n            // console.log(\"ENDPOINT: \" + model.endpoint + `?key=\\$\\{${root.apiKeyEnvVarName}\\}`)\n            return model.endpoint + `?key=\\$\\{${root.apiKeyEnvVarName}\\}`;\n        }\n\n        function buildOpenAIEndpoint(model) {\n            return model.endpoint;\n        }\n\n        function markDone() {\n            requester.message.done = true;\n            if (root.postResponseHook) {\n                root.postResponseHook();\n                root.postResponseHook = null; // Reset hook after use\n            }\n        }\n\n        function buildGeminiRequestData(model, messages) {\n            let baseData = {\n                \"contents\": messages.filter(message => (message.role != Ai.interfaceRole)).map(message => {\n                    const geminiApiRoleName = (message.role === \"assistant\") ? \"model\" : message.role;\n                    const usingSearch = model.tools[0].google_search != undefined                \n                    if (!usingSearch && message.functionCall != undefined && message.functionCall.length > 0) {\n                        return {\n                            \"role\": geminiApiRoleName,\n                            \"parts\": [{ \n                                functionCall: {\n                                    \"name\": message.functionName,\n                                }\n                            }]\n                        }\n                    }\n                    if (!usingSearch && message.functionResponse != undefined && message.functionResponse.length > 0) {\n                        return {\n                            \"role\": geminiApiRoleName,\n                            \"parts\": [{ \n                                functionResponse: {\n                                    \"name\": message.functionName,\n                                    \"response\": { \"content\": message.functionResponse }\n                                }\n                            }]\n                        }\n                    }\n                    return {\n                        \"role\": geminiApiRoleName,\n                        \"parts\": [{ \n                            text: message.content,\n                        }]\n                    }\n                }),\n                \"tools\": [\n                    ...model.tools,\n                ],\n                \"system_instruction\": {\n                    \"parts\": [{ text: root.systemPrompt }]\n                },\n                \"generationConfig\": {\n                    // \"temperature\": root.temperature,\n                },\n            };\n            return model.extraParams ? Object.assign({}, baseData, model.extraParams) : baseData;\n        }\n\n        function buildOpenAIRequestData(model, messages) {\n            let baseData = {\n                \"model\": model.model,\n                \"messages\": [\n                    {role: \"system\", content: root.systemPrompt},\n                    ...messages.filter(message => (message.role != Ai.interfaceRole)).map(message => {\n                        return {\n                            \"role\": message.role,\n                            \"content\": message.content,\n                        }\n                    }),\n                ],\n                \"stream\": true,\n                // \"temperature\": root.temperature,\n            };\n            return model.extraParams ? Object.assign({}, baseData, model.extraParams) : baseData;\n        }\n\n        function makeRequest() {\n            const model = models[currentModelId];\n            requester.apiFormat = model.api_format ?? \"openai\";\n\n            /* Put API key in environment variable */\n            if (model.requires_key) requester.environment[`${root.apiKeyEnvVarName}`] = root.apiKeys ? (root.apiKeys[model.key_id] ?? \"\") : \"\"\n\n            /* Build endpoint, request data */\n            const endpoint = (apiFormat === \"gemini\") ? buildGeminiEndpoint(model) : buildOpenAIEndpoint(model);\n            const messageArray = root.messageIDs.map(id => root.messageByID[id]);\n            const data = (apiFormat === \"gemini\") ? buildGeminiRequestData(model, messageArray) : buildOpenAIRequestData(model, messageArray);\n            // console.log(\"REQUEST DATA: \", JSON.stringify(data, null, 2));\n\n            let requestHeaders = {\n                \"Content-Type\": \"application/json\",\n            }\n            \n            /* Create local message object */\n            requester.message = root.aiMessageComponent.createObject(root, {\n                \"role\": \"assistant\",\n                \"model\": currentModelId,\n                \"content\": \"\",\n                \"thinking\": true,\n                \"done\": false,\n            });\n            const id = idForMessage(requester.message);\n            root.messageIDs = [...root.messageIDs, id];\n            root.messageByID[id] = requester.message;\n\n            /* Build header string for curl */ \n            let headerString = Object.entries(requestHeaders)\n                .filter(([k, v]) => v && v.length > 0)\n                .map(([k, v]) => `-H '${k}: ${v}'`)\n                .join(' ');\n\n            // console.log(\"Request headers: \", JSON.stringify(requestHeaders));\n            // console.log(\"Header string: \", headerString);\n\n            /* Create command string */\n            const requestCommandString = `curl --no-buffer \"${endpoint}\"`\n                + ` ${headerString}`\n                + ((apiFormat == \"gemini\") ? \"\" : ` -H \"Authorization: Bearer \\$\\{${root.apiKeyEnvVarName}\\}\"`)\n                + ` -d '${StringUtils.shellSingleQuoteEscape(JSON.stringify(data))}'`\n            // console.log(\"Request command: \", requestCommandString);\n            requester.command = baseCommand.concat([requestCommandString]);\n\n            /* Reset vars and make the request */\n            requester.isReasoning = false\n            requester.running = true\n        }\n\n        function parseGeminiBuffer() {\n            // console.log(\"BUFFER DATA: \", requester.geminiBuffer);\n            try {\n                if (requester.geminiBuffer.length === 0) return;\n                const dataJson = JSON.parse(requester.geminiBuffer);\n                if (!dataJson.candidates) return;\n                \n                if (dataJson.candidates[0]?.finishReason) {\n                    requester.markDone();\n                }\n                // Function call handling\n                if (dataJson.candidates[0]?.content?.parts[0]?.functionCall) {\n                    const functionCall = dataJson.candidates[0]?.content?.parts[0]?.functionCall;\n                    requester.message.functionName = functionCall.name;\n                    requester.message.functionCall = functionCall.name;\n                    requester.message.content += `\\n\\n[[ Function: ${functionCall.name}(${JSON.stringify(functionCall.args, null, 2)}) ]]\\n`;\n                    root.handleGeminiFunctionCall(functionCall.name, functionCall.args);\n                    return\n                }\n                // Normal text response\n                const responseContent = dataJson.candidates[0]?.content?.parts[0]?.text\n                requester.message.content += responseContent;\n                const annotationSources = dataJson.candidates[0]?.groundingMetadata?.groundingChunks?.map(chunk => {\n                    return {\n                        \"type\": \"url_citation\",\n                        \"text\": chunk?.web?.title,\n                        \"url\": chunk?.web?.uri,\n                    }\n                });\n                const annotations = dataJson.candidates[0]?.groundingMetadata?.groundingSupports?.map(citation => {\n                    return {\n                        \"type\": \"url_citation\",\n                        \"start_index\": citation.segment?.startIndex,\n                        \"end_index\": citation.segment?.endIndex,\n                        \"text\": citation?.segment.text,\n                        \"url\": annotationSources[citation.groundingChunkIndices[0]]?.url,\n                        \"sources\": citation.groundingChunkIndices\n                    }\n                });\n                requester.message.annotationSources = annotationSources;\n                requester.message.annotations = annotations;\n                // console.log(JSON.stringify(requester.message, null, 2));\n            } catch (e) {\n                console.log(\"[AI] Could not parse response from stream: \", e);\n                requester.message.content += requester.geminiBuffer\n            } finally {\n                requester.geminiBuffer = \"\";\n            }\n        }\n\n        function handleGeminiResponseLine(line) {\n            if (line.startsWith(\"[\")) {\n                requester.geminiBuffer += line.slice(1).trim();\n            } else if (line == \"]\") {\n                requester.geminiBuffer += line.slice(0, -1).trim();\n                parseGeminiBuffer();\n            } else if (line.startsWith(\",\")) { // end of one entry \n                parseGeminiBuffer();\n            } else {\n                requester.geminiBuffer += line.trim();\n            }\n        }\n\n        function handleOpenAIResponseLine(line) {\n            // Remove 'data: ' prefix if present and trim whitespace\n            let cleanData = line.trim();\n            if (cleanData.startsWith(\"data:\")) {\n                cleanData = cleanData.slice(5).trim();\n            }\n            // console.log(\"Clean data: \", cleanData);\n            if (!cleanData || cleanData.startsWith(\":\")) return;\n\n            if (cleanData === \"[DONE]\") {\n                requester.markDone();\n                return;\n            }\n            const dataJson = JSON.parse(cleanData);\n\n            let newContent = \"\";\n            const responseContent = dataJson.choices[0]?.delta?.content || dataJson.message?.content;\n            const responseReasoning = dataJson.choices[0]?.delta?.reasoning || dataJson.choices[0]?.delta?.reasoning_content;\n\n            if (responseContent && responseContent.length > 0) {\n                if (requester.isReasoning) {\n                    requester.isReasoning = false;\n                    requester.message.content += \"\\n\\n</think>\\n\\n\";\n                }\n                newContent = dataJson.choices[0]?.delta?.content || dataJson.message.content;\n            } else if (responseReasoning && responseReasoning.length > 0) {\n                // console.log(\"Reasoning content: \", dataJson.choices[0].delta.reasoning);\n                if (!requester.isReasoning) {\n                    requester.isReasoning = true;\n                    requester.message.content += \"\\n\\n<think>\\n\\n\";\n                } \n                newContent = dataJson.choices[0].delta.reasoning || dataJson.choices[0].delta.reasoning_content;\n            }\n\n            requester.message.content += newContent;\n\n            if (dataJson.done) {\n                requester.markDone();\n            }\n        }\n\n        stdout: SplitParser {\n            onRead: data => {\n                // console.log(\"RAW DATA: \", data);\n                if (data.length === 0) return;\n\n                // Handle response line\n                if (requester.message.thinking) requester.message.thinking = false;\n                try {\n                    if (requester.apiFormat === \"gemini\") {\n                        requester.handleGeminiResponseLine(data);\n                    }\n                    else if (requester.apiFormat === \"openai\") {\n                        requester.handleOpenAIResponseLine(data);\n                    }\n                    else {\n                        console.log(\"Unknown API format: \", requester.apiFormat);\n                        requester.message.content += data;\n                    }\n                } catch (e) {\n                    console.log(\"[AI] Could not parse response from stream: \", e);\n                    requester.message.content += data;\n                }\n            }\n        }\n\n        onExited: (exitCode, exitStatus) => {\n            if (requester.apiFormat == \"gemini\") requester.parseGeminiBuffer();\n            else requester.markDone();\n\n            try { // to parse full response into json for error handling\n                // console.log(\"Full response: \", requester.message.content + \"]\"); \n                const parsedResponse = JSON.parse(requester.message.content + \"]\");\n                requester.message.content = `\\`\\`\\`json\\n${JSON.stringify(parsedResponse, null, 2)}\\n\\`\\`\\``;\n            } catch (e) { \n                // console.log(\"[AI] Could not parse response on exit: \", e);\n            }\n\n            if (requester.message.content.includes(\"API key not valid\")) {\n                root.addApiKeyAdvice(models[requester.message.model]);\n            }\n        }\n    }\n\n    function sendUserMessage(message) {\n        if (message.length === 0) return;\n        root.addMessage(message, \"user\");\n        requester.makeRequest();\n    }\n\n    function addFunctionOutputMessage(name, output) {\n        const aiMessage = aiMessageComponent.createObject(root, {\n            \"role\": \"user\",\n            \"content\": `[[ Output of ${name} ]]`,\n            \"functionName\": name,\n            \"functionResponse\": output,\n            \"thinking\": false,\n            \"done\": true,\n            \"visibleToUser\": false,\n        });\n        // console.log(\"Adding function output message: \", JSON.stringify(aiMessage));\n        const id = idForMessage(aiMessage);\n        root.messageIDs = [...root.messageIDs, id];\n        root.messageByID[id] = aiMessage;\n    }\n\n    function buildGeminiFunctionOutput(name, output) {\n        const functionResponsePart = {\n            \"name\": name,\n            \"response\": { \"content\": output }\n        }\n        return {\n            \"role\": \"user\",\n            \"parts\": [{ \n                functionResponse: functionResponsePart,\n            }]\n        }\n    }\n\n    function handleGeminiFunctionCall(name, args) {\n        if (name === \"switch_to_search_mode\") {\n            if (root.currentModelId === \"gemini-2.5-flash-tools\") {\n                root.setModel(\"gemini-2.5-flash-search\", false);\n                root.postResponseHook = () => root.setModel(\"gemini-2.5-flash-tools\", false);\n            } else if (root.currentModelId === \"gemini-2.0-flash-tools\") {\n                root.setModel(\"gemini-2.0-flash-search\", false);\n                root.postResponseHook = () => root.setModel(\"gemini-2.0-flash-tools\", false);\n            }\n            addFunctionOutputMessage(name, qsTr(\"Switched to search mode. Continue with the user's request.\"))\n            requester.makeRequest();\n        } else if (name === \"get_shell_config\") {\n            const configJson = ObjectUtils.toPlainObject(ConfigOptions)\n            addFunctionOutputMessage(name, JSON.stringify(configJson));\n            requester.makeRequest();\n        } else if (name === \"set_shell_config\") {\n            if (!args.key || !args.value) {\n                addFunctionOutputMessage(name, qsTr(\"Invalid arguments. Must provide `key` and `value`.\"));\n                return;\n            }\n            const key = args.key;\n            const value = args.value;\n            ConfigLoader.setLiveConfigValue(key, value);\n            ConfigLoader.saveConfig();\n        }\n        else root.addMessage(qsTr(\"Unknown function call: {0}\"), \"assistant\");\n    }\n\n}\n", "path": "quickshell/services/Ai.qml", "language": "plaintext", "prefixBegin": 0, "suffixEnd": 0}}], ["fec067d3-dc8d-47c0-ae4c-644f6ca64ace", {"value": {"selectedCode": "                        Qt.openUrlExternally(\"quickshell://settings?page=ai\");", "prefix": "import \"root:/\"\nimport \"root:/services\"\nimport \"root:/modules/common\"\nimport \"root:/modules/common/widgets\"\nimport \"./aiChat/\"\nimport \"root:/modules/common/functions/fuzzysort.js\" as Fuzzy\nimport \"root:/modules/common/functions/string_utils.js\" as StringUtils\nimport \"root:/modules/common/functions/file_utils.js\" as FileUtils\nimport QtQuick\nimport QtQuick.Controls\nimport QtQuick.Layouts\nimport Qt5Compat.GraphicalEffects\nimport Quickshell.Io\nimport Quickshell\nimport Quickshell.Hyprland\n\nItem {\n    id: root\n    property var inputField: messageInputField\n    property string commandPrefix: \"/\"\n\n    property var suggestionQuery: \"\"\n    property var suggestionList: []\n\n    ModelSelector {\n        id: modelSelector\n        x: (parent.width - width) / 2\n        y: (parent.height - height) / 2\n    }\n\n    onFocusChanged: (focus) => {\n        if (focus) {\n            root.inputField.forceActiveFocus()\n        }\n    }\n\n    Keys.onPressed: (event) => {\n        messageInputField.forceActiveFocus()\n        if (event.modifiers === Qt.NoModifier) {\n            if (event.key === Qt.Key_PageUp) {\n                messageListView.contentY = Math.max(0, messageListView.contentY - messageListView.height / 2)\n                event.accepted = true\n            } else if (event.key === Qt.Key_PageDown) {\n                messageListView.contentY = Math.min(messageListView.contentHeight - messageListView.height / 2, messageListView.contentY + messageListView.height / 2)\n                event.accepted = true\n            }\n        }\n    }\n\n    property var allCommands: [\n        {\n            name: \"model\",\n            description: qsTr(\"Choose model\"),\n            execute: (args) => {\n                Ai.setModel(args[0]);\n            }\n        },\n        {\n            name: \"clear\",\n            description: qsTr(\"Clear chat history\"),\n            execute: () => {\n                Ai.clearMessages();\n            }\n        },\n        {\n            name: \"key\",\n            description: qsTr(\"Set API key\"),\n            execute: (args) => {\n                if (args[0] == \"get\") {\n                    Ai.printApiKey()\n                } else {\n                    Ai.setApiKey(args[0]);\n                }\n            }\n        },\n        {\n            name: \"temp\",\n            description: qsTr(\"Set temperature (randomness) of the model. Values range between 0 to 2 for Gemini, 0 to 1 for other models. Default is 0.5.\"),\n            execute: (args) => {\n                // console.log(args)\n                if (args.length == 0 || args[0] == \"get\") {\n                    Ai.printTemperature()\n                } else {\n                    const temp = parseFloat(args[0]);\n                    Ai.setTemperature(temp);\n                }\n            }\n        },\n        {\n            name: \"test\",\n            description: qsTr(\"Markdown test\"),\n            execute: () => {\n                Ai.addMessage(`\n<think>\nA longer think block to test revealing animation\nOwO wem ipsum dowo sit amet, consekituwet awipiscing ewit, sed do eiuwsmod tempow inwididunt ut wabowe et dowo mawa. Ut enim ad minim weniam, quis nostwud exeucitation uwuwamcow bowowis nisi ut awiquip ex ea commowo consequat. Duuis aute iwuwe dowo in wepwependewit in wowuptate velit esse ciwwum dowo eu fugiat nuwa pawiatuw. Excepteuw sint occaecat cupidatat non pwowoident, sunt in cuwpa qui officia desewunt mowit anim id est wabowum. Meouw! >w<\nMowe uwu wem ipsum!\n</think>\n## ✏️ Markdown test\n### Formatting\n\n- *Italic*, \\`Monospace\\`, **Bold**, [Link](https://example.com)\n- Arch lincox icon <img src=\"/home/<USER>/.config/quickshell/assets/icons/arch-symbolic.svg\" height=\"${Appearance.font.pixelSize.small}\"/>\n\n### Table\n\nQuickshell vs AGS/Astal\n\n|                          | Quickshell       | AGS/Astal         |\n|--------------------------|------------------|-------------------|\n| UI Toolkit               | Qt               | Gtk3/Gtk4         |\n| Language                 | QML              | Js/Ts/Lua         |\n| Reactivity               | Implied          | Needs declaration |\n| Widget placement         | Mildly difficult | More intuitive    |\n| Bluetooth & Wifi support | ❌               | ✅                |\n| No-delay keybinds        | ✅               | ❌                |\n| Development              | New APIs         | New syntax        |\n\n### Code block\n\nJust a hello world...\n\n\\`\\`\\`cpp\n#include <bits/stdc++.h>\n// This is intentionally very long to test scrolling\nconst std::string GREETING = \\\"UwU\\\";\nint main(int argc, char* argv[]) {\n    std::cout << GREETING;\n}\n\\`\\`\\`\n\n### LaTeX\n\n\nInline w/ dollar signs: $\\\\frac{1}{2} = \\\\frac{2}{4}$\n\nInline w/ double dollar signs: $$\\\\int_0^\\\\infty e^{-x^2} dx = \\\\frac{\\\\sqrt{\\\\pi}}{2}$$\n\nInline w/ backslash and square brackets \\\\[\\\\int_0^\\\\infty \\\\frac{1}{x^2} dx = \\\\infty\\\\]\n\nInline w/ backslash and round brackets \\\\(e^{i\\\\pi} + 1 = 0\\\\)\n`, \n                    Ai.interfaceRole);\n            }\n        },\n    ]\n\n    function handleInput(inputText) {\n        if (inputText.startsWith(root.commandPrefix)) {\n            // Handle special commands\n            const command = inputText.split(\" \")[0].substring(1);\n            const args = inputText.split(\" \").slice(1);\n            const commandObj = root.allCommands.find(cmd => cmd.name === `${command}`);\n            if (commandObj) {\n                commandObj.execute(args);\n            } else {\n                Ai.addMessage(qsTr(\"Unknown command: \") + command, Ai.interfaceRole);\n            }\n        }\n        else {\n            Ai.sendUserMessage(inputText);\n        }\n    }\n\n    ColumnLayout {\n        id: columnLayout\n        anchors.fill: parent\n\n        Item { // Messages\n            Layout.fillWidth: true\n            Layout.fillHeight: true\n            StyledListView { // Message list\n                id: messageListView\n                anchors.fill: parent\n                spacing: 10\n                popin: false\n\n                property int lastResponseLength: 0\n\n                clip: true\n                layer.enabled: true\n                layer.effect: OpacityMask {\n                    maskSource: Rectangle {\n                        width: swipeView.width\n                        height: swipeView.height\n                        radius: Appearance.rounding.small\n                    }\n                }\n\n                add: null // Prevent function calls from being janky\n\n                Behavior on contentY {\n                    NumberAnimation {\n                        id: scrollAnim\n                        duration: Appearance.animation.scroll.duration\n                        easing.type: Appearance.animation.scroll.type\n                        easing.bezierCurve: Appearance.animation.scroll.bezierCurve\n                    }\n                }\n\n                model: ScriptModel {\n                    values: Ai.messageIDs.filter(id => {\n                        const message = Ai.messageByID[id];\n                        return message?.visibleToUser ?? true;\n                    })\n                }\n                delegate: AiMessage {\n                    required property var modelData\n                    required property int index\n                    messageIndex: index\n                    messageData: {\n                        Ai.messageByID[modelData]\n                    }\n                    messageInputField: root.inputField\n                }\n            }\n\n            Item { // Placeholder when list is empty\n                opacity: Ai.messageIDs.length === 0 ? 1 : 0\n                visible: opacity > 0\n                anchors.fill: parent\n\n                Behavior on opacity {\n                    animation: Appearance.animation.elementMoveEnter.numberAnimation.createObject(this)\n                }\n\n                ColumnLayout {\n                    anchors.centerIn: parent\n                    spacing: 5\n\n                    MaterialSymbol {\n                        Layout.alignment: Qt.AlignHCenter\n                        iconSize: 60\n                        color: Appearance.m3colors.m3outline\n                        text: \"neurology\"\n                    }\n                    StyledText {\n                        id: widgetNameText\n                        Layout.alignment: Qt.AlignHCenter\n                        font.pixelSize: Appearance.font.pixelSize.larger\n                        font.family: Appearance.font.family.title\n                        color: Appearance.m3colors.m3outline\n                        horizontalAlignment: Text.AlignHCenter\n                        text: qsTr(\"Large language models\")\n                    }\n                    StyledText {\n                        id: widgetDescriptionText\n                        Layout.fillWidth: true\n                        font.pixelSize: Appearance.font.pixelSize.small\n                        color: Appearance.m3colors.m3outline\n                        horizontalAlignment: Text.AlignLeft\n                        wrapMode: Text.Wrap\n                        text: qsTr(\"Type /key to get started with online models\\nCtrl+O to expand the sidebar\\nCtrl+P to detach sidebar into a window\")\n                    }\n                }\n            }\n        }\n\n        Item { // Suggestion description\n            visible: descriptionText.text.length > 0\n            Layout.fillWidth: true\n            implicitHeight: descriptionBackground.implicitHeight\n\n            Rectangle {\n                id: descriptionBackground\n                color: Appearance.colors.colTooltip\n                anchors.left: parent.left\n                anchors.right: parent.right\n                anchors.verticalCenter: parent.verticalCenter\n                implicitHeight: descriptionText.implicitHeight + 5 * 2\n                radius: Appearance.rounding.verysmall\n\n                StyledText {\n                    id: descriptionText\n                    anchors.left: parent.left\n                    anchors.right: parent.right\n                    anchors.leftMargin: 10\n                    anchors.rightMargin: 10\n                    anchors.verticalCenter: parent.verticalCenter\n                    font.pixelSize: Appearance.font.pixelSize.smaller\n                    color: Appearance.colors.colOnTooltip\n                    wrapMode: Text.Wrap\n                    text: root.suggestionList[suggestions.selectedIndex]?.description ?? \"\"\n                }\n            }\n        }\n\n        FlowButtonGroup { // Suggestions\n            id: suggestions\n            visible: root.suggestionList.length > 0 && messageInputField.text.length > 0\n            property int selectedIndex: 0\n            Layout.fillWidth: true\n            spacing: 5\n\n            Repeater {\n                id: suggestionRepeater\n                model: {\n                    suggestions.selectedIndex = 0\n                    return root.suggestionList.slice(0, 10)\n                }\n                delegate: ApiCommandButton {\n                    id: commandButton\n                    colBackground: suggestions.selectedIndex === index ? Appearance.colors.colLayer2Hover : Appearance.colors.colLayer2\n                    bounce: false\n                    contentItem: StyledText {\n                        font.pixelSize: Appearance.font.pixelSize.small\n                        color: Appearance.m3colors.m3onSurface\n                        horizontalAlignment: Text.AlignHCenter\n                        text: modelData.displayName ?? modelData.name\n                    }\n\n                    onHoveredChanged: {\n                        if (commandButton.hovered) {\n                            suggestions.selectedIndex = index;\n                        }\n                    }\n                    onClicked: {\n                        suggestions.acceptSuggestion(modelData.name)\n                    }\n                }\n            }\n\n            function acceptSuggestion(word) {\n                const words = messageInputField.text.trim().split(/\\s+/);\n                if (words.length > 0) {\n                    words[words.length - 1] = word;\n                } else {\n                    words.push(word);\n                }\n                const updatedText = words.join(\" \") + \" \";\n                messageInputField.text = updatedText;\n                messageInputField.cursorPosition = messageInputField.text.length;\n                messageInputField.forceActiveFocus();\n            }\n\n            function acceptSelectedWord() {\n                if (suggestions.selectedIndex >= 0 && suggestions.selectedIndex < suggestionRepeater.count) {\n                    const word = root.suggestionList[suggestions.selectedIndex].name;\n                    suggestions.acceptSuggestion(word);\n                }\n            }\n        }\n\n        Rectangle { // Input area\n            id: inputWrapper\n            property real columnSpacing: 5\n            Layout.fillWidth: true\n            radius: Appearance.rounding.small\n            color: Appearance.colors.colLayer1\n            implicitWidth: messageInputField.implicitWidth\n            implicitHeight: Math.max(inputFieldRowLayout.implicitHeight + inputFieldRowLayout.anchors.topMargin \n                + commandButtonsRow.implicitHeight + commandButtonsRow.anchors.bottomMargin + columnSpacing, 45)\n            clip: true\n            border.color: Appearance.colors.colOutlineVariant\n            border.width: 1\n\n            Behavior on implicitHeight {\n                animation: Appearance.animation.elementMove.numberAnimation.createObject(this)\n            }\n\n            RowLayout { // Input field and send button\n                id: inputFieldRowLayout\n                anchors.top: parent.top\n                anchors.left: parent.left\n                anchors.right: parent.right\n                anchors.topMargin: 5\n                spacing: 0\n\n                StyledTextArea { // The actual TextArea\n                    id: messageInputField\n                    wrapMode: TextArea.Wrap\n                    Layout.fillWidth: true\n                    padding: 10\n                    color: activeFocus ? Appearance.m3colors.m3onSurface : Appearance.m3colors.m3onSurfaceVariant\n                    placeholderText: StringUtils.format(qsTr('Message the model... \"{0}\" for commands'), root.commandPrefix)\n\n                    background: null\n\n                    onTextChanged: { // Handle suggestions\n                        if(messageInputField.text.length === 0) {\n                            root.suggestionQuery = \"\"\n                            root.suggestionList = []\n                            return\n                        } else if(messageInputField.text.startsWith(`${root.commandPrefix}model`)) {\n                            root.suggestionQuery = messageInputField.text.split(\" \")[1] ?? \"\"\n                            const modelResults = Fuzzy.go(root.suggestionQuery, Ai.modelList.map(model => {\n                                return {\n                                    name: Fuzzy.prepare(model),\n                                    obj: model,\n                                }\n                            }), {\n                                all: true,\n                                key: \"name\"\n                            })\n                            root.suggestionList = modelResults.map(model => {\n                                return {\n                                    name: `${messageInputField.text.trim().split(\" \").length == 1 ? (root.commandPrefix + \"model \") : \"\"}${model.target}`,\n                                    displayName: `${Ai.models[model.target].name}`,\n                                    description: `${Ai.models[model.target].description}`,\n                                }\n                            })\n                        } else if(messageInputField.text.startsWith(root.commandPrefix)) {\n                            root.suggestionQuery = messageInputField.text\n                            root.suggestionList = root.allCommands.filter(cmd => cmd.name.startsWith(messageInputField.text.substring(1))).map(cmd => {\n                                return {\n                                    name: `${root.commandPrefix}${cmd.name}`,\n                                    description: `${cmd.description}`,\n                                }\n                            })\n                        }\n                    }\n\n                    function accept() {\n                        root.handleInput(text)\n                        text = \"\"\n                    }\n\n                    Keys.onPressed: (event) => {\n                        if (event.key === Qt.Key_Tab) {\n                            suggestions.acceptSelectedWord();\n                            event.accepted = true;\n                        } else if (event.key === Qt.Key_Up && suggestions.visible) {\n                            suggestions.selectedIndex = Math.max(0, suggestions.selectedIndex - 1);\n                            event.accepted = true;\n                        } else if (event.key === Qt.Key_Down && suggestions.visible) {\n                            suggestions.selectedIndex = Math.min(root.suggestionList.length - 1, suggestions.selectedIndex + 1);\n                            event.accepted = true;\n                        } else if ((event.key === Qt.Key_Enter || event.key === Qt.Key_Return)) {\n                            if (event.modifiers & Qt.ShiftModifier) {\n                                // Insert newline\n                                messageInputField.insert(messageInputField.cursorPosition, \"\\n\")\n                                event.accepted = true\n                            } else { // Accept text\n                                const inputText = messageInputField.text\n                                messageInputField.clear()\n                                root.handleInput(inputText)\n                                event.accepted = true\n                            }\n                        }\n                    }\n                }\n\n                RippleButton { // Send button\n                    id: sendButton\n                    Layout.alignment: Qt.AlignTop\n                    Layout.rightMargin: 5\n                    implicitWidth: 40\n                    implicitHeight: 40\n                    buttonRadius: Appearance.rounding.small\n                    enabled: messageInputField.text.length > 0\n                    toggled: enabled\n\n                    MouseArea {\n                        anchors.fill: parent\n                        cursorShape: sendButton.enabled ? Qt.PointingHandCursor : Qt.ArrowCursor\n                        onClicked: {\n                            const inputText = messageInputField.text\n                            root.handleInput(inputText)\n                            messageInputField.clear()\n                        }\n                    }\n\n                    contentItem: MaterialSymbol {\n                        anchors.centerIn: parent\n                        horizontalAlignment: Text.AlignHCenter\n                        iconSize: Appearance.font.pixelSize.larger\n                        // fill: sendButton.enabled ? 1 : 0\n                        color: sendButton.enabled ? Appearance.m3colors.m3onPrimary : Appearance.colors.colOnLayer2Disabled\n                        text: \"send\"\n                    }\n                }\n            }\n\n            RowLayout { // Controls\n                id: commandButtonsRow\n                anchors.left: parent.left\n                anchors.right: parent.right\n                anchors.bottom: parent.bottom\n                anchors.bottomMargin: 5\n                anchors.leftMargin: 5\n                anchors.rightMargin: 5\n                spacing: 5\n\n                property var commandsShown: [\n                    {\n                        name: \"model\",\n                        sendDirectly: false,\n                    },\n                    {\n                        name: \"clear\",\n                        sendDirectly: true,\n                    },\n                ]\n\n                RippleButton {\n                    implicitHeight: providerRowLayout.implicitHeight + 5 * 2\n                    implicitWidth: providerRowLayout.implicitWidth + 10 * 2\n                    buttonRadius: Appearance.rounding.small\n\n                    onClicked: {\n                        modelSelector.open();\n                    }\n\n                    contentItem: RowLayout {\n                        id: providerRowLayout\n                        spacing: 8\n\n                        MaterialSymbol {\n                            text: Ai.getModel()?.icon || \"smart_toy\"\n                            iconSize: Appearance.font.pixelSize.large\n                            color: Appearance.colors.colPrimary\n                        }\n\n                        ColumnLayout {\n                            spacing: 0\n\n                            StyledText {\n                                id: providerName\n                                font.pixelSize: Appearance.font.pixelSize.small\n                                font.weight: Font.Medium\n                                color: Appearance.m3colors.m3onSurface\n                                elide: Text.ElideRight\n                                text: Ai.getModel()?.name || qsTr(\"未选择模型\")\n                            }\n\n                            StyledText {\n                                font.pixelSize: Appearance.font.pixelSize.smaller\n                                color: Appearance.colors.colSubtext\n                                elide: Text.ElideRight\n                                text: {\n                                    const model = Ai.getModel();\n                                    if (!model) return \"\";\n\n                                    let info = [];\n                                    if (model.custom) info.push(qsTr(\"自定义\"));\n                                    if (model.requires_key) {\n                                        const hasKey = Ai.apiKeysLoaded && Ai.apiKeys[model.key_id];\n                                        info.push(hasKey ? qsTr(\"✓\") : qsTr(\"需要密钥\"));\n                                    }\n                                    return info.join(\" \");\n                                }\n                                visible: text.length > 0\n                            }\n                        }\n\n                        MaterialSymbol {\n                            text: \"expand_more\"\n                            iconSize: Appearance.font.pixelSize.normal\n                            color: Appearance.colors.colSubtext\n                        }\n                    }\n\n                    StyledToolTip {\n                        id: toolTip\n                        content: qsTr(\"点击选择模型\")\n                    }\n                }\n\n                Item { Layout.fillWidth: true }\n\n                AiMessageControlButton {\n                    buttonIcon: \"settings\"\n                    onClicked: {\n                        // 打开设置窗口并导航到AI页面\n", "suffix": "\n                    }\n\n                    StyledToolTip {\n                        content: qsTr(\"AI设置\")\n                    }\n                }\n\n                ButtonGroup {\n                    padding: 0\n\n                    Repeater { // Command buttons\n                        model: commandButtonsRow.commandsShown\n                        delegate: ApiCommandButton {\n                            property string commandRepresentation: `${root.commandPrefix}${modelData.name}`\n                            buttonText: commandRepresentation\n                            onClicked: {\n                                if(modelData.sendDirectly) {\n                                    root.handleInput(commandRepresentation)\n                                } else {\n                                    messageInputField.text = commandRepresentation + \" \"\n                                    messageInputField.cursorPosition = messageInputField.text.length\n                                    messageInputField.forceActiveFocus()\n                                }\n                                if (modelData.name === \"clear\") {\n                                    messageInputField.text = \"\"\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n\n        }\n        \n    }\n\n}", "path": "quickshell/modules/sidebarLeft/AiChat.qml", "language": "plaintext", "prefixBegin": 0, "suffixEnd": 1}}], ["50eba2ae-1219-4060-a978-a43e7afdbbc5", {"value": {"selectedCode": "                        Qt.openUrlExternally(\"quickshell://settings?page=ai\");", "prefix": "import \"root:/\"\nimport \"root:/services\"\nimport \"root:/modules/common\"\nimport \"root:/modules/common/widgets\"\nimport \"./aiChat/\"\nimport \"root:/modules/common/functions/fuzzysort.js\" as Fuzzy\nimport \"root:/modules/common/functions/string_utils.js\" as StringUtils\nimport \"root:/modules/common/functions/file_utils.js\" as FileUtils\nimport QtQuick\nimport QtQuick.Controls\nimport QtQuick.Layouts\nimport Qt5Compat.GraphicalEffects\nimport Quickshell.Io\nimport Quickshell\nimport Quickshell.Hyprland\n\nItem {\n    id: root\n    property var inputField: messageInputField\n    property string commandPrefix: \"/\"\n\n    property var suggestionQuery: \"\"\n    property var suggestionList: []\n\n    ModelSelector {\n        id: modelSelector\n        x: (parent.width - width) / 2\n        y: (parent.height - height) / 2\n    }\n\n    onFocusChanged: (focus) => {\n        if (focus) {\n            root.inputField.forceActiveFocus()\n        }\n    }\n\n    Keys.onPressed: (event) => {\n        messageInputField.forceActiveFocus()\n        if (event.modifiers === Qt.NoModifier) {\n            if (event.key === Qt.Key_PageUp) {\n                messageListView.contentY = Math.max(0, messageListView.contentY - messageListView.height / 2)\n                event.accepted = true\n            } else if (event.key === Qt.Key_PageDown) {\n                messageListView.contentY = Math.min(messageListView.contentHeight - messageListView.height / 2, messageListView.contentY + messageListView.height / 2)\n                event.accepted = true\n            }\n        }\n    }\n\n    property var allCommands: [\n        {\n            name: \"model\",\n            description: qsTr(\"Choose model\"),\n            execute: (args) => {\n                Ai.setModel(args[0]);\n            }\n        },\n        {\n            name: \"clear\",\n            description: qsTr(\"Clear chat history\"),\n            execute: () => {\n                Ai.clearMessages();\n            }\n        },\n        {\n            name: \"key\",\n            description: qsTr(\"Set API key\"),\n            execute: (args) => {\n                if (args[0] == \"get\") {\n                    Ai.printApiKey()\n                } else {\n                    Ai.setApiKey(args[0]);\n                }\n            }\n        },\n        {\n            name: \"temp\",\n            description: qsTr(\"Set temperature (randomness) of the model. Values range between 0 to 2 for Gemini, 0 to 1 for other models. Default is 0.5.\"),\n            execute: (args) => {\n                // console.log(args)\n                if (args.length == 0 || args[0] == \"get\") {\n                    Ai.printTemperature()\n                } else {\n                    const temp = parseFloat(args[0]);\n                    Ai.setTemperature(temp);\n                }\n            }\n        },\n        {\n            name: \"test\",\n            description: qsTr(\"Markdown test\"),\n            execute: () => {\n                Ai.addMessage(`\n<think>\nA longer think block to test revealing animation\nOwO wem ipsum dowo sit amet, consekituwet awipiscing ewit, sed do eiuwsmod tempow inwididunt ut wabowe et dowo mawa. Ut enim ad minim weniam, quis nostwud exeucitation uwuwamcow bowowis nisi ut awiquip ex ea commowo consequat. Duuis aute iwuwe dowo in wepwependewit in wowuptate velit esse ciwwum dowo eu fugiat nuwa pawiatuw. Excepteuw sint occaecat cupidatat non pwowoident, sunt in cuwpa qui officia desewunt mowit anim id est wabowum. Meouw! >w<\nMowe uwu wem ipsum!\n</think>\n## ✏️ Markdown test\n### Formatting\n\n- *Italic*, \\`Monospace\\`, **Bold**, [Link](https://example.com)\n- Arch lincox icon <img src=\"/home/<USER>/.config/quickshell/assets/icons/arch-symbolic.svg\" height=\"${Appearance.font.pixelSize.small}\"/>\n\n### Table\n\nQuickshell vs AGS/Astal\n\n|                          | Quickshell       | AGS/Astal         |\n|--------------------------|------------------|-------------------|\n| UI Toolkit               | Qt               | Gtk3/Gtk4         |\n| Language                 | QML              | Js/Ts/Lua         |\n| Reactivity               | Implied          | Needs declaration |\n| Widget placement         | Mildly difficult | More intuitive    |\n| Bluetooth & Wifi support | ❌               | ✅                |\n| No-delay keybinds        | ✅               | ❌                |\n| Development              | New APIs         | New syntax        |\n\n### Code block\n\nJust a hello world...\n\n\\`\\`\\`cpp\n#include <bits/stdc++.h>\n// This is intentionally very long to test scrolling\nconst std::string GREETING = \\\"UwU\\\";\nint main(int argc, char* argv[]) {\n    std::cout << GREETING;\n}\n\\`\\`\\`\n\n### LaTeX\n\n\nInline w/ dollar signs: $\\\\frac{1}{2} = \\\\frac{2}{4}$\n\nInline w/ double dollar signs: $$\\\\int_0^\\\\infty e^{-x^2} dx = \\\\frac{\\\\sqrt{\\\\pi}}{2}$$\n\nInline w/ backslash and square brackets \\\\[\\\\int_0^\\\\infty \\\\frac{1}{x^2} dx = \\\\infty\\\\]\n\nInline w/ backslash and round brackets \\\\(e^{i\\\\pi} + 1 = 0\\\\)\n`, \n                    Ai.interfaceRole);\n            }\n        },\n    ]\n\n    function handleInput(inputText) {\n        if (inputText.startsWith(root.commandPrefix)) {\n            // Handle special commands\n            const command = inputText.split(\" \")[0].substring(1);\n            const args = inputText.split(\" \").slice(1);\n            const commandObj = root.allCommands.find(cmd => cmd.name === `${command}`);\n            if (commandObj) {\n                commandObj.execute(args);\n            } else {\n                Ai.addMessage(qsTr(\"Unknown command: \") + command, Ai.interfaceRole);\n            }\n        }\n        else {\n            Ai.sendUserMessage(inputText);\n        }\n    }\n\n    ColumnLayout {\n        id: columnLayout\n        anchors.fill: parent\n\n        Item { // Messages\n            Layout.fillWidth: true\n            Layout.fillHeight: true\n            StyledListView { // Message list\n                id: messageListView\n                anchors.fill: parent\n                spacing: 10\n                popin: false\n\n                property int lastResponseLength: 0\n\n                clip: true\n                layer.enabled: true\n                layer.effect: OpacityMask {\n                    maskSource: Rectangle {\n                        width: swipeView.width\n                        height: swipeView.height\n                        radius: Appearance.rounding.small\n                    }\n                }\n\n                add: null // Prevent function calls from being janky\n\n                Behavior on contentY {\n                    NumberAnimation {\n                        id: scrollAnim\n                        duration: Appearance.animation.scroll.duration\n                        easing.type: Appearance.animation.scroll.type\n                        easing.bezierCurve: Appearance.animation.scroll.bezierCurve\n                    }\n                }\n\n                model: ScriptModel {\n                    values: Ai.messageIDs.filter(id => {\n                        const message = Ai.messageByID[id];\n                        return message?.visibleToUser ?? true;\n                    })\n                }\n                delegate: AiMessage {\n                    required property var modelData\n                    required property int index\n                    messageIndex: index\n                    messageData: {\n                        Ai.messageByID[modelData]\n                    }\n                    messageInputField: root.inputField\n                }\n            }\n\n            Item { // Placeholder when list is empty\n                opacity: Ai.messageIDs.length === 0 ? 1 : 0\n                visible: opacity > 0\n                anchors.fill: parent\n\n                Behavior on opacity {\n                    animation: Appearance.animation.elementMoveEnter.numberAnimation.createObject(this)\n                }\n\n                ColumnLayout {\n                    anchors.centerIn: parent\n                    spacing: 5\n\n                    MaterialSymbol {\n                        Layout.alignment: Qt.AlignHCenter\n                        iconSize: 60\n                        color: Appearance.m3colors.m3outline\n                        text: \"neurology\"\n                    }\n                    StyledText {\n                        id: widgetNameText\n                        Layout.alignment: Qt.AlignHCenter\n                        font.pixelSize: Appearance.font.pixelSize.larger\n                        font.family: Appearance.font.family.title\n                        color: Appearance.m3colors.m3outline\n                        horizontalAlignment: Text.AlignHCenter\n                        text: qsTr(\"Large language models\")\n                    }\n                    StyledText {\n                        id: widgetDescriptionText\n                        Layout.fillWidth: true\n                        font.pixelSize: Appearance.font.pixelSize.small\n                        color: Appearance.m3colors.m3outline\n                        horizontalAlignment: Text.AlignLeft\n                        wrapMode: Text.Wrap\n                        text: qsTr(\"Type /key to get started with online models\\nCtrl+O to expand the sidebar\\nCtrl+P to detach sidebar into a window\")\n                    }\n                }\n            }\n        }\n\n        Item { // Suggestion description\n            visible: descriptionText.text.length > 0\n            Layout.fillWidth: true\n            implicitHeight: descriptionBackground.implicitHeight\n\n            Rectangle {\n                id: descriptionBackground\n                color: Appearance.colors.colTooltip\n                anchors.left: parent.left\n                anchors.right: parent.right\n                anchors.verticalCenter: parent.verticalCenter\n                implicitHeight: descriptionText.implicitHeight + 5 * 2\n                radius: Appearance.rounding.verysmall\n\n                StyledText {\n                    id: descriptionText\n                    anchors.left: parent.left\n                    anchors.right: parent.right\n                    anchors.leftMargin: 10\n                    anchors.rightMargin: 10\n                    anchors.verticalCenter: parent.verticalCenter\n                    font.pixelSize: Appearance.font.pixelSize.smaller\n                    color: Appearance.colors.colOnTooltip\n                    wrapMode: Text.Wrap\n                    text: root.suggestionList[suggestions.selectedIndex]?.description ?? \"\"\n                }\n            }\n        }\n\n        FlowButtonGroup { // Suggestions\n            id: suggestions\n            visible: root.suggestionList.length > 0 && messageInputField.text.length > 0\n            property int selectedIndex: 0\n            Layout.fillWidth: true\n            spacing: 5\n\n            Repeater {\n                id: suggestionRepeater\n                model: {\n                    suggestions.selectedIndex = 0\n                    return root.suggestionList.slice(0, 10)\n                }\n                delegate: ApiCommandButton {\n                    id: commandButton\n                    colBackground: suggestions.selectedIndex === index ? Appearance.colors.colLayer2Hover : Appearance.colors.colLayer2\n                    bounce: false\n                    contentItem: StyledText {\n                        font.pixelSize: Appearance.font.pixelSize.small\n                        color: Appearance.m3colors.m3onSurface\n                        horizontalAlignment: Text.AlignHCenter\n                        text: modelData.displayName ?? modelData.name\n                    }\n\n                    onHoveredChanged: {\n                        if (commandButton.hovered) {\n                            suggestions.selectedIndex = index;\n                        }\n                    }\n                    onClicked: {\n                        suggestions.acceptSuggestion(modelData.name)\n                    }\n                }\n            }\n\n            function acceptSuggestion(word) {\n                const words = messageInputField.text.trim().split(/\\s+/);\n                if (words.length > 0) {\n                    words[words.length - 1] = word;\n                } else {\n                    words.push(word);\n                }\n                const updatedText = words.join(\" \") + \" \";\n                messageInputField.text = updatedText;\n                messageInputField.cursorPosition = messageInputField.text.length;\n                messageInputField.forceActiveFocus();\n            }\n\n            function acceptSelectedWord() {\n                if (suggestions.selectedIndex >= 0 && suggestions.selectedIndex < suggestionRepeater.count) {\n                    const word = root.suggestionList[suggestions.selectedIndex].name;\n                    suggestions.acceptSuggestion(word);\n                }\n            }\n        }\n\n        Rectangle { // Input area\n            id: inputWrapper\n            property real columnSpacing: 5\n            Layout.fillWidth: true\n            radius: Appearance.rounding.small\n            color: Appearance.colors.colLayer1\n            implicitWidth: messageInputField.implicitWidth\n            implicitHeight: Math.max(inputFieldRowLayout.implicitHeight + inputFieldRowLayout.anchors.topMargin \n                + commandButtonsRow.implicitHeight + commandButtonsRow.anchors.bottomMargin + columnSpacing, 45)\n            clip: true\n            border.color: Appearance.colors.colOutlineVariant\n            border.width: 1\n\n            Behavior on implicitHeight {\n                animation: Appearance.animation.elementMove.numberAnimation.createObject(this)\n            }\n\n            RowLayout { // Input field and send button\n                id: inputFieldRowLayout\n                anchors.top: parent.top\n                anchors.left: parent.left\n                anchors.right: parent.right\n                anchors.topMargin: 5\n                spacing: 0\n\n                StyledTextArea { // The actual TextArea\n                    id: messageInputField\n                    wrapMode: TextArea.Wrap\n                    Layout.fillWidth: true\n                    padding: 10\n                    color: activeFocus ? Appearance.m3colors.m3onSurface : Appearance.m3colors.m3onSurfaceVariant\n                    placeholderText: StringUtils.format(qsTr('Message the model... \"{0}\" for commands'), root.commandPrefix)\n\n                    background: null\n\n                    onTextChanged: { // Handle suggestions\n                        if(messageInputField.text.length === 0) {\n                            root.suggestionQuery = \"\"\n                            root.suggestionList = []\n                            return\n                        } else if(messageInputField.text.startsWith(`${root.commandPrefix}model`)) {\n                            root.suggestionQuery = messageInputField.text.split(\" \")[1] ?? \"\"\n                            const modelResults = Fuzzy.go(root.suggestionQuery, Ai.modelList.map(model => {\n                                return {\n                                    name: Fuzzy.prepare(model),\n                                    obj: model,\n                                }\n                            }), {\n                                all: true,\n                                key: \"name\"\n                            })\n                            root.suggestionList = modelResults.map(model => {\n                                return {\n                                    name: `${messageInputField.text.trim().split(\" \").length == 1 ? (root.commandPrefix + \"model \") : \"\"}${model.target}`,\n                                    displayName: `${Ai.models[model.target].name}`,\n                                    description: `${Ai.models[model.target].description}`,\n                                }\n                            })\n                        } else if(messageInputField.text.startsWith(root.commandPrefix)) {\n                            root.suggestionQuery = messageInputField.text\n                            root.suggestionList = root.allCommands.filter(cmd => cmd.name.startsWith(messageInputField.text.substring(1))).map(cmd => {\n                                return {\n                                    name: `${root.commandPrefix}${cmd.name}`,\n                                    description: `${cmd.description}`,\n                                }\n                            })\n                        }\n                    }\n\n                    function accept() {\n                        root.handleInput(text)\n                        text = \"\"\n                    }\n\n                    Keys.onPressed: (event) => {\n                        if (event.key === Qt.Key_Tab) {\n                            suggestions.acceptSelectedWord();\n                            event.accepted = true;\n                        } else if (event.key === Qt.Key_Up && suggestions.visible) {\n                            suggestions.selectedIndex = Math.max(0, suggestions.selectedIndex - 1);\n                            event.accepted = true;\n                        } else if (event.key === Qt.Key_Down && suggestions.visible) {\n                            suggestions.selectedIndex = Math.min(root.suggestionList.length - 1, suggestions.selectedIndex + 1);\n                            event.accepted = true;\n                        } else if ((event.key === Qt.Key_Enter || event.key === Qt.Key_Return)) {\n                            if (event.modifiers & Qt.ShiftModifier) {\n                                // Insert newline\n                                messageInputField.insert(messageInputField.cursorPosition, \"\\n\")\n                                event.accepted = true\n                            } else { // Accept text\n                                const inputText = messageInputField.text\n                                messageInputField.clear()\n                                root.handleInput(inputText)\n                                event.accepted = true\n                            }\n                        }\n                    }\n                }\n\n                RippleButton { // Send button\n                    id: sendButton\n                    Layout.alignment: Qt.AlignTop\n                    Layout.rightMargin: 5\n                    implicitWidth: 40\n                    implicitHeight: 40\n                    buttonRadius: Appearance.rounding.small\n                    enabled: messageInputField.text.length > 0\n                    toggled: enabled\n\n                    MouseArea {\n                        anchors.fill: parent\n                        cursorShape: sendButton.enabled ? Qt.PointingHandCursor : Qt.ArrowCursor\n                        onClicked: {\n                            const inputText = messageInputField.text\n                            root.handleInput(inputText)\n                            messageInputField.clear()\n                        }\n                    }\n\n                    contentItem: MaterialSymbol {\n                        anchors.centerIn: parent\n                        horizontalAlignment: Text.AlignHCenter\n                        iconSize: Appearance.font.pixelSize.larger\n                        // fill: sendButton.enabled ? 1 : 0\n                        color: sendButton.enabled ? Appearance.m3colors.m3onPrimary : Appearance.colors.colOnLayer2Disabled\n                        text: \"send\"\n                    }\n                }\n            }\n\n            RowLayout { // Controls\n                id: commandButtonsRow\n                anchors.left: parent.left\n                anchors.right: parent.right\n                anchors.bottom: parent.bottom\n                anchors.bottomMargin: 5\n                anchors.leftMargin: 5\n                anchors.rightMargin: 5\n                spacing: 5\n\n                property var commandsShown: [\n                    {\n                        name: \"model\",\n                        sendDirectly: false,\n                    },\n                    {\n                        name: \"clear\",\n                        sendDirectly: true,\n                    },\n                ]\n\n                RippleButton {\n                    implicitHeight: providerRowLayout.implicitHeight + 5 * 2\n                    implicitWidth: providerRowLayout.implicitWidth + 10 * 2\n                    buttonRadius: Appearance.rounding.small\n\n                    onClicked: {\n                        modelSelector.open();\n                    }\n\n                    contentItem: RowLayout {\n                        id: providerRowLayout\n                        spacing: 8\n\n                        MaterialSymbol {\n                            text: Ai.getModel()?.icon || \"smart_toy\"\n                            iconSize: Appearance.font.pixelSize.large\n                            color: Appearance.colors.colPrimary\n                        }\n\n                        ColumnLayout {\n                            spacing: 0\n\n                            StyledText {\n                                id: providerName\n                                font.pixelSize: Appearance.font.pixelSize.small\n                                font.weight: Font.Medium\n                                color: Appearance.m3colors.m3onSurface\n                                elide: Text.ElideRight\n                                text: Ai.getModel()?.name || qsTr(\"未选择模型\")\n                            }\n\n                            StyledText {\n                                font.pixelSize: Appearance.font.pixelSize.smaller\n                                color: Appearance.colors.colSubtext\n                                elide: Text.ElideRight\n                                text: {\n                                    const model = Ai.getModel();\n                                    if (!model) return \"\";\n\n                                    let info = [];\n                                    if (model.custom) info.push(qsTr(\"自定义\"));\n                                    if (model.requires_key) {\n                                        const hasKey = Ai.apiKeysLoaded && Ai.apiKeys[model.key_id];\n                                        info.push(hasKey ? qsTr(\"✓\") : qsTr(\"需要密钥\"));\n                                    }\n                                    return info.join(\" \");\n                                }\n                                visible: text.length > 0\n                            }\n                        }\n\n                        MaterialSymbol {\n                            text: \"expand_more\"\n                            iconSize: Appearance.font.pixelSize.normal\n                            color: Appearance.colors.colSubtext\n                        }\n                    }\n\n                    StyledToolTip {\n                        id: toolTip\n                        content: qsTr(\"点击选择模型\")\n                    }\n                }\n\n                Item { Layout.fillWidth: true }\n\n                AiMessageControlButton {\n                    buttonIcon: \"settings\"\n                    onClicked: {\n                        // 打开设置窗口并导航到AI页面\n", "suffix": "\n                    }\n\n                    StyledToolTip {\n                        content: qsTr(\"AI设置\")\n                    }\n                }\n\n                ButtonGroup {\n                    padding: 0\n\n                    Repeater { // Command buttons\n                        model: commandButtonsRow.commandsShown\n                        delegate: ApiCommandButton {\n                            property string commandRepresentation: `${root.commandPrefix}${modelData.name}`\n                            buttonText: commandRepresentation\n                            onClicked: {\n                                if(modelData.sendDirectly) {\n                                    root.handleInput(commandRepresentation)\n                                } else {\n                                    messageInputField.text = commandRepresentation + \" \"\n                                    messageInputField.cursorPosition = messageInputField.text.length\n                                    messageInputField.forceActiveFocus()\n                                }\n                                if (modelData.name === \"clear\") {\n                                    messageInputField.text = \"\"\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n\n        }\n        \n    }\n\n}", "path": "quickshell/modules/sidebarLeft/AiChat.qml", "language": "plaintext", "prefixBegin": 0, "suffixEnd": 1}}], ["90053018-95be-4896-ba8b-46cdeddb6f17", {"value": {"selectedCode": "                        const settingsPath = FileUtils.trimFileProtocol(`${Directories.config}/quickshell/settings.qml`);\n                        Quickshell.execDetached([\"qs\", \"-p\", settingsPath]);", "prefix": "import \"root:/\"\nimport \"root:/services\"\nimport \"root:/modules/common\"\nimport \"root:/modules/common/widgets\"\nimport \"./aiChat/\"\nimport \"root:/modules/common/functions/fuzzysort.js\" as Fuzzy\nimport \"root:/modules/common/functions/string_utils.js\" as StringUtils\nimport \"root:/modules/common/functions/file_utils.js\" as FileUtils\nimport QtQuick\nimport QtQuick.Controls\nimport QtQuick.Layouts\nimport Qt5Compat.GraphicalEffects\nimport Quickshell.Io\nimport Quickshell\nimport Quickshell.Hyprland\n\nItem {\n    id: root\n    property var inputField: messageInputField\n    property string commandPrefix: \"/\"\n\n    property var suggestionQuery: \"\"\n    property var suggestionList: []\n\n    ModelSelector {\n        id: modelSelector\n        x: (parent.width - width) / 2\n        y: (parent.height - height) / 2\n    }\n\n    onFocusChanged: (focus) => {\n        if (focus) {\n            root.inputField.forceActiveFocus()\n        }\n    }\n\n    Keys.onPressed: (event) => {\n        messageInputField.forceActiveFocus()\n        if (event.modifiers === Qt.NoModifier) {\n            if (event.key === Qt.Key_PageUp) {\n                messageListView.contentY = Math.max(0, messageListView.contentY - messageListView.height / 2)\n                event.accepted = true\n            } else if (event.key === Qt.Key_PageDown) {\n                messageListView.contentY = Math.min(messageListView.contentHeight - messageListView.height / 2, messageListView.contentY + messageListView.height / 2)\n                event.accepted = true\n            }\n        }\n    }\n\n    property var allCommands: [\n        {\n            name: \"model\",\n            description: qsTr(\"Choose model\"),\n            execute: (args) => {\n                Ai.setModel(args[0]);\n            }\n        },\n        {\n            name: \"clear\",\n            description: qsTr(\"Clear chat history\"),\n            execute: () => {\n                Ai.clearMessages();\n            }\n        },\n        {\n            name: \"key\",\n            description: qsTr(\"Set API key\"),\n            execute: (args) => {\n                if (args[0] == \"get\") {\n                    Ai.printApiKey()\n                } else {\n                    Ai.setApiKey(args[0]);\n                }\n            }\n        },\n        {\n            name: \"temp\",\n            description: qsTr(\"Set temperature (randomness) of the model. Values range between 0 to 2 for Gemini, 0 to 1 for other models. Default is 0.5.\"),\n            execute: (args) => {\n                // console.log(args)\n                if (args.length == 0 || args[0] == \"get\") {\n                    Ai.printTemperature()\n                } else {\n                    const temp = parseFloat(args[0]);\n                    Ai.setTemperature(temp);\n                }\n            }\n        },\n        {\n            name: \"test\",\n            description: qsTr(\"Markdown test\"),\n            execute: () => {\n                Ai.addMessage(`\n<think>\nA longer think block to test revealing animation\nOwO wem ipsum dowo sit amet, consekituwet awipiscing ewit, sed do eiuwsmod tempow inwididunt ut wabowe et dowo mawa. Ut enim ad minim weniam, quis nostwud exeucitation uwuwamcow bowowis nisi ut awiquip ex ea commowo consequat. Duuis aute iwuwe dowo in wepwependewit in wowuptate velit esse ciwwum dowo eu fugiat nuwa pawiatuw. Excepteuw sint occaecat cupidatat non pwowoident, sunt in cuwpa qui officia desewunt mowit anim id est wabowum. Meouw! >w<\nMowe uwu wem ipsum!\n</think>\n## ✏️ Markdown test\n### Formatting\n\n- *Italic*, \\`Monospace\\`, **Bold**, [Link](https://example.com)\n- Arch lincox icon <img src=\"/home/<USER>/.config/quickshell/assets/icons/arch-symbolic.svg\" height=\"${Appearance.font.pixelSize.small}\"/>\n\n### Table\n\nQuickshell vs AGS/Astal\n\n|                          | Quickshell       | AGS/Astal         |\n|--------------------------|------------------|-------------------|\n| UI Toolkit               | Qt               | Gtk3/Gtk4         |\n| Language                 | QML              | Js/Ts/Lua         |\n| Reactivity               | Implied          | Needs declaration |\n| Widget placement         | Mildly difficult | More intuitive    |\n| Bluetooth & Wifi support | ❌               | ✅                |\n| No-delay keybinds        | ✅               | ❌                |\n| Development              | New APIs         | New syntax        |\n\n### Code block\n\nJust a hello world...\n\n\\`\\`\\`cpp\n#include <bits/stdc++.h>\n// This is intentionally very long to test scrolling\nconst std::string GREETING = \\\"UwU\\\";\nint main(int argc, char* argv[]) {\n    std::cout << GREETING;\n}\n\\`\\`\\`\n\n### LaTeX\n\n\nInline w/ dollar signs: $\\\\frac{1}{2} = \\\\frac{2}{4}$\n\nInline w/ double dollar signs: $$\\\\int_0^\\\\infty e^{-x^2} dx = \\\\frac{\\\\sqrt{\\\\pi}}{2}$$\n\nInline w/ backslash and square brackets \\\\[\\\\int_0^\\\\infty \\\\frac{1}{x^2} dx = \\\\infty\\\\]\n\nInline w/ backslash and round brackets \\\\(e^{i\\\\pi} + 1 = 0\\\\)\n`, \n                    Ai.interfaceRole);\n            }\n        },\n    ]\n\n    function handleInput(inputText) {\n        if (inputText.startsWith(root.commandPrefix)) {\n            // Handle special commands\n            const command = inputText.split(\" \")[0].substring(1);\n            const args = inputText.split(\" \").slice(1);\n            const commandObj = root.allCommands.find(cmd => cmd.name === `${command}`);\n            if (commandObj) {\n                commandObj.execute(args);\n            } else {\n                Ai.addMessage(qsTr(\"Unknown command: \") + command, Ai.interfaceRole);\n            }\n        }\n        else {\n            Ai.sendUserMessage(inputText);\n        }\n    }\n\n    ColumnLayout {\n        id: columnLayout\n        anchors.fill: parent\n\n        Item { // Messages\n            Layout.fillWidth: true\n            Layout.fillHeight: true\n            StyledListView { // Message list\n                id: messageListView\n                anchors.fill: parent\n                spacing: 10\n                popin: false\n\n                property int lastResponseLength: 0\n\n                clip: true\n                layer.enabled: true\n                layer.effect: OpacityMask {\n                    maskSource: Rectangle {\n                        width: swipeView.width\n                        height: swipeView.height\n                        radius: Appearance.rounding.small\n                    }\n                }\n\n                add: null // Prevent function calls from being janky\n\n                Behavior on contentY {\n                    NumberAnimation {\n                        id: scrollAnim\n                        duration: Appearance.animation.scroll.duration\n                        easing.type: Appearance.animation.scroll.type\n                        easing.bezierCurve: Appearance.animation.scroll.bezierCurve\n                    }\n                }\n\n                model: ScriptModel {\n                    values: Ai.messageIDs.filter(id => {\n                        const message = Ai.messageByID[id];\n                        return message?.visibleToUser ?? true;\n                    })\n                }\n                delegate: AiMessage {\n                    required property var modelData\n                    required property int index\n                    messageIndex: index\n                    messageData: {\n                        Ai.messageByID[modelData]\n                    }\n                    messageInputField: root.inputField\n                }\n            }\n\n            Item { // Placeholder when list is empty\n                opacity: Ai.messageIDs.length === 0 ? 1 : 0\n                visible: opacity > 0\n                anchors.fill: parent\n\n                Behavior on opacity {\n                    animation: Appearance.animation.elementMoveEnter.numberAnimation.createObject(this)\n                }\n\n                ColumnLayout {\n                    anchors.centerIn: parent\n                    spacing: 5\n\n                    MaterialSymbol {\n                        Layout.alignment: Qt.AlignHCenter\n                        iconSize: 60\n                        color: Appearance.m3colors.m3outline\n                        text: \"neurology\"\n                    }\n                    StyledText {\n                        id: widgetNameText\n                        Layout.alignment: Qt.AlignHCenter\n                        font.pixelSize: Appearance.font.pixelSize.larger\n                        font.family: Appearance.font.family.title\n                        color: Appearance.m3colors.m3outline\n                        horizontalAlignment: Text.AlignHCenter\n                        text: qsTr(\"Large language models\")\n                    }\n                    StyledText {\n                        id: widgetDescriptionText\n                        Layout.fillWidth: true\n                        font.pixelSize: Appearance.font.pixelSize.small\n                        color: Appearance.m3colors.m3outline\n                        horizontalAlignment: Text.AlignLeft\n                        wrapMode: Text.Wrap\n                        text: qsTr(\"Type /key to get started with online models\\nCtrl+O to expand the sidebar\\nCtrl+P to detach sidebar into a window\")\n                    }\n                }\n            }\n        }\n\n        Item { // Suggestion description\n            visible: descriptionText.text.length > 0\n            Layout.fillWidth: true\n            implicitHeight: descriptionBackground.implicitHeight\n\n            Rectangle {\n                id: descriptionBackground\n                color: Appearance.colors.colTooltip\n                anchors.left: parent.left\n                anchors.right: parent.right\n                anchors.verticalCenter: parent.verticalCenter\n                implicitHeight: descriptionText.implicitHeight + 5 * 2\n                radius: Appearance.rounding.verysmall\n\n                StyledText {\n                    id: descriptionText\n                    anchors.left: parent.left\n                    anchors.right: parent.right\n                    anchors.leftMargin: 10\n                    anchors.rightMargin: 10\n                    anchors.verticalCenter: parent.verticalCenter\n                    font.pixelSize: Appearance.font.pixelSize.smaller\n                    color: Appearance.colors.colOnTooltip\n                    wrapMode: Text.Wrap\n                    text: root.suggestionList[suggestions.selectedIndex]?.description ?? \"\"\n                }\n            }\n        }\n\n        FlowButtonGroup { // Suggestions\n            id: suggestions\n            visible: root.suggestionList.length > 0 && messageInputField.text.length > 0\n            property int selectedIndex: 0\n            Layout.fillWidth: true\n            spacing: 5\n\n            Repeater {\n                id: suggestionRepeater\n                model: {\n                    suggestions.selectedIndex = 0\n                    return root.suggestionList.slice(0, 10)\n                }\n                delegate: ApiCommandButton {\n                    id: commandButton\n                    colBackground: suggestions.selectedIndex === index ? Appearance.colors.colLayer2Hover : Appearance.colors.colLayer2\n                    bounce: false\n                    contentItem: StyledText {\n                        font.pixelSize: Appearance.font.pixelSize.small\n                        color: Appearance.m3colors.m3onSurface\n                        horizontalAlignment: Text.AlignHCenter\n                        text: modelData.displayName ?? modelData.name\n                    }\n\n                    onHoveredChanged: {\n                        if (commandButton.hovered) {\n                            suggestions.selectedIndex = index;\n                        }\n                    }\n                    onClicked: {\n                        suggestions.acceptSuggestion(modelData.name)\n                    }\n                }\n            }\n\n            function acceptSuggestion(word) {\n                const words = messageInputField.text.trim().split(/\\s+/);\n                if (words.length > 0) {\n                    words[words.length - 1] = word;\n                } else {\n                    words.push(word);\n                }\n                const updatedText = words.join(\" \") + \" \";\n                messageInputField.text = updatedText;\n                messageInputField.cursorPosition = messageInputField.text.length;\n                messageInputField.forceActiveFocus();\n            }\n\n            function acceptSelectedWord() {\n                if (suggestions.selectedIndex >= 0 && suggestions.selectedIndex < suggestionRepeater.count) {\n                    const word = root.suggestionList[suggestions.selectedIndex].name;\n                    suggestions.acceptSuggestion(word);\n                }\n            }\n        }\n\n        Rectangle { // Input area\n            id: inputWrapper\n            property real columnSpacing: 5\n            Layout.fillWidth: true\n            radius: Appearance.rounding.small\n            color: Appearance.colors.colLayer1\n            implicitWidth: messageInputField.implicitWidth\n            implicitHeight: Math.max(inputFieldRowLayout.implicitHeight + inputFieldRowLayout.anchors.topMargin \n                + commandButtonsRow.implicitHeight + commandButtonsRow.anchors.bottomMargin + columnSpacing, 45)\n            clip: true\n            border.color: Appearance.colors.colOutlineVariant\n            border.width: 1\n\n            Behavior on implicitHeight {\n                animation: Appearance.animation.elementMove.numberAnimation.createObject(this)\n            }\n\n            RowLayout { // Input field and send button\n                id: inputFieldRowLayout\n                anchors.top: parent.top\n                anchors.left: parent.left\n                anchors.right: parent.right\n                anchors.topMargin: 5\n                spacing: 0\n\n                StyledTextArea { // The actual TextArea\n                    id: messageInputField\n                    wrapMode: TextArea.Wrap\n                    Layout.fillWidth: true\n                    padding: 10\n                    color: activeFocus ? Appearance.m3colors.m3onSurface : Appearance.m3colors.m3onSurfaceVariant\n                    placeholderText: StringUtils.format(qsTr('Message the model... \"{0}\" for commands'), root.commandPrefix)\n\n                    background: null\n\n                    onTextChanged: { // Handle suggestions\n                        if(messageInputField.text.length === 0) {\n                            root.suggestionQuery = \"\"\n                            root.suggestionList = []\n                            return\n                        } else if(messageInputField.text.startsWith(`${root.commandPrefix}model`)) {\n                            root.suggestionQuery = messageInputField.text.split(\" \")[1] ?? \"\"\n                            const modelResults = Fuzzy.go(root.suggestionQuery, Ai.modelList.map(model => {\n                                return {\n                                    name: Fuzzy.prepare(model),\n                                    obj: model,\n                                }\n                            }), {\n                                all: true,\n                                key: \"name\"\n                            })\n                            root.suggestionList = modelResults.map(model => {\n                                return {\n                                    name: `${messageInputField.text.trim().split(\" \").length == 1 ? (root.commandPrefix + \"model \") : \"\"}${model.target}`,\n                                    displayName: `${Ai.models[model.target].name}`,\n                                    description: `${Ai.models[model.target].description}`,\n                                }\n                            })\n                        } else if(messageInputField.text.startsWith(root.commandPrefix)) {\n                            root.suggestionQuery = messageInputField.text\n                            root.suggestionList = root.allCommands.filter(cmd => cmd.name.startsWith(messageInputField.text.substring(1))).map(cmd => {\n                                return {\n                                    name: `${root.commandPrefix}${cmd.name}`,\n                                    description: `${cmd.description}`,\n                                }\n                            })\n                        }\n                    }\n\n                    function accept() {\n                        root.handleInput(text)\n                        text = \"\"\n                    }\n\n                    Keys.onPressed: (event) => {\n                        if (event.key === Qt.Key_Tab) {\n                            suggestions.acceptSelectedWord();\n                            event.accepted = true;\n                        } else if (event.key === Qt.Key_Up && suggestions.visible) {\n                            suggestions.selectedIndex = Math.max(0, suggestions.selectedIndex - 1);\n                            event.accepted = true;\n                        } else if (event.key === Qt.Key_Down && suggestions.visible) {\n                            suggestions.selectedIndex = Math.min(root.suggestionList.length - 1, suggestions.selectedIndex + 1);\n                            event.accepted = true;\n                        } else if ((event.key === Qt.Key_Enter || event.key === Qt.Key_Return)) {\n                            if (event.modifiers & Qt.ShiftModifier) {\n                                // Insert newline\n                                messageInputField.insert(messageInputField.cursorPosition, \"\\n\")\n                                event.accepted = true\n                            } else { // Accept text\n                                const inputText = messageInputField.text\n                                messageInputField.clear()\n                                root.handleInput(inputText)\n                                event.accepted = true\n                            }\n                        }\n                    }\n                }\n\n                RippleButton { // Send button\n                    id: sendButton\n                    Layout.alignment: Qt.AlignTop\n                    Layout.rightMargin: 5\n                    implicitWidth: 40\n                    implicitHeight: 40\n                    buttonRadius: Appearance.rounding.small\n                    enabled: messageInputField.text.length > 0\n                    toggled: enabled\n\n                    MouseArea {\n                        anchors.fill: parent\n                        cursorShape: sendButton.enabled ? Qt.PointingHandCursor : Qt.ArrowCursor\n                        onClicked: {\n                            const inputText = messageInputField.text\n                            root.handleInput(inputText)\n                            messageInputField.clear()\n                        }\n                    }\n\n                    contentItem: MaterialSymbol {\n                        anchors.centerIn: parent\n                        horizontalAlignment: Text.AlignHCenter\n                        iconSize: Appearance.font.pixelSize.larger\n                        // fill: sendButton.enabled ? 1 : 0\n                        color: sendButton.enabled ? Appearance.m3colors.m3onPrimary : Appearance.colors.colOnLayer2Disabled\n                        text: \"send\"\n                    }\n                }\n            }\n\n            RowLayout { // Controls\n                id: commandButtonsRow\n                anchors.left: parent.left\n                anchors.right: parent.right\n                anchors.bottom: parent.bottom\n                anchors.bottomMargin: 5\n                anchors.leftMargin: 5\n                anchors.rightMargin: 5\n                spacing: 5\n\n                property var commandsShown: [\n                    {\n                        name: \"model\",\n                        sendDirectly: false,\n                    },\n                    {\n                        name: \"clear\",\n                        sendDirectly: true,\n                    },\n                ]\n\n                RippleButton {\n                    implicitHeight: providerRowLayout.implicitHeight + 5 * 2\n                    implicitWidth: providerRowLayout.implicitWidth + 10 * 2\n                    buttonRadius: Appearance.rounding.small\n\n                    onClicked: {\n                        modelSelector.open();\n                    }\n\n                    contentItem: RowLayout {\n                        id: providerRowLayout\n                        spacing: 8\n\n                        MaterialSymbol {\n                            text: Ai.getModel()?.icon || \"smart_toy\"\n                            iconSize: Appearance.font.pixelSize.large\n                            color: Appearance.colors.colPrimary\n                        }\n\n                        ColumnLayout {\n                            spacing: 0\n\n                            StyledText {\n                                id: providerName\n                                font.pixelSize: Appearance.font.pixelSize.small\n                                font.weight: Font.Medium\n                                color: Appearance.m3colors.m3onSurface\n                                elide: Text.ElideRight\n                                text: Ai.getModel()?.name || qsTr(\"未选择模型\")\n                            }\n\n                            StyledText {\n                                font.pixelSize: Appearance.font.pixelSize.smaller\n                                color: Appearance.colors.colSubtext\n                                elide: Text.ElideRight\n                                text: {\n                                    const model = Ai.getModel();\n                                    if (!model) return \"\";\n\n                                    let info = [];\n                                    if (model.custom) info.push(qsTr(\"自定义\"));\n                                    if (model.requires_key) {\n                                        const hasKey = Ai.apiKeysLoaded && Ai.apiKeys[model.key_id];\n                                        info.push(hasKey ? qsTr(\"✓\") : qsTr(\"需要密钥\"));\n                                    }\n                                    return info.join(\" \");\n                                }\n                                visible: text.length > 0\n                            }\n                        }\n\n                        MaterialSymbol {\n                            text: \"expand_more\"\n                            iconSize: Appearance.font.pixelSize.normal\n                            color: Appearance.colors.colSubtext\n                        }\n                    }\n\n                    StyledToolTip {\n                        id: toolTip\n                        content: qsTr(\"点击选择模型\")\n                    }\n                }\n\n                Item { Layout.fillWidth: true }\n\n                AiMessageControlButton {\n                    buttonIcon: \"settings\"\n                    onClicked: {\n                        // 打开设置窗口\n", "suffix": "\n                    }\n\n                    StyledToolTip {\n                        content: qsTr(\"AI设置\")\n                    }\n                }\n\n                ButtonGroup {\n                    padding: 0\n\n                    Repeater { // Command buttons\n                        model: commandButtonsRow.commandsShown\n                        delegate: ApiCommandButton {\n                            property string commandRepresentation: `${root.commandPrefix}${modelData.name}`\n                            buttonText: commandRepresentation\n                            onClicked: {\n                                if(modelData.sendDirectly) {\n                                    root.handleInput(commandRepresentation)\n                                } else {\n                                    messageInputField.text = commandRepresentation + \" \"\n                                    messageInputField.cursorPosition = messageInputField.text.length\n                                    messageInputField.forceActiveFocus()\n                                }\n                                if (modelData.name === \"clear\") {\n                                    messageInputField.text = \"\"\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n\n        }\n        \n    }\n\n}", "path": "quickshell/modules/sidebarLeft/AiChat.qml", "language": "plaintext", "prefixBegin": 0, "suffixEnd": 1}}]]