[["/home/<USER>/.config/fontconfig/conf.d/99-material-symbols.conf", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "fontconfig/conf.d/99-material-symbols.conf"}}], ["/home/<USER>/.config/quickshell/modules/common/Appearance.qml", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/common/Appearance.qml"}}], ["/home/<USER>/.config/quickshell/modules/common/widgets/NotificationGroupExpandButton.qml", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/common/widgets/NotificationGroupExpandButton.qml"}}], ["/home/<USER>/.config/quickshell/modules/common/widgets/RippleButtonWithIcon.qml", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/common/widgets/RippleButtonWithIcon.qml"}}], ["/home/<USER>/.config/quickshell/modules/dock/Dock.qml", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/dock/Dock.qml"}}], ["/home/<USER>/.config/quickshell/modules/sidebarRight/quickToggles/BluetoothToggle.qml", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/sidebarRight/quickToggles/BluetoothToggle.qml"}}], ["/home/<USER>/.config/quickshell/modules/sidebarRight/volumeMixer/VolumeMixer.qml", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/sidebarRight/volumeMixer/VolumeMixer.qml"}}], ["/home/<USER>/.config/quickshell/settings.qml", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/settings.qml"}}], ["/home/<USER>/.config/hypr/hyprland/scripts/launch_first_available.sh", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/hyprland/scripts/launch_first_available.sh"}}], ["/home/<USER>/.config/hypr/hyprland/scripts/record.sh", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/hyprland/scripts/record.sh"}}], ["/home/<USER>/.config/hypr/hyprland/scripts/workspace_action.sh", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/hyprland/scripts/workspace_action.sh"}}], ["/home/<USER>/.config/hypr/hyprland/scripts/zoom.sh", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/hyprland/scripts/zoom.sh"}}], ["/home/<USER>/.config/hypr/hyprland/scripts/fuzzel-emoji.sh", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/hyprland/scripts/fuzzel-emoji.sh"}}], ["/home/<USER>/.config/hypr/hyprland/scripts/ai/show-loaded-ollama-models.sh", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/hyprland/scripts/ai/show-loaded-ollama-models.sh"}}], ["/home/<USER>/.config/hypr/hyprland/scripts/ai/primary-buffer-query.sh", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/hyprland/scripts/ai/primary-buffer-query.sh"}}], ["/home/<USER>/.config/hypr/custom/monitor.conf", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/custom/monitor.conf"}}], ["/home/<USER>/.config/hypr/custom/keybinds.conf", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/custom/keybinds.conf"}}], ["/home/<USER>/.config/quickshell/services/Network.qml", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/services/Network.qml"}}], ["/home/<USER>/.config/hypr/hyprland/rules.conf", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/hyprland/rules.conf"}}], ["/home/<USER>/.config/quickshell/modules/backgroundWidgets/BackgroundWidgets.qml", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/backgroundWidgets/BackgroundWidgets.qml"}}], ["/home/<USER>/.config/hypr/custom/rules.conf", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/custom/rules.conf"}}], ["/home/<USER>/.config/quickshell/services/HyprlandData.qml", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/services/HyprlandData.qml"}}], ["/home/<USER>/.config/quickshell/shell.qml", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/shell.qml"}}], ["/home/<USER>/.config/MATERIAL_SYMBOLS_FIX.md", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "MATERIAL_SYMBOLS_FIX.md"}}], ["/home/<USER>/.config/hypr/hyprland/general.conf", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/hyprland/general.conf"}}], ["/home/<USER>/.config/illogical-impulse/config.json", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "illogical-impulse/config.json"}}], ["/home/<USER>/.config/quickshell/modules/onScreenKeyboard/layouts.js", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/onScreenKeyboard/layouts.js"}}], ["/home/<USER>/.config/quickshell/modules/common/ConfigOptions.qml", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/common/ConfigOptions.qml"}}], ["/home/<USER>/.config/quickshell/modules/bar/UtilButtons.qml", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/bar/UtilButtons.qml"}}], ["/home/<USER>/.config/quickshell/modules/bar/Bar.qml", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/bar/Bar.qml"}}], ["/home/<USER>/.config/quickshell/modules/common/widgets/MaterialSymbol.qml", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/common/widgets/MaterialSymbol.qml"}}], ["/home/<USER>/.config/quickshell/modules/bar/BatteryIndicator.qml", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/bar/BatteryIndicator.qml"}}], ["/home/<USER>/.config/quickshell/modules/bar/ScrollHint.qml", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/bar/ScrollHint.qml"}}], ["/home/<USER>/.config/quickshell/modules/onScreenDisplay/OnScreenDisplayBrightness.qml", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/onScreenDisplay/OnScreenDisplayBrightness.qml"}}], ["/home/<USER>/.config/hypr/custom/env.conf", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/custom/env.conf"}}], ["/home/<USER>/.config/hypr/custom/execs.conf", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/custom/execs.conf"}}], ["/home/<USER>/.config/hypr/hyprland.conf", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/hyprland.conf"}}], ["/home/<USER>/.config/hypr/hyprland/keybinds.conf", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/hyprland/keybinds.conf"}}], ["/home/<USER>/.config/microsoft-edge-stable-flags.conf", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "microsoft-edge-stable-flags.conf"}}], ["/home/<USER>/.config/hypr/hyprlock.conf", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/hyprlock.conf"}}], ["/home/<USER>/.config/hypr/hyprland/colors.conf", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/hyprland/colors.conf"}}], ["/home/<USER>/.config/quickshell/modules/onScreenKeyboard/OskKey.qml", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/modules/onScreenKeyboard/OskKey.qml"}}], ["/home/<USER>/.config/quickshell/services/Ai.qml", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "quickshell/services/Ai.qml"}}]]