{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": [], "broken_count": 1, "host": "augment-assets.com", "port": 443, "protocol_str": "quic"}], "servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397790456129996", "port": 443, "protocol_str": "quic"}], "anonymization": [], "server": "https://redirector.gvt1.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397790456402349", "port": 443, "protocol_str": "quic"}, {"advertised_alpns": ["h3"], "expiration": "13397790456402349", "port": 443, "protocol_str": "quic"}], "anonymization": [], "server": "https://r4---sn-j5o7dn7e.gvt1-cn.com"}, {"anonymization": [], "server": "https://ms-toolsai.gallery.vsassets.io", "supports_spdy": true}, {"anonymization": [], "server": "https://ms-python.gallery.vsassets.io", "supports_spdy": true}, {"anonymization": [], "server": "https://esbenp.gallery.vsassets.io", "supports_spdy": true}, {"anonymization": [], "server": "https://ms-azuretools.gallery.vsassets.io", "supports_spdy": true}, {"anonymization": [], "server": "https://github.gallery.vsassets.io", "supports_spdy": true}, {"anonymization": [], "server": "https://ms-vscode-remote.gallery.vsassets.io", "supports_spdy": true}, {"anonymization": [], "server": "https://ms-dotnettools.gallery.vsassets.io", "supports_spdy": true}, {"anonymization": [], "server": "https://ms-vscode.gallery.vsassets.io", "supports_spdy": true}, {"anonymization": [], "server": "https://vscjava.gallery.vsassets.io", "supports_spdy": true}, {"anonymization": [], "server": "https://code.visualstudio.com", "supports_spdy": true}, {"anonymization": [], "server": "https://github.com", "supports_spdy": true}, {"anonymization": [], "server": "https://raw.githubusercontent.com", "supports_spdy": true}, {"anonymization": [], "server": "https://avatars.githubusercontent.com", "supports_spdy": true}, {"anonymization": [], "server": "https://augment-assets.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397790827872970", "port": 443, "protocol_str": "quic"}], "anonymization": [], "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395311322865025", "port": 443, "protocol_str": "quic"}], "anonymization": [], "server": "https://unpkg.com", "supports_spdy": true}, {"anonymization": [], "server": "https://default.exp-tas.com", "supports_spdy": true}, {"anonymization": [], "server": "https://update.code.visualstudio.com", "supports_spdy": true}, {"anonymization": [], "server": "https://marketplace.visualstudio.com", "supports_spdy": true}, {"anonymization": [], "server": "https://api.github.com", "supports_spdy": true}, {"anonymization": [], "server": "https://main.vscode-cdn.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395323433125669", "port": 443, "protocol_str": "quic"}], "anonymization": [], "network_stats": {"srtt": 69476}, "server": "https://cdnjs.cloudflare.com", "supports_spdy": true}], "supports_quic": {"address": "2001:250:3007:3:7b3d:e6ee:4dcd:e104", "used_quic": true}, "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "3G", "CAESABiAgICA+P////8B": "4G", "CAYSABiAgICA+P////8B": "Offline"}}}