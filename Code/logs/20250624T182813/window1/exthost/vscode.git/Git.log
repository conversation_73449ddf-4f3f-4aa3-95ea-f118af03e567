2025-06-24 18:28:20.865 [info] [main] 日志级别: Info
2025-06-24 18:28:20.865 [info] [main] 正在验证在以下位置找到的 git: "git"
2025-06-24 18:28:20.865 [info] [main] 使用来自 "git" 的 git "2.50.0"
2025-06-24 18:28:20.865 [info] [Model][doInitialScan] Initial repository scan started
2025-06-24 18:28:20.865 [info] > git rev-parse --show-toplevel [47ms]
2025-06-24 18:28:20.865 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.556 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:28:21.556 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.566 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:28:21.566 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.580 [info] > git rev-parse --show-toplevel [4ms]
2025-06-24 18:28:21.580 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.606 [info] > git rev-parse --show-toplevel [3ms]
2025-06-24 18:28:21.606 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.617 [info] > git rev-parse --show-toplevel [0ms]
2025-06-24 18:28:21.617 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.627 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:28:21.627 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.638 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:28:21.638 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.648 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:28:21.648 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.673 [info] > git rev-parse --show-toplevel [15ms]
2025-06-24 18:28:21.673 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.686 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:28:21.686 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.696 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:28:21.696 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.705 [info] > git rev-parse --show-toplevel [0ms]
2025-06-24 18:28:21.705 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.720 [info] > git rev-parse --show-toplevel [4ms]
2025-06-24 18:28:21.720 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.730 [info] > git rev-parse --show-toplevel [0ms]
2025-06-24 18:28:21.730 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.740 [info] > git rev-parse --show-toplevel [0ms]
2025-06-24 18:28:21.740 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.752 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:28:21.752 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.763 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:28:21.763 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.778 [info] > git rev-parse --show-toplevel [5ms]
2025-06-24 18:28:21.778 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.789 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:28:21.789 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.801 [info] > git rev-parse --show-toplevel [0ms]
2025-06-24 18:28:21.801 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.811 [info] > git rev-parse --show-toplevel [0ms]
2025-06-24 18:28:21.811 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.821 [info] > git rev-parse --show-toplevel [0ms]
2025-06-24 18:28:21.821 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.831 [info] > git rev-parse --show-toplevel [0ms]
2025-06-24 18:28:21.831 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.841 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:28:21.841 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.852 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:28:21.852 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.862 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:28:21.873 [info] > git rev-parse --git-dir --git-common-dir [1ms]
2025-06-24 18:28:21.880 [info] [Model][openRepository] Opened repository (path): /home/<USER>/.config/hypr
2025-06-24 18:28:21.880 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/.config/hypr
2025-06-24 18:28:21.906 [info] > git rev-parse --show-toplevel [5ms]
2025-06-24 18:28:21.906 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.906 [info] > git config --get commit.template [15ms]
2025-06-24 18:28:21.926 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:28:21.928 [info] > git rev-parse --show-toplevel [12ms]
2025-06-24 18:28:21.928 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.939 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [13ms]
2025-06-24 18:28:21.961 [info] > git status -z -uall [12ms]
2025-06-24 18:28:21.962 [info] > git rev-parse --show-toplevel [24ms]
2025-06-24 18:28:21.962 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:21.962 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-24 18:28:21.999 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [25ms]
2025-06-24 18:28:22.011 [info] > git config --get commit.template [12ms]
2025-06-24 18:28:22.011 [info] > git rev-parse --show-toplevel [23ms]
2025-06-24 18:28:22.011 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:22.022 [info] > git config --get --local branch.master.vscode-merge-base [14ms]
2025-06-24 18:28:22.022 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:28:22.043 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:28:22.044 [info] > git rev-parse --show-toplevel [22ms]
2025-06-24 18:28:22.044 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:22.054 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [11ms]
2025-06-24 18:28:22.075 [info] > git reflog master --grep-reflog=branch: Created from *. [41ms]
2025-06-24 18:28:22.076 [info] > git status -z -uall [12ms]
2025-06-24 18:28:22.076 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:28:22.078 [info] > git rev-parse --show-toplevel [25ms]
2025-06-24 18:28:22.078 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:22.089 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:28:22.089 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:22.099 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:28:22.099 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:22.110 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:28:22.110 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:22.127 [info] > git rev-parse --show-toplevel [2ms]
2025-06-24 18:28:22.127 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:22.201 [info] > git rev-parse --show-toplevel [48ms]
2025-06-24 18:28:22.201 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:22.225 [info] > git rev-parse --show-toplevel [14ms]
2025-06-24 18:28:22.225 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:22.660 [info] > git check-ignore -v -z --stdin [212ms]
2025-06-24 18:28:22.920 [info] > git rev-parse --show-toplevel [682ms]
2025-06-24 18:28:22.920 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:23.008 [info] > git rev-parse --show-toplevel [48ms]
2025-06-24 18:28:23.008 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:23.062 [info] > git rev-parse --show-toplevel [21ms]
2025-06-24 18:28:23.062 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:23.097 [info] > git rev-parse --show-toplevel [18ms]
2025-06-24 18:28:23.097 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:23.112 [info] > git rev-parse --show-toplevel [3ms]
2025-06-24 18:28:23.112 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:23.153 [info] > git rev-parse --show-toplevel [28ms]
2025-06-24 18:28:23.153 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:23.182 [info] > git rev-parse --show-toplevel [17ms]
2025-06-24 18:28:23.195 [info] > git rev-parse --git-dir --git-common-dir [2ms]
2025-06-24 18:28:23.197 [info] [Model][openRepository] Opened repository (path): /home/<USER>/.config/quickshell
2025-06-24 18:28:23.197 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/.config/quickshell
2025-06-24 18:28:23.226 [info] > git rev-parse --show-toplevel [9ms]
2025-06-24 18:28:23.226 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:23.226 [info] > git config --get commit.template [18ms]
2025-06-24 18:28:23.249 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:28:23.250 [info] > git rev-parse --show-toplevel [12ms]
2025-06-24 18:28:23.250 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:23.261 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [12ms]
2025-06-24 18:28:23.281 [info] > git status -z -uall [11ms]
2025-06-24 18:28:23.282 [info] > git rev-parse --show-toplevel [22ms]
2025-06-24 18:28:23.282 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:23.283 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-24 18:28:23.319 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [24ms]
2025-06-24 18:28:23.334 [info] > git config --get --local branch.master.vscode-merge-base [4ms]
2025-06-24 18:28:23.334 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:28:23.344 [info] > git config --get commit.template [26ms]
2025-06-24 18:28:23.344 [info] > git rev-parse --show-toplevel [38ms]
2025-06-24 18:28:23.344 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:23.513 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:28:23.517 [info] > git rev-parse --show-toplevel [161ms]
2025-06-24 18:28:23.517 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:23.517 [info] > git reflog master --grep-reflog=branch: Created from *. [173ms]
2025-06-24 18:28:23.545 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [176ms]
2025-06-24 18:28:23.590 [info] > git status -z -uall [33ms]
2025-06-24 18:28:23.591 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [22ms]
2025-06-24 18:28:23.592 [info] > git rev-parse --show-toplevel [53ms]
2025-06-24 18:28:23.592 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:23.920 [info] > git rev-parse --show-toplevel [314ms]
2025-06-24 18:28:23.920 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:24.059 [info] > git check-ignore -v -z --stdin [128ms]
2025-06-24 18:28:24.060 [info] > git rev-parse --show-toplevel [46ms]
2025-06-24 18:28:24.060 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:24.061 [info] [Model][doInitialScan] Initial repository scan completed - repositories (2), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-24 18:28:24.128 [info] > git config --get --local branch.master.github-pr-owner-number [44ms]
2025-06-24 18:28:24.128 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:28:24.138 [info] > git config --get --local branch.master.github-pr-owner-number [63ms]
2025-06-24 18:28:24.138 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:28:24.150 [info] > git config --get --local branch.master.remote [2ms]
2025-06-24 18:28:24.150 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:28:24.160 [info] > git config --get --local branch.master.remote [22ms]
2025-06-24 18:28:24.161 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:28:24.172 [info] > git config --get commit.template [55ms]
2025-06-24 18:28:24.172 [info] > git config --get commit.template [66ms]
2025-06-24 18:28:24.174 [info] > git config --get --local branch.master.merge [3ms]
2025-06-24 18:28:24.174 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:28:24.174 [info] > git config --get --local branch.master.merge [14ms]
2025-06-24 18:28:24.174 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:28:24.188 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:28:24.200 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:28:24.204 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [4ms]
2025-06-24 18:28:24.228 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [41ms]
2025-06-24 18:28:24.251 [info] > git status -z -uall [35ms]
2025-06-24 18:28:24.251 [info] > git status -z -uall [13ms]
2025-06-24 18:28:24.251 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-24 18:28:24.264 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [37ms]
2025-06-24 18:28:26.662 [info] > git show --textconv :hyprlock.conf [33ms]
2025-06-24 18:28:26.662 [info] > git show --textconv HEAD:hyprlock.conf [22ms]
2025-06-24 18:28:26.663 [info] > git ls-files --stage -- hyprlock.conf [11ms]
2025-06-24 18:28:26.674 [info] > git cat-file -s 149f58ddb432eff8680c9160d1e08f8595ae02b7 [1ms]
2025-06-24 18:28:26.674 [info] > git ls-tree -l HEAD -- hyprlock.conf [12ms]
2025-06-24 18:28:26.945 [info] > git check-ignore -v -z --stdin [11ms]
2025-06-24 18:28:26.946 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-24 18:28:31.750 [info] > git show --textconv :hyprlock.conf [15ms]
2025-06-24 18:28:31.750 [info] > git ls-files --stage -- hyprlock.conf [1ms]
2025-06-24 18:28:31.767 [info] > git cat-file -s 149f58ddb432eff8680c9160d1e08f8595ae02b7 [1ms]
2025-06-24 18:28:31.958 [info] > git blame --root --incremental 9f8b79d3dd8dded0caf2a24b7ffc11625c4f1962 -- hyprlock.conf [2ms]
2025-06-24 18:28:32.019 [info] > git show --textconv HEAD:hyprlock.conf [13ms]
2025-06-24 18:28:32.020 [info] > git ls-tree -l HEAD -- hyprlock.conf [1ms]
2025-06-24 18:28:34.086 [info] > git rev-parse --show-toplevel [5ms]
2025-06-24 18:28:34.086 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:34.112 [info] > git rev-parse --show-toplevel [4ms]
2025-06-24 18:28:34.112 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:28:35.567 [info] > git show --textconv :hyprland/colors.conf [14ms]
2025-06-24 18:28:35.567 [info] > git ls-files --stage -- hyprland/colors.conf [2ms]
2025-06-24 18:28:35.579 [info] > git cat-file -s 83fcee1f6b052edff5e0e4d4c7b9ea85671ba0c0 [1ms]
2025-06-24 18:28:35.823 [info] > git show --textconv HEAD:hyprland/colors.conf [11ms]
2025-06-24 18:28:35.824 [info] > git ls-tree -l HEAD -- hyprland/colors.conf [1ms]
2025-06-24 18:28:39.961 [info] > git ls-files --stage -- hyprland/colors.conf [1ms]
2025-06-24 18:28:39.975 [info] > git cat-file -s 83fcee1f6b052edff5e0e4d4c7b9ea85671ba0c0 [2ms]
2025-06-24 18:28:41.318 [info] > git checkout -q -- hyprland/colors.conf [14ms]
2025-06-24 18:28:41.330 [info] > git ls-files --stage -- hyprland/colors.conf [12ms]
2025-06-24 18:28:41.342 [info] > git config --get commit.template [12ms]
2025-06-24 18:28:41.352 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:28:41.352 [info] > git cat-file -s 83fcee1f6b052edff5e0e4d4c7b9ea85671ba0c0 [11ms]
2025-06-24 18:28:41.352 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:28:41.375 [info] > git status -z -uall [12ms]
2025-06-24 18:28:41.375 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:28:41.736 [info] > git blame --root --incremental 9f8b79d3dd8dded0caf2a24b7ffc11625c4f1962 -- hyprland/colors.conf [1ms]
2025-06-24 18:28:42.523 [info] > git config --get commit.template [1ms]
2025-06-24 18:28:42.523 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:28:42.534 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:28:42.557 [info] > git status -z -uall [12ms]
2025-06-24 18:28:42.558 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:28:42.636 [info] > git ls-files --stage -- hyprland/colors.conf [13ms]
2025-06-24 18:28:42.647 [info] > git ls-tree -l HEAD -- hyprland/colors.conf [11ms]
2025-06-24 18:28:42.648 [info] > git cat-file -s 83fcee1f6b052edff5e0e4d4c7b9ea85671ba0c0 [1ms]
2025-06-24 18:28:42.671 [info] > git show --textconv HEAD:hyprland/colors.conf [12ms]
2025-06-24 18:28:42.671 [info] > git show --textconv :hyprland/colors.conf [0ms]
2025-06-24 18:28:43.723 [info] > git show --textconv HEAD:hyprland/colors.conf [13ms]
2025-06-24 18:28:43.770 [info] > git show --textconv :hyprland/colors.conf [48ms]
2025-06-24 18:28:43.770 [info] > git ls-files --stage -- hyprland/colors.conf [22ms]
2025-06-24 18:28:43.780 [info] > git ls-tree -l HEAD -- hyprland/colors.conf [45ms]
2025-06-24 18:28:43.781 [info] > git cat-file -s 83fcee1f6b052edff5e0e4d4c7b9ea85671ba0c0 [1ms]
2025-06-24 18:28:44.261 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-24 18:28:47.585 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:28:47.585 [info] > git config --get commit.template [12ms]
2025-06-24 18:28:47.586 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:28:47.608 [info] > git status -z -uall [11ms]
2025-06-24 18:28:47.608 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:30:05.086 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-24 18:30:05.663 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-24 18:30:07.323 [info] > git show --textconv :modules/onScreenKeyboard/OskKey.qml [15ms]
2025-06-24 18:30:07.323 [info] > git ls-files --stage -- modules/onScreenKeyboard/OskKey.qml [1ms]
2025-06-24 18:30:07.339 [info] > git cat-file -s 1f28a9e57a90f6ed156bcbfe7e5c1c99ddcc0034 [1ms]
2025-06-24 18:30:07.362 [info] > git blame --root --incremental 46897bcd5144bf5e8efc934c0e1af4410b34a0d3 -- modules/onScreenKeyboard/OskKey.qml [2ms]
2025-06-24 18:30:07.819 [info] > git show --textconv :services/Ai.qml [15ms]
2025-06-24 18:30:07.820 [info] > git ls-files --stage -- services/Ai.qml [1ms]
2025-06-24 18:30:07.835 [info] > git cat-file -s f545b878f1553f293c911f31b727277a7d154869 [1ms]
2025-06-24 18:30:07.889 [info] > git blame --root --incremental 46897bcd5144bf5e8efc934c0e1af4410b34a0d3 -- services/Ai.qml [1ms]
2025-06-24 18:30:08.093 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-24 18:34:30.836 [info] > git config --get commit.template [0ms]
2025-06-24 18:34:30.850 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:34:30.851 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:34:30.879 [info] > git status -z -uall [14ms]
2025-06-24 18:34:30.879 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:34:50.557 [info] > git config --get commit.template [1ms]
2025-06-24 18:34:50.570 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:34:50.571 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:34:50.600 [info] > git status -z -uall [15ms]
2025-06-24 18:34:50.601 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-24 18:35:12.072 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:35:12.072 [info] > git config --get commit.template [13ms]
2025-06-24 18:35:12.072 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:35:12.098 [info] > git status -z -uall [15ms]
2025-06-24 18:35:12.099 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:35:35.767 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:35:35.767 [info] > git config --get commit.template [15ms]
2025-06-24 18:35:35.767 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:35:35.795 [info] > git status -z -uall [14ms]
2025-06-24 18:35:35.796 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:35:49.975 [info] > git config --get commit.template [1ms]
2025-06-24 18:35:49.988 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:35:49.989 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:35:50.014 [info] > git status -z -uall [12ms]
2025-06-24 18:35:50.015 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:36:08.590 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:36:08.591 [info] > git config --get commit.template [19ms]
2025-06-24 18:36:08.591 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:36:08.628 [info] > git status -z -uall [19ms]
2025-06-24 18:36:08.628 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-24 18:40:06.966 [info] > git config --get commit.template [0ms]
2025-06-24 18:40:06.987 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:40:06.987 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:40:07.021 [info] > git status -z -uall [18ms]
2025-06-24 18:40:07.021 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:40:22.826 [info] > git config --get commit.template [0ms]
2025-06-24 18:40:22.843 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:40:22.843 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:40:22.881 [info] > git status -z -uall [19ms]
2025-06-24 18:40:22.882 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:41:00.881 [info] > git config --get commit.template [1ms]
2025-06-24 18:41:00.900 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:41:00.900 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [0ms]
2025-06-24 18:41:00.939 [info] > git status -z -uall [19ms]
2025-06-24 18:41:00.940 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:45:10.320 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-24 18:45:14.129 [info] > git show --textconv :modules/sidebarLeft/AiChat.qml [19ms]
2025-06-24 18:45:14.130 [info] > git ls-files --stage -- modules/sidebarLeft/AiChat.qml [1ms]
2025-06-24 18:45:14.150 [info] > git cat-file -s 422dc41b836be445a7fcceb29ef16e0b9c33590a [2ms]
2025-06-24 18:45:14.224 [info] > git blame --root --incremental 46897bcd5144bf5e8efc934c0e1af4410b34a0d3 -- modules/sidebarLeft/AiChat.qml [1ms]
2025-06-24 18:45:14.393 [info] > git check-ignore -v -z --stdin [1ms]
