2025-06-24 18:28:21.044 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-24 18:28:21.044 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"disableFocusOnAugmentPanel":false,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-24 18:28:21.044 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"maxLinesTerminalProcessOutput":0,"vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryMaxChars":0,"historySummaryLowerChars":0,"historySummaryPrompt":"","enableSpawnSubAgentTool":false,"enableCommitIndexing":false,"maxCommitsToIndex":0}
2025-06-24 18:28:21.044 [info] 'AugmentExtension' Retrieving model config
2025-06-24 18:28:22.239 [info] 'AugmentExtension' Retrieved model config
2025-06-24 18:28:22.239 [info] 'AugmentExtension' Returning model config
2025-06-24 18:28:22.252 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - enableNewThreadsList: false to true
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - vscodeTaskListMinVersion: "" to "0.482.0"
  - vscodeSupportToolUseStartMinVersion: "" to "0.485.0"
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryMinVersion: "" to "0.0.0"
  - historySummaryMaxChars: 0 to 200000
  - historySummaryLowerChars: 0 to 80000
  - historySummaryPrompt: "" to "Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\n\nYour summary should be structured as follows:\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\n\nExample summary structure:\n1. Previous Conversation:\n[Detailed description]\n2. Current Work:\n[Detailed description]\n3. Key Technical Concepts:\n- [Concept 1]\n- [Concept 2]\n- [...]\n4. Relevant Files and Code:\n- [File Name 1]\n    - [Summary of why this file is important]\n    - [Summary of the changes made to this file, if any]\n    - [Important Code Snippet]\n- [File Name 2]\n    - [Important Code Snippet]\n- [...]\n5. Problem Solving:\n[Detailed description]\n6. Pending Tasks and Next Steps:\n- [Task 1 details & next steps]\n- [Task 2 details & next steps]\n- [...]\n\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\n"
2025-06-24 18:28:22.252 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/.config (explicit) at 2025/6/24 16:02:55
2025-06-24 18:28:22.252 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-24 18:28:22.252 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-24 18:28:22.252 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/.config granted at 2025/6/24 16:02:55; type = explicit
2025-06-24 18:28:22.252 [info] 'WorkspaceManager' Adding workspace folder .config; folderRoot = /home/<USER>/.config; syncingPermission = granted
2025-06-24 18:28:22.252 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/.config (explicit) at 2025/6/24 16:02:55
2025-06-24 18:28:22.298 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-24 18:28:22.298 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-24 18:28:22.310 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-06-24 18:28:22.324 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-24 18:28:22.324 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-24 18:28:23.021 [info] 'WorkspaceManager[.config]' Start tracking
2025-06-24 18:28:23.129 [info] 'PathMap' Opened source folder /home/<USER>/.config with id 100
2025-06-24 18:28:23.129 [info] 'OpenFileManager' Opened source folder 100
2025-06-24 18:28:23.195 [info] 'MtimeCache[.config]' reading blob name cache from /home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/053bfff36d55a35354aa78fe5c63eac5cf43c0b9b251966b5e57a559210d4a3e/mtime-cache.json
2025-06-24 18:28:23.378 [info] 'MtimeCache[.config]' read 12559 entries from /home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/053bfff36d55a35354aa78fe5c63eac5cf43c0b9b251966b5e57a559210d4a3e/mtime-cache.json
2025-06-24 18:28:24.148 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-24 18:28:24.148 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-24 18:28:24.278 [info] 'ToolsModel' Tools Mode: AGENT (6 hosts)
2025-06-24 18:28:25.664 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-06-24 18:28:26.337 [info] 'ToolsModel' Host: mcpHost (1 tools: 18 enabled, 0 disabled})
 + search_g-search

2025-06-24 18:28:26.337 [info] 'ToolsModel' Host: mcpHost (1 tools: 48 enabled, 0 disabled})
 + interactive_feedback_interactive-feedback-mcp

2025-06-24 18:28:26.337 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-24 18:28:26.337 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-24 18:28:26.337 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-24 18:28:26.609 [info] 'WorkspaceManager[.config]' Directory removed: Code/logs/20250624T105718
2025-06-24 18:28:26.609 [info] 'WorkspaceManager[.config]' Directory removed: Code/logs/20250624T105814
2025-06-24 18:28:31.701 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 3e34a41e44c7353c11670c9ba87ce0d198b77aed0aa85108e48f804c747f58d1: deleted
2025-06-24 18:28:34.068 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-24 18:28:34.096 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-24 18:28:41.522 [error] 'FuzzySymbolSearcher' Failed to read file tokens for dfe32595fd7ea9fb45ac74186f6a6be4b06b432fa324fa0d6ba72c3998d156be: deleted
2025-06-24 18:28:45.144 [info] 'WorkspaceManager[.config]' Directory removed: Code/User/workspaceStorage/1750760423647
2025-06-24 18:28:51.398 [error] 'AugmentExtension' API request e27be934-4bc2-42e4-973b-ed4725226c4f to https://i0.api.augmentcode.com/find-missing failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:28:51.901 [error] 'AugmentExtension' API request 516b3df7-7266-4176-b7cc-d15fb2217d6a to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:28:51.901 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:28:51.901 [info] 'DiskFileManager[.config]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 18:28:53.887 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
2025-06-24 18:29:05.645 [info] 'WorkspaceManager[.config]' Directory created: Code/blob_storage/1d50be04-60ef-4e36-a60b-97264c5c2cbe
2025-06-24 18:29:05.645 [info] 'WorkspaceManager[.config]' Directory removed: Code/blob_storage/5f4b81f2-953e-4b58-8cab-0f107b990c36
2025-06-24 18:29:05.648 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182905
2025-06-24 18:29:05.678 [error] 'AugmentExtension' API request 8184672d-b3cf-4603-89fb-1d3e611d34ee to https://i0.api.augmentcode.com/batch-upload failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:29:05.918 [info] 'WorkspaceManager[.config]' Directory created: Code/User/workspaceStorage/1750760946047
2025-06-24 18:29:05.920 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window2
2025-06-24 18:29:06.181 [error] 'AugmentExtension' API request 8ee20805-1ff3-42d9-9d2b-9db04a126ed7 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:29:06.181 [error] 'AugmentExtension' Dropping error report "batch-upload call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:29:06.181 [info] 'DiskFileManager[.config]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 18:29:06.591 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window2/exthost
2025-06-24 18:29:06.668 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window2/output_20250624T182906
2025-06-24 18:29:06.922 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window2/exthost/vscode.github-authentication
2025-06-24 18:29:07.742 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window2/exthost/output_logging_20250624T182906
2025-06-24 18:29:07.743 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window2/exthost/vscode.microsoft-authentication
2025-06-24 18:29:07.946 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window2/exthost/vscode.git
2025-06-24 18:29:07.948 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window2/exthost/vscode.json-language-features
2025-06-24 18:29:08.203 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
2025-06-24 18:29:08.841 [info] 'WorkspaceManager[.config]' Directory created: microsoft-edge/Edge Designer
2025-06-24 18:29:08.842 [info] 'WorkspaceManager[.config]' Directory removed: sparkle/blob_storage/9bf7b702-9785-4a70-b372-1be3c36e9f55
2025-06-24 18:29:08.849 [info] 'WorkspaceManager[.config]' Directory created: sparkle/blob_storage/1cafcc1f-2367-42c1-980b-350f5f61d766
2025-06-24 18:29:09.555 [info] 'WorkspaceManager[.config]' Directory removed: microsoft-edge/Edge Designer
2025-06-24 18:29:09.747 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window2/exthost/ms-dotnettools.vscode-dotnet-runtime
2025-06-24 18:29:09.969 [info] 'WorkspaceManager[.config]' Directory created: microsoft-edge/Default/blob_storage/e8215009-7b73-4dea-9969-15b6868220c0
2025-06-24 18:29:09.969 [info] 'WorkspaceManager[.config]' Directory removed: Code/User/globalStorage/github.vscode-pull-request-github/temp
2025-06-24 18:29:10.439 [info] 'WorkspaceManager[.config]' Directory created: Code/User/globalStorage/github.vscode-pull-request-github/temp
2025-06-24 18:29:10.446 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window2/exthost/Augment.vscode-augment
2025-06-24 18:29:10.446 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window2/exthost/GitHub.copilot
2025-06-24 18:29:10.446 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window2/exthost/GitHub.copilot-chat
2025-06-24 18:29:10.446 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window2/exthost/GitHub.vscode-pull-request-github
2025-06-24 18:29:10.446 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window2/exthost/vscode.github
2025-06-24 18:29:10.918 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window2/exthost/ms-vscode-remote.remote-containers
2025-06-24 18:29:13.425 [info] 'WorkspaceManager[.config]' Directory removed: microsoft-edge/Default/blob_storage/6b5f5c7e-abfa-40c2-9fd2-e1f0084ac504
2025-06-24 18:30:08.920 [error] 'AugmentExtension' API request c6fb661f-2390-4a9f-8d74-af69885229a3 to https://i0.api.augmentcode.com/batch-upload failed: This operation was aborted
2025-06-24 18:30:09.445 [info] 'WorkspaceManager[.config]' Directory removed: microsoft-edge/FirstPartySetsPreloaded/2025.4.2.0/_metadata
2025-06-24 18:30:09.446 [error] 'AugmentExtension' API request f4cb6d82-e854-4ebf-95e3-764af2fb3c8f to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:30:09.446 [error] 'AugmentExtension' Dropping error report "batch-upload call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:30:09.446 [info] 'DiskFileManager[.config]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-24 18:30:10.097 [info] 'WorkspaceManager[.config]' Directory removed: microsoft-edge/Subresource Filter/Unindexed Rules/9.55.0/_metadata
2025-06-24 18:30:11.419 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
2025-06-24 18:30:14.950 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-24 18:30:15.335 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-24 18:30:15.335 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-24 18:30:15.337 [info] 'TaskManager' Setting current root task UUID to 2cb1b5d8-1f90-4cb6-ae2b-87bfe9780bed
2025-06-24 18:30:15.412 [info] 'TaskManager' Setting current root task UUID to 26cd876c-f1c6-478f-9e55-21e2e75ab961
2025-06-24 18:30:15.413 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-24 18:30:15.414 [error] 'AugmentExtensionSidecar' API request 89f5ac16-d99e-498f-b147-ccf05f469243 to https://i0.api.augmentcode.com/remote-agents/list-stream failed: This operation was aborted
2025-06-24 18:30:15.414 [error] 'AugmentExtensionSidecar' AbortError: This operation was aborted
    at node:internal/deps/undici/undici:13510:13
2025-06-24 18:30:15.471 [error] 'GitReferenceMessenger' Failed to locally get remote url: Failed to get remote url, no remote found
2025-06-24 18:30:15.926 [error] 'AugmentExtension' API request fa2dfc6f-1066-4866-95fb-464b47f0ecdb to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:30:15.926 [error] 'AugmentExtension' Dropping error report "remote-agents/list-stream call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:30:15.945 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/d852bbd2-c4f9-4744-bc04-49b487aa0091/document--1750753309067-e99b06e7-f370-40cd-ae05-26d83d3497b1.json
2025-06-24 18:30:15.946 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/d852bbd2-c4f9-4744-bc04-49b487aa0091/document--1750753309067-e99b06e7-f370-40cd-ae05-26d83d3497b1.json
2025-06-24 18:30:17.575 [info] 'TaskManager' Setting current root task UUID to 2cb1b5d8-1f90-4cb6-ae2b-87bfe9780bed
2025-06-24 18:30:17.576 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-24 18:30:17.854 [error] 'AugmentExtension' API request 457a2542-2f56-483f-b84c-97beac182f87 to https://i0.api.augmentcode.com/batch-upload failed: This operation was aborted
2025-06-24 18:30:18.376 [error] 'AugmentExtension' API request 28c0859f-da84-4e25-8d7f-e1b9a3f5bc4c to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:30:18.376 [error] 'AugmentExtension' Dropping error report "batch-upload call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:30:18.376 [info] 'DiskFileManager[.config]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-24 18:30:19.133 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
2025-06-24 18:30:34.540 [error] 'AugmentExtension' API request cecf9616-e696-457d-b0b3-58fd0bfecd6e to https://i0.api.augmentcode.com/batch-upload failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:30:35.045 [error] 'AugmentExtension' API request fd1dd34b-784a-4e65-a23b-514d7eb7f9b8 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:30:35.045 [error] 'AugmentExtension' Dropping error report "batch-upload call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:30:35.045 [info] 'DiskFileManager[.config]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 18:30:36.124 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
2025-06-24 18:30:41.967 [info] 'WorkspaceManager[.config]' Tracking enabled
2025-06-24 18:30:41.967 [info] 'WorkspaceManager[.config]' Path metrics:
  - directories emitted: 3968
  - files emitted: 18646
  - other paths emitted: 16
  - total paths emitted: 22630
  - timing stats:
    - readDir: 40 ms
    - filter: 171 ms
    - yield: 30 ms
    - total: 316 ms
2025-06-24 18:30:41.967 [info] 'WorkspaceManager[.config]' File metrics:
  - paths accepted: 13048
  - paths not accessible: 0
  - not plain files: 0
  - large files: 728
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 12513
  - mtime cache misses: 535
  - probe batches: 46
  - blob names probed: 13260
  - files read: 6513
  - blobs uploaded: 226
  - timing stats:
    - ingestPath: 91 ms
    - probe: 56087 ms
    - stat: 49 ms
    - read: 4457 ms
    - upload: 36067 ms
2025-06-24 18:30:41.967 [info] 'WorkspaceManager[.config]' Startup metrics:
  - create SourceFolder: 174 ms
  - read MtimeCache: 184 ms
  - pre-populate PathMap: 109 ms
  - create PathFilter: 188 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 320 ms
  - purge stale PathMap entries: 2 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 137962 ms
  - enable persist: 8 ms
  - total: 138947 ms
2025-06-24 18:30:41.967 [info] 'WorkspaceManager' Workspace startup complete in 139722 ms
2025-06-24 18:31:02.754 [info] 'ViewTool' Tool called with path: quickshell/services/Ai.qml and view_range: undefined
2025-06-24 18:31:22.277 [info] 'WorkspaceManager[.config]' Directory created: microsoft-edge/Default/WebStorage/75
2025-06-24 18:31:22.519 [error] 'AugmentExtension' API request f4dc5ae2-e97d-45d3-a28c-f8fa57337513 to https://i0.api.augmentcode.com/record-request-events failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:31:23.021 [error] 'AugmentExtension' API request 2f2a938c-feab-4d97-9b09-aac402de9348 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:31:23.021 [error] 'AugmentExtension' Dropping error report "record-request-events call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:31:23.021 [error] 'ToolUseRequestEventReporter' Error uploading metrics: Error: fetch failed Error: fetch failed
    at e.transientIssue (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:13968)
    at wM.callApi (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:12446)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.callApi (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:57610)
    at wM.logToolUseRequestEvent (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:35999)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:18561
    at Qs (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:16773)
    at e._doUpload (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:18460)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:17783
2025-06-24 18:31:23.021 [info] 'ToolUseRequestEventReporter' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 18:31:23.699 [info] 'ToolUseRequestEventReporter' Operation succeeded after 1 transient failures
2025-06-24 18:33:26.246 [info] 'ViewTool' Tool called with path: quickshell/modules/sidebarLeft/AiChat.qml and view_range: [1,100]
2025-06-24 18:33:37.523 [info] 'ViewTool' Tool called with path: quickshell/modules/settings/ServicesConfig.qml and view_range: undefined
2025-06-24 18:34:25.485 [error] 'AugmentExtension' API request fc530e91-b71f-4785-9ceb-0f769944e635 to https://i0.api.augmentcode.com/subscription-info failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:34:25.987 [error] 'AugmentExtension' API request 2138b6f6-bc13-47df-9713-1a76a67f245b to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:34:25.987 [error] 'AugmentExtension' Dropping error report "subscription-info call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:34:25.987 [error] 'ChatApp' Failed to get subscription info: Error: fetch failed
2025-06-24 18:34:26.005 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":10651.321619,"timestamp":"2025-06-24T10:34:25.987Z"}]
2025-06-24 18:34:34.764 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:34:34.941 [info] 'WorkspaceManager[.config]' Directory created: Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/checkpoint-documents/73449ddf-4f3f-4aa3-95ea-f118af03e567
2025-06-24 18:34:39.463 [error] 'AugmentExtension' API request b62d9e13-cd66-4993-8b48-273400a6c773 to https://i0.api.augmentcode.com/find-missing failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:34:39.966 [error] 'AugmentExtension' API request 88c76dc4-9053-41a1-9f84-61131feadec1 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:34:39.966 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:34:39.966 [info] 'DiskFileManager[.config]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 18:34:48.457 [error] 'AugmentExtension' API request 93b218ab-faa0-4f83-899b-ab3b12d934c4 to https://i0.api.augmentcode.com/find-missing failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:34:48.959 [error] 'AugmentExtension' API request 046b40ec-2bdd-4149-ba35-0736b8545282 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:34:48.959 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:34:48.959 [info] 'DiskFileManager[.config]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 18:34:49.372 [info] 'ToolFileUtils' Reading file: quickshell/settings.qml
2025-06-24 18:34:49.376 [info] 'ToolFileUtils' Successfully read file: quickshell/settings.qml (9366 bytes)
2025-06-24 18:34:49.747 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
2025-06-24 18:34:50.415 [info] 'ToolFileUtils' Reading file: quickshell/settings.qml
2025-06-24 18:34:50.416 [info] 'ToolFileUtils' Successfully read file: quickshell/settings.qml (9543 bytes)
2025-06-24 18:34:50.453 [error] 'AugmentExtension' API request bb53566b-104c-4406-9312-83b083a1a866 to https://i0.api.augmentcode.com/find-missing failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:34:50.955 [error] 'AugmentExtension' API request e34973a7-a748-4567-8341-00898e9283a4 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:34:50.955 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:34:50.955 [info] 'DiskFileManager[.config]' Operation failed with error Error: fetch failed, retrying in 200 ms; retries = 1
2025-06-24 18:34:51.922 [info] 'DiskFileManager[.config]' Operation succeeded after 2 transient failures
2025-06-24 18:34:51.950 [error] 'AugmentExtension' API request ee97ee1f-6df9-4b77-8894-831ca7289019 to https://i0.api.augmentcode.com/batch-upload failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:34:52.453 [error] 'AugmentExtension' API request eb4f8dc3-54e8-49f3-9e81-f35ef9c3969a to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:34:52.453 [error] 'AugmentExtension' Dropping error report "batch-upload call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:34:52.453 [info] 'DiskFileManager[.config]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 18:34:53.467 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
2025-06-24 18:34:54.385 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:34:54.945 [error] 'AugmentExtension' API request 2b1633b2-8e5b-4a0c-b2f3-a5e590189f8e to https://i0.api.augmentcode.com/batch-upload failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:34:55.449 [error] 'AugmentExtension' API request a83fd31e-dc43-4c82-afee-b9cb558c103a to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:34:55.449 [error] 'AugmentExtension' Dropping error report "batch-upload call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:34:55.449 [info] 'DiskFileManager[.config]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 18:34:56.568 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
2025-06-24 18:35:10.875 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:35:10.875 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (10096 bytes)
2025-06-24 18:35:11.044 [info] 'WorkspaceManager[.config]' Directory created: Code/User/History/-e509d4b
2025-06-24 18:35:11.917 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:35:11.917 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (12875 bytes)
2025-06-24 18:35:15.879 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:35:21.408 [error] 'AugmentExtension' API request 38989bf6-b374-49f5-b9e9-61fe284b42cc to https://i0.api.augmentcode.com/find-missing failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:35:21.910 [error] 'AugmentExtension' API request 3e87c625-9ed6-4c49-bebf-ab5bf9a6a5ed to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:35:21.910 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:35:21.910 [info] 'DiskFileManager[.config]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 18:35:22.711 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
2025-06-24 18:35:34.532 [info] 'ToolFileUtils' Reading file: quickshell/services/Ai.qml
2025-06-24 18:35:34.532 [info] 'ToolFileUtils' Successfully read file: quickshell/services/Ai.qml (33311 bytes)
2025-06-24 18:35:34.737 [info] 'WorkspaceManager[.config]' Directory created: Code/User/History/2f6c0000
2025-06-24 18:35:35.611 [info] 'ToolFileUtils' Reading file: quickshell/services/Ai.qml
2025-06-24 18:35:35.611 [info] 'ToolFileUtils' Successfully read file: quickshell/services/Ai.qml (35523 bytes)
2025-06-24 18:35:39.538 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:35:48.799 [info] 'ToolFileUtils' Reading file: quickshell/services/Ai.qml
2025-06-24 18:35:48.799 [info] 'ToolFileUtils' Successfully read file: quickshell/services/Ai.qml (35523 bytes)
2025-06-24 18:35:48.809 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 94b919638c8201f292c81883d2f2def64613889264b29ebbdaa48fb1881db10e: deleted
2025-06-24 18:35:48.809 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 94b919638c8201f292c81883d2f2def64613889264b29ebbdaa48fb1881db10e: deleted
2025-06-24 18:35:49.834 [info] 'ToolFileUtils' Reading file: quickshell/services/Ai.qml
2025-06-24 18:35:49.834 [info] 'ToolFileUtils' Successfully read file: quickshell/services/Ai.qml (35377 bytes)
2025-06-24 18:35:53.809 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 94b919638c8201f292c81883d2f2def64613889264b29ebbdaa48fb1881db10e: deleted
2025-06-24 18:35:53.811 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:36:07.369 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:36:07.370 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (12875 bytes)
2025-06-24 18:36:08.433 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:36:08.433 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (13488 bytes)
2025-06-24 18:36:12.379 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:36:29.115 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:36:29.115 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (13488 bytes)
2025-06-24 18:36:29.120 [error] 'FuzzySymbolSearcher' Failed to read file tokens for c2b31733a8b1de55af2081bcb14e9ca6cf0b3973d6b2563cdc31e4d9ca4d23d7: deleted
2025-06-24 18:36:30.151 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:36:30.152 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (17057 bytes)
2025-06-24 18:36:34.118 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:36:50.055 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:36:57.582 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:36:57.583 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (17057 bytes)
2025-06-24 18:36:58.608 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:36:58.608 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (17564 bytes)
2025-06-24 18:37:02.589 [error] 'FuzzySymbolSearcher' Failed to read file tokens for a74e225b606b9c104f7cd5cd8807f812e016a7d447fc6abf0e1094d710fda6ef: deleted
2025-06-24 18:37:02.615 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:37:08.751 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:37:08.751 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (17564 bytes)
2025-06-24 18:37:09.775 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:37:09.775 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (17642 bytes)
2025-06-24 18:37:13.754 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:37:13.756 [error] 'FuzzySymbolSearcher' Failed to read file tokens for e337533abfff906cacd42033e3faa3114a6c2448a5da0a0341469e80dd8d539c: deleted
2025-06-24 18:37:39.357 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:37:39.361 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (17642 bytes)
2025-06-24 18:37:40.401 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:37:40.401 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (21876 bytes)
2025-06-24 18:37:44.365 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:37:52.287 [error] 'AugmentExtension' API request 6d31c4d2-fe25-4513-be4c-bda0161017b2 to https://i0.api.augmentcode.com/client-metrics failed: This operation was aborted
2025-06-24 18:37:52.802 [error] 'AugmentExtension' API request 9b3c30d9-d2e2-4cff-a5a5-246142103bb6 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:37:52.802 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:37:52.802 [error] 'ClientMetricsReporter' Error uploading metrics: Error: This operation was aborted Error: This operation was aborted
    at e.transientIssue (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:13968)
    at wM.callApi (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:12446)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.callApi (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:57610)
    at wM.clientMetrics (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:52204)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:18561
    at Qs (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:16773)
    at e._doUpload (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:18460)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:17783
2025-06-24 18:37:52.802 [info] 'ClientMetricsReporter' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-24 18:37:53.491 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-06-24 18:37:57.422 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:37:57.422 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (21876 bytes)
2025-06-24 18:37:57.428 [error] 'FuzzySymbolSearcher' Failed to read file tokens for e03f36370d41c7e9d01f1b88bac579f67b96d30576b4b12875ae45df5848769c: deleted
2025-06-24 18:37:58.452 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:37:58.452 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (22376 bytes)
2025-06-24 18:38:02.425 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:38:12.660 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/AiChat.qml
2025-06-24 18:38:12.664 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/AiChat.qml (22611 bytes)
2025-06-24 18:38:13.717 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/AiChat.qml
2025-06-24 18:38:13.717 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/AiChat.qml (22610 bytes)
2025-06-24 18:38:17.671 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:38:25.957 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/AiChat.qml
2025-06-24 18:38:25.957 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/AiChat.qml (22610 bytes)
2025-06-24 18:38:25.963 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 34b98bffc93cc7a16995a758a5828bf950e7f61eea69b2533b660907b35e87b6: deleted
2025-06-24 18:38:26.995 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/AiChat.qml
2025-06-24 18:38:26.995 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/AiChat.qml (22994 bytes)
2025-06-24 18:38:30.961 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:39:13.548 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:39:17.895 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/AiChat.qml
2025-06-24 18:39:17.895 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/AiChat.qml (22994 bytes)
2025-06-24 18:39:18.921 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/AiChat.qml
2025-06-24 18:39:18.921 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/AiChat.qml (23125 bytes)
2025-06-24 18:39:22.901 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:39:29.399 [info] 'ViewTool' Tool called with path: quickshell/modules/sidebarLeft/AiChat.qml and view_range: [500,555]
2025-06-24 18:40:02.301 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/AiChat.qml
2025-06-24 18:40:02.301 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/AiChat.qml (23125 bytes)
2025-06-24 18:40:03.329 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/AiChat.qml
2025-06-24 18:40:03.329 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/AiChat.qml (24367 bytes)
2025-06-24 18:40:07.304 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:40:41.174 [error] 'AugmentExtension' API request 45e71a92-fb3e-4e5b-b995-4c3bad14080f to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-24 18:40:41.691 [error] 'AugmentExtension' API request da5751d9-58b4-474d-bd31-26d1bf77eb7f to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:40:41.691 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:40:41.691 [info] 'DiskFileManager[.config]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-24 18:40:42.489 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
2025-06-24 18:40:46.436 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/AiChat.qml
2025-06-24 18:40:46.436 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/AiChat.qml (24367 bytes)
2025-06-24 18:40:54.507 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml
2025-06-24 18:40:54.507 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml (6768 bytes)
2025-06-24 18:40:54.670 [info] 'WorkspaceManager[.config]' Directory created: Code/User/History/-530c3716
2025-06-24 18:40:55.217 [error] 'AugmentExtension' API request 06216057-356c-483d-aa08-5fe301e52412 to https://i0.api.augmentcode.com/batch-upload failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:40:55.545 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml
2025-06-24 18:40:55.545 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml (6794 bytes)
2025-06-24 18:40:55.721 [error] 'AugmentExtension' API request 6d316cda-232c-43b3-8311-fda495e99f77 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:40:55.721 [error] 'AugmentExtension' Dropping error report "batch-upload call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:40:55.721 [info] 'DiskFileManager[.config]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 18:40:56.898 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
2025-06-24 18:40:59.511 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:41:04.704 [error] 'AugmentExtension' API request 88e7b501-9b17-4546-99ff-16e4fd4475f3 to https://i0.api.augmentcode.com/find-missing failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:41:05.205 [error] 'AugmentExtension' API request bc45a5c8-0671-4ff6-a93b-673cc7bff758 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:41:05.205 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:41:05.205 [info] 'FileUploader#BlobStatusExecutor' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 18:41:05.986 [info] 'FileUploader#BlobStatusExecutor' Operation succeeded after 1 transient failures
2025-06-24 18:41:43.298 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:41:55.502 [info] 'WorkspaceManager[.config]' Directory created: Code/User/History/73ec2fe7
2025-06-24 18:46:15.897 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:46:21.156 [error] 'AugmentExtension' API request 5233aa63-1a1a-47be-9778-c0fa0b75b1c3 to https://i0.api.augmentcode.com/find-missing response 503: Service Unavailable
2025-06-24 18:46:21.660 [error] 'AugmentExtension' API request 8923fb6d-270b-4399-9bc9-e44882c31598 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:46:21.660 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:46:21.660 [info] 'DiskFileManager[.config]' Operation failed with error Error: HTTP error: 503 Service Unavailable, retrying in 100 ms; retries = 0
2025-06-24 18:46:23.107 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
2025-06-24 18:46:26.150 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml
2025-06-24 18:46:26.153 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml (6794 bytes)
2025-06-24 18:46:27.192 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml
2025-06-24 18:46:27.192 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml (6768 bytes)
2025-06-24 18:46:31.156 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:46:46.479 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml
2025-06-24 18:46:46.479 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml (6768 bytes)
2025-06-24 18:46:46.484 [error] 'FuzzySymbolSearcher' Failed to read file tokens for ae085bbcf0ba5adb83ca8b5b1bec3bae382241821eb298f87042590c4d358aca: deleted
2025-06-24 18:46:47.526 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml
2025-06-24 18:46:47.526 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml (6537 bytes)
2025-06-24 18:46:51.485 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:47:16.490 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1074.432851,"timestamp":"2025-06-24T10:47:16.411Z"}]
2025-06-24 18:47:31.530 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/AiChat.qml
2025-06-24 18:47:31.530 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/AiChat.qml (24367 bytes)
2025-06-24 18:47:32.596 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/AiChat.qml
2025-06-24 18:47:32.596 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/AiChat.qml (24479 bytes)
2025-06-24 18:47:36.536 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:47:36.545 [error] 'FuzzySymbolSearcher' Failed to read file tokens for c1fe620b6d1f719eead25945237daa2e4b04e9667c43c4035a206d9db32f764c: deleted
2025-06-24 18:47:49.651 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml
2025-06-24 18:47:49.651 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml (6537 bytes)
2025-06-24 18:47:50.686 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml
2025-06-24 18:47:50.686 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml (6683 bytes)
2025-06-24 18:47:54.654 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 4ae37743767aebc4c14141306787c1bfde6837c2a3e80824b87ed8602f5169cc: deleted
2025-06-24 18:47:54.667 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:47:59.718 [error] 'AugmentExtension' API request 190dc0d5-f3e5-4f55-a283-722c7890e4b0 to https://i0.api.augmentcode.com/memorize failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:48:00.221 [error] 'AugmentExtension' API request 45333b15-de00-467e-a93c-d85a1047863a to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:48:00.221 [error] 'AugmentExtension' Dropping error report "memorize call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:48:00.221 [info] 'OpenFileManager' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 18:48:01.128 [info] 'OpenFileManager' Operation succeeded after 1 transient failures
2025-06-24 18:48:01.487 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml
2025-06-24 18:48:01.487 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml (6683 bytes)
2025-06-24 18:48:02.508 [info] 'ToolFileUtils' Reading file: quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml
2025-06-24 18:48:02.508 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/sidebarLeft/aiChat/ModelSelector.qml (6768 bytes)
2025-06-24 18:48:06.490 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:48:13.925 [info] 'ToolFileUtils' Reading file: quickshell/settings.qml
2025-06-24 18:48:13.930 [info] 'ToolFileUtils' Successfully read file: quickshell/settings.qml (9543 bytes)
2025-06-24 18:48:14.989 [info] 'ToolFileUtils' Reading file: quickshell/settings.qml
2025-06-24 18:48:14.989 [info] 'ToolFileUtils' Successfully read file: quickshell/settings.qml (9850 bytes)
2025-06-24 18:48:18.939 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:48:29.163 [error] 'AugmentExtension' API request ed4daf83-2c95-4308-9ce4-27f442a7759d to https://i0.api.augmentcode.com/record-user-events failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:48:29.665 [error] 'AugmentExtension' API request 2ad57a4c-879c-49e7-b43b-9eeaf8666f76 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:48:29.665 [error] 'AugmentExtension' Dropping error report "record-user-events call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:48:29.665 [info] 'UploadHandler' Error uploading tracked events fetch failed
2025-06-24 18:49:00.915 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:49:11.800 [info] 'ToolFileUtils' Reading file: AI_CONFIG_SUMMARY.md
2025-06-24 18:49:11.803 [info] 'ToolFileUtils' Successfully read file: AI_CONFIG_SUMMARY.md (2778 bytes)
2025-06-24 18:49:28.592 [info] 'ToolFileUtils' Reading file: AI_CONFIG_SUMMARY.md
2025-06-24 18:49:28.592 [info] 'ToolFileUtils' Successfully read file: AI_CONFIG_SUMMARY.md (2778 bytes)
2025-06-24 18:49:28.851 [info] 'WorkspaceManager[.config]' Directory created: Code/User/History/6e32ba36
2025-06-24 18:49:29.650 [info] 'ToolFileUtils' Reading file: AI_CONFIG_SUMMARY.md
2025-06-24 18:49:29.650 [info] 'ToolFileUtils' Successfully read file: AI_CONFIG_SUMMARY.md (3461 bytes)
2025-06-24 18:49:33.596 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:49:54.223 [error] 'AugmentExtension' API request 5a559a1d-d266-444f-91c5-5b7e5d7d13a1 to https://i0.api.augmentcode.com/find-missing failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:49:54.725 [error] 'AugmentExtension' API request 4bf54185-4443-4c2e-a5f1-4f6c4321d87b to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:49:54.725 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:49:54.725 [info] 'DiskFileManager[.config]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 18:49:55.472 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
2025-06-24 18:49:58.217 [error] 'AugmentExtension' API request e5d67c35-ed5f-4d7e-8a11-7c9de6d0c60a to https://i0.api.augmentcode.com/batch-upload failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:49:58.719 [error] 'AugmentExtension' API request 92c2de2a-aae9-40be-a5c2-b2ccfcb0fa7b to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:49:58.719 [error] 'AugmentExtension' Dropping error report "batch-upload call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:49:58.719 [info] 'DiskFileManager[.config]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 18:49:59.881 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
2025-06-24 18:50:07.223 [error] 'AugmentExtension' API request 01886d45-9e0d-4d3f-a3e0-a99ee180a06f to https://i0.api.augmentcode.com/batch-upload failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:50:07.725 [error] 'AugmentExtension' API request 7984de65-923a-4944-af91-4b4c209be588 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:50:07.725 [error] 'AugmentExtension' Dropping error report "batch-upload call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:50:07.725 [info] 'DiskFileManager[.config]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 18:50:09.007 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
2025-06-24 18:50:20.533 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:50:21.474 [info] 'ViewTool' Tool called with path: quickshell/modules/backgroundWidgets/BackgroundWidgets.qml and view_range: undefined
2025-06-24 18:50:40.793 [info] 'ViewTool' Tool called with path: quickshell/state/user/generated/wallpaper/least_busy_region.json and view_range: undefined
2025-06-24 18:50:40.794 [info] 'ViewTool' Path does not exist: quickshell/state/user/generated/wallpaper/least_busy_region.json
2025-06-24 18:51:24.287 [info] 'ViewTool' Tool called with path: quickshell/state and view_range: undefined
2025-06-24 18:51:24.288 [info] 'ViewTool' Path does not exist: quickshell/state
2025-06-24 18:51:44.515 [info] 'ToolFileUtils' Reading file: quickshell/modules/backgroundWidgets/BackgroundWidgets.qml
2025-06-24 18:51:44.515 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/backgroundWidgets/BackgroundWidgets.qml (5764 bytes)
2025-06-24 18:51:45.562 [info] 'ToolFileUtils' Reading file: quickshell/modules/backgroundWidgets/BackgroundWidgets.qml
2025-06-24 18:51:45.562 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/backgroundWidgets/BackgroundWidgets.qml (6397 bytes)
2025-06-24 18:51:49.519 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:51:59.745 [info] 'ToolFileUtils' Reading file: quickshell/modules/backgroundWidgets/BackgroundWidgets.qml
2025-06-24 18:51:59.745 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/backgroundWidgets/BackgroundWidgets.qml (6397 bytes)
2025-06-24 18:51:59.749 [error] 'FuzzySymbolSearcher' Failed to read file tokens for dc549353cda133832f348760568f19c676225085e3574d1ffebe1a7adee8c543: deleted
2025-06-24 18:52:00.776 [info] 'ToolFileUtils' Reading file: quickshell/modules/backgroundWidgets/BackgroundWidgets.qml
2025-06-24 18:52:00.776 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/backgroundWidgets/BackgroundWidgets.qml (6445 bytes)
2025-06-24 18:52:04.749 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:52:09.492 [info] 'WorkspaceManager[.config]' Directory created: quickshell/state
2025-06-24 18:52:21.028 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:52:42.722 [error] 'AugmentExtension' API request 7fd539cc-8052-413a-b3d9-53f181459bfd to https://i0.api.augmentcode.com/client-metrics failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:52:43.225 [error] 'AugmentExtension' API request a8f65896-2238-44e4-99ec-02ecd2cf5596 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:52:43.226 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:52:43.226 [error] 'ClientMetricsReporter' Error uploading metrics: Error: fetch failed Error: fetch failed
    at e.transientIssue (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:13968)
    at wM.callApi (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:12446)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.callApi (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:57610)
    at wM.clientMetrics (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:52204)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:18561
    at Qs (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:16773)
    at e._doUpload (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:18460)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:17783
2025-06-24 18:52:43.226 [info] 'ClientMetricsReporter' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 18:52:43.327 [info] 'ViewTool' Tool called with path: quickshell/services/HyprlandData.qml and view_range: [40,70]
2025-06-24 18:52:43.973 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-06-24 18:52:56.769 [info] 'ToolFileUtils' Reading file: quickshell/services/HyprlandData.qml
2025-06-24 18:52:56.769 [info] 'ToolFileUtils' Successfully read file: quickshell/services/HyprlandData.qml (1876 bytes)
2025-06-24 18:52:57.815 [info] 'ToolFileUtils' Reading file: quickshell/services/HyprlandData.qml
2025-06-24 18:52:57.815 [info] 'ToolFileUtils' Successfully read file: quickshell/services/HyprlandData.qml (2336 bytes)
2025-06-24 18:53:01.777 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:53:17.347 [info] 'ToolFileUtils' Reading file: quickshell/services/HyprlandData.qml
2025-06-24 18:53:17.347 [info] 'ToolFileUtils' Successfully read file: quickshell/services/HyprlandData.qml (2336 bytes)
2025-06-24 18:53:17.351 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 1de4bf2d06e3adab483066e19a03db9703cb6c42fc2829ea206842f1edbc29b0: deleted
2025-06-24 18:53:18.376 [info] 'ToolFileUtils' Reading file: quickshell/services/HyprlandData.qml
2025-06-24 18:53:18.376 [info] 'ToolFileUtils' Successfully read file: quickshell/services/HyprlandData.qml (2770 bytes)
2025-06-24 18:53:22.373 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:53:26.136 [info] 'ViewTool' Tool called with path: quickshell/services/Ai.qml and view_range: [695,715]
2025-06-24 18:53:54.931 [info] 'ViewTool' Tool called with path: quickshell/modules/settings/AiConfig.qml and view_range: [1,15]
2025-06-24 18:54:01.420 [info] 'ViewTool' Tool called with path: quickshell/modules/common/widgets/ConfirmDialog.qml and view_range: [1,20]
2025-06-24 18:54:09.968 [info] 'ToolFileUtils' Reading file: quickshell/modules/common/widgets/ConfirmDialog.qml
2025-06-24 18:54:09.969 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/common/widgets/ConfirmDialog.qml (1917 bytes)
2025-06-24 18:54:10.141 [info] 'WorkspaceManager[.config]' Directory created: Code/User/History/620ab389
2025-06-24 18:54:11.014 [info] 'ToolFileUtils' Reading file: quickshell/modules/common/widgets/ConfirmDialog.qml
2025-06-24 18:54:11.015 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/common/widgets/ConfirmDialog.qml (1919 bytes)
2025-06-24 18:54:14.972 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:54:21.390 [info] 'ToolFileUtils' Reading file: quickshell/modules/common/widgets/ConfirmDialog.qml
2025-06-24 18:54:21.390 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/common/widgets/ConfirmDialog.qml (1919 bytes)
2025-06-24 18:54:21.394 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 95e5577b18e202bb067a858520697d18b23a3d43163fcf09361996ced1a849b8: deleted
2025-06-24 18:54:22.427 [info] 'ToolFileUtils' Reading file: quickshell/modules/common/widgets/ConfirmDialog.qml
2025-06-24 18:54:22.427 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/common/widgets/ConfirmDialog.qml (1925 bytes)
2025-06-24 18:54:26.406 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:54:34.039 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:54:34.039 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (22376 bytes)
2025-06-24 18:54:35.088 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:54:35.088 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (22382 bytes)
2025-06-24 18:54:39.044 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:54:43.679 [info] 'ViewTool' Tool called with path: quickshell/modules/settings/AiConfig.qml and view_range: [580,613]
2025-06-24 18:54:52.469 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:54:52.469 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (22382 bytes)
2025-06-24 18:54:52.474 [error] 'FuzzySymbolSearcher' Failed to read file tokens for bf843afb2790615e66f12fdd3657bff89f593ced7a51072dd7bb2590d410d68c: deleted
2025-06-24 18:54:53.497 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:54:53.498 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (22388 bytes)
2025-06-24 18:54:57.487 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:55:31.812 [info] 'ViewTool' Tool called with path: quickshell/modules/settings/AiConfig.qml and view_range: [160,170]
2025-06-24 18:56:21.546 [error] 'FuzzySymbolSearcher' Failed to read file tokens for ed0a6bf7178818443d15c6791cb78206da7b35a2a400d9a432dcddc625bbe66b: deleted
2025-06-24 18:56:26.547 [error] 'FuzzySymbolSearcher' Failed to read file tokens for ed0a6bf7178818443d15c6791cb78206da7b35a2a400d9a432dcddc625bbe66b: deleted
2025-06-24 18:56:26.547 [error] 'FuzzySymbolSearcher' Failed to read file tokens for ed0a6bf7178818443d15c6791cb78206da7b35a2a400d9a432dcddc625bbe66b: deleted
2025-06-24 18:56:34.755 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:56:34.755 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (22388 bytes)
2025-06-24 18:56:35.795 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:56:35.795 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (22984 bytes)
2025-06-24 18:56:39.760 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:56:46.550 [info] 'StallDetector' Recent work: [{"name":"find-symbol-request","durationMs":10928.753875,"timestamp":"2025-06-24T10:56:46.548Z"},{"name":"find-symbol-request","durationMs":10928.480259,"timestamp":"2025-06-24T10:56:46.548Z"},{"name":"find-symbol-request","durationMs":10928.241461,"timestamp":"2025-06-24T10:56:46.548Z"},{"name":"find-symbol-request","durationMs":10927.995707,"timestamp":"2025-06-24T10:56:46.548Z"},{"name":"find-symbol-request","durationMs":10927.744311,"timestamp":"2025-06-24T10:56:46.548Z"},{"name":"find-symbol-request","durationMs":10927.500747,"timestamp":"2025-06-24T10:56:46.548Z"}]
2025-06-24 18:56:47.685 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:56:47.685 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (22984 bytes)
2025-06-24 18:56:47.690 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 79d37d3a2f1c5bda423ca98559050ad54d1f622a7463fce7685cc6bbdb071b68: deleted
2025-06-24 18:56:48.727 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:56:48.727 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (22984 bytes)
2025-06-24 18:56:52.714 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:57:02.670 [error] 'FuzzySymbolSearcher' Failed to read file tokens for cfae6b8f9c9f1419e0e83147a067d50904017bbf853b6e038e05363f280feb52: deleted
2025-06-24 18:57:02.670 [error] 'FuzzySymbolSearcher' Failed to read file tokens for cfae6b8f9c9f1419e0e83147a067d50904017bbf853b6e038e05363f280feb52: deleted
2025-06-24 18:57:02.670 [error] 'FuzzySymbolSearcher' Failed to read file tokens for cfae6b8f9c9f1419e0e83147a067d50904017bbf853b6e038e05363f280feb52: deleted
2025-06-24 18:57:02.670 [error] 'FuzzySymbolSearcher' Failed to read file tokens for cfae6b8f9c9f1419e0e83147a067d50904017bbf853b6e038e05363f280feb52: deleted
2025-06-24 18:57:02.670 [error] 'FuzzySymbolSearcher' Failed to read file tokens for cfae6b8f9c9f1419e0e83147a067d50904017bbf853b6e038e05363f280feb52: deleted
2025-06-24 18:57:02.706 [info] 'StallDetector' Recent work: [{"name":"find-symbol-request","durationMs":18458.244911,"timestamp":"2025-06-24T10:57:02.670Z"},{"name":"find-symbol-request","durationMs":17834.864083,"timestamp":"2025-06-24T10:57:02.670Z"}]
2025-06-24 18:57:23.904 [info] 'ViewTool' Tool called with path: quickshell/modules/common/widgets/StyledTextInput.qml and view_range: undefined
2025-06-24 18:57:40.849 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:57:40.854 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (22984 bytes)
2025-06-24 18:57:41.908 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 18:57:41.908 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (23316 bytes)
2025-06-24 18:57:45.861 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:57:45.868 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 8ee6e6bcd0c5ffbbecf0b7a79b20303f1c829b66b0172cbe8199606300610564: deleted
2025-06-24 18:58:20.216 [info] 'AugmentExtension' Retrieving model config
2025-06-24 18:58:21.045 [info] 'AugmentExtension' Retrieved model config
2025-06-24 18:58:21.045 [info] 'AugmentExtension' Returning model config
2025-06-24 18:59:31.726 [error] 'AugmentExtension' API request 8c73177c-22a7-438d-95ff-cb0610906ffa to https://i0.api.augmentcode.com/batch-upload failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:59:32.231 [error] 'AugmentExtension' API request 41ab708b-e532-4994-91b9-18ef2c727aa2 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:59:32.231 [error] 'AugmentExtension' Dropping error report "batch-upload call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:59:32.231 [info] 'DiskFileManager[.config]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 18:59:33.171 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
2025-06-24 18:59:43.743 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 18:59:46.378 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 13c15d38e41222efa65f5d1678dd85c40b2a7113fb10b8b46329ecd06bece7a1: deleted
2025-06-24 18:59:51.158 [info] 'ViewTool' Tool called with path: quickshell/state/user/config.json and view_range: [-50,-1]
2025-06-24 18:59:51.159 [info] 'ViewTool' Path does not exist: quickshell/state/user/config.json
2025-06-24 18:59:51.192 [info] 'ToolFileUtils' File not found: quickshell/state/user/config.json. Similar files found:
/home/<USER>/.config/CherryStudio/config.json
/home/<USER>/.config/illogical-impulse/config.json
2025-06-24 19:00:12.999 [info] 'ViewTool' Tool called with path: ../illogical-impulse/config.json and view_range: [-50,-1]
2025-06-24 19:00:13.000 [info] 'ViewTool' Path does not exist: ../illogical-impulse/config.json
2025-06-24 19:00:13.038 [info] 'ToolFileUtils' File not found: ../illogical-impulse/config.json. Similar files found:
/home/<USER>/.config/illogical-impulse/config.json
/home/<USER>/.config/CherryStudio/config.json
2025-06-24 19:00:17.936 [info] 'ViewTool' Tool called with path: /home/<USER>/.config/illogical-impulse/config.json and view_range: [-50,-1]
2025-06-24 19:00:24.669 [info] 'ViewTool' Tool called with path: quickshell/services/ConfigLoader.qml and view_range: [30,50]
2025-06-24 19:00:41.232 [info] 'ToolFileUtils' Reading file: quickshell/services/ConfigLoader.qml
2025-06-24 19:00:41.232 [info] 'ToolFileUtils' Successfully read file: quickshell/services/ConfigLoader.qml (4880 bytes)
2025-06-24 19:00:42.275 [info] 'ToolFileUtils' Reading file: quickshell/services/ConfigLoader.qml
2025-06-24 19:00:42.275 [info] 'ToolFileUtils' Successfully read file: quickshell/services/ConfigLoader.qml (6040 bytes)
2025-06-24 19:00:46.238 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 19:00:56.105 [info] 'ToolFileUtils' Reading file: quickshell/services/ConfigLoader.qml
2025-06-24 19:00:56.105 [info] 'ToolFileUtils' Successfully read file: quickshell/services/ConfigLoader.qml (6040 bytes)
2025-06-24 19:00:56.110 [error] 'FuzzySymbolSearcher' Failed to read file tokens for e439ae2a7d92dd70c70174953e496c297ab34207515a230c30d245ceabbe588a: deleted
2025-06-24 19:00:57.131 [info] 'ToolFileUtils' Reading file: quickshell/services/ConfigLoader.qml
2025-06-24 19:00:57.131 [info] 'ToolFileUtils' Successfully read file: quickshell/services/ConfigLoader.qml (6168 bytes)
2025-06-24 19:01:01.109 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 19:02:10.715 [info] 'ToolFileUtils' Reading file: quickshell/modules/common/ConfigOptions.qml
2025-06-24 19:02:10.720 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/common/ConfigOptions.qml (6691 bytes)
2025-06-24 19:02:10.934 [info] 'WorkspaceManager[.config]' Directory created: Code/User/History/669de0bd
2025-06-24 19:02:11.768 [info] 'ToolFileUtils' Reading file: quickshell/modules/common/ConfigOptions.qml
2025-06-24 19:02:11.768 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/common/ConfigOptions.qml (6887 bytes)
2025-06-24 19:02:15.724 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 19:03:12.704 [error] 'AugmentExtensionSidecar' API request 969eb5c3-39b3-4e1b-9df4-017e099f8be1 to https://i0.api.augmentcode.com/chat-stream failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 19:03:12.705 [error] 'AugmentExtensionSidecar' TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.OJ.globalThis.fetch (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:21986)
    at LA (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:17177)
    at wM.callApiStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:518:4046)
    at wM.callApiStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:58439)
    at wM.chatStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:17794)
    at e.startChatStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:727:35710)
    at e.chatStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:727:33876)
    at pC.onUserSendMessage (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1810:3226)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 19:03:13.208 [error] 'AugmentExtension' API request c7cbc238-c3e7-439a-a4e8-5aab973988b3 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 19:03:13.208 [error] 'AugmentExtension' Dropping error report "chat-stream call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 19:03:13.209 [error] 'ChatApp' Chat stream failed: Error: fetch failed
Error: fetch failed
    at e.transientIssue (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:13968)
    at wM.callApiStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:518:4279)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.callApiStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:58439)
    at wM.chatStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:17794)
    at e.startChatStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:727:35710)
    at e.chatStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:727:33876)
    at pC.onUserSendMessage (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1810:3226)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 19:03:13.720 [error] 'AugmentExtension' API request b5f8a721-7acc-42ab-a6b2-97695f67fffe to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 19:03:13.720 [error] 'AugmentExtension' Dropping error report "chat_stream_failed" due to error: This operation was aborted
2025-06-24 19:05:14.593 [info] 'WorkspaceManager[.config]' Directory created: Code/blob_storage/823ed029-4fa2-456f-8195-4c173333f318
2025-06-24 19:05:14.593 [info] 'WorkspaceManager[.config]' Directory removed: Code/blob_storage/1d50be04-60ef-4e36-a60b-97264c5c2cbe
2025-06-24 19:05:14.595 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T190514
2025-06-24 19:05:14.829 [info] 'WorkspaceManager[.config]' Directory created: Code/User/workspaceStorage/1750763114469
2025-06-24 19:05:14.830 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window3
2025-06-24 19:05:15.502 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window3/exthost
2025-06-24 19:05:15.848 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window3/exthost/vscode.github-authentication
2025-06-24 19:05:15.850 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window3/output_20250624T190515
2025-06-24 19:05:16.418 [info] 'WorkspaceManager[.config]' Directory created: sparkle/blob_storage/7e7a50ec-dff6-4db1-9053-61d6bec684cd
2025-06-24 19:05:16.418 [info] 'WorkspaceManager[.config]' Directory removed: sparkle/blob_storage/1cafcc1f-2367-42c1-980b-350f5f61d766
2025-06-24 19:05:16.789 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window3/exthost/output_logging_20250624T190515
2025-06-24 19:05:17.040 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window3/exthost/vscode.git
2025-06-24 19:05:17.041 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window3/exthost/vscode.json-language-features
2025-06-24 19:05:17.041 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window3/exthost/vscode.microsoft-authentication
2025-06-24 19:05:18.839 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window3/exthost/ms-dotnettools.vscode-dotnet-runtime
2025-06-24 19:05:19.293 [info] 'WorkspaceManager[.config]' Directory removed: Code/User/globalStorage/github.vscode-pull-request-github/temp
2025-06-24 19:05:19.551 [info] 'WorkspaceManager[.config]' Directory created: Code/User/globalStorage/github.vscode-pull-request-github/temp
2025-06-24 19:05:19.552 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window3/exthost/Augment.vscode-augment
2025-06-24 19:05:19.552 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window3/exthost/GitHub.copilot
2025-06-24 19:05:19.552 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window3/exthost/vscode.github
2025-06-24 19:05:19.767 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window3/exthost/GitHub.copilot-chat
2025-06-24 19:05:19.768 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window3/exthost/GitHub.vscode-pull-request-github
2025-06-24 19:05:20.168 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182813/window3/exthost/ms-vscode-remote.remote-containers
2025-06-24 19:06:04.967 [info] 'WorkspaceManager[.config]' Directory created: microsoft-edge/Edge Designer
2025-06-24 19:06:05.291 [info] 'WorkspaceManager[.config]' Directory removed: microsoft-edge/Edge Designer
2025-06-24 19:06:05.925 [info] 'WorkspaceManager[.config]' Directory created: microsoft-edge/Default/blob_storage/350d7b2b-15ec-4f03-90b8-766a8b081b84
2025-06-24 19:06:10.487 [info] 'WorkspaceManager[.config]' Directory removed: microsoft-edge/Default/blob_storage/e8215009-7b73-4dea-9969-15b6868220c0
2025-06-24 19:06:19.480 [info] 'WorkspaceManager[.config]' Directory created: microsoft-edge/Crash Reports/temp
2025-06-24 19:06:45.631 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 19:06:52.900 [error] 'AugmentExtension' API request 33ec41a9-70b9-48bf-aa09-aa19f5f4893e to https://i0.api.augmentcode.com/record-session-events failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 19:06:53.448 [error] 'AugmentExtension' API request 317bcc0c-2c5e-4b91-abed-65ac7cbd3a92 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 19:06:53.448 [error] 'AugmentExtension' Dropping error report "record-session-events call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 19:06:53.448 [error] 'OnboardingSessionEventReporter' Error uploading metrics: Error: fetch failed Error: fetch failed
    at e.transientIssue (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:13968)
    at wM.callApi (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:12446)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.callApi (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:57610)
    at wM.logOnboardingSessionEvent (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:34153)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:18561
    at Qs (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:16773)
    at e._doUpload (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:18460)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:17783
2025-06-24 19:06:53.448 [info] 'OnboardingSessionEventReporter' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 19:06:54.410 [info] 'OnboardingSessionEventReporter' Operation succeeded after 1 transient failures
2025-06-24 19:07:17.035 [info] 'ViewTool' Tool called with path: quickshell/modules/settings/AiConfig.qml and view_range: [315,395]
2025-06-24 19:07:19.558 [info] 'WorkspaceManager[.config]' Directory created: microsoft-edge/Crash Reports/temp/d7a480f9-514a-428c-affe-07f2d366363d
2025-06-24 19:07:20.548 [error] 'FuzzySymbolSearcher' Failed to read file tokens for e81b9a01b540bdf69adb225f5062ac48841be427cdd956f392b6426b57851b0f: deleted
2025-06-24 19:07:21.259 [error] 'FuzzySymbolSearcher' Failed to read file tokens for e81b9a01b540bdf69adb225f5062ac48841be427cdd956f392b6426b57851b0f: deleted
2025-06-24 19:07:26.260 [error] 'FuzzySymbolSearcher' Failed to read file tokens for e81b9a01b540bdf69adb225f5062ac48841be427cdd956f392b6426b57851b0f: deleted
2025-06-24 19:07:31.763 [error] 'FuzzySymbolSearcher' Failed to read file tokens for e81b9a01b540bdf69adb225f5062ac48841be427cdd956f392b6426b57851b0f: deleted
2025-06-24 19:07:36.099 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 19:07:36.099 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (23316 bytes)
2025-06-24 19:07:37.147 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 19:07:37.147 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (23929 bytes)
2025-06-24 19:07:41.109 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 19:07:57.495 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 19:07:57.495 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (23929 bytes)
2025-06-24 19:07:58.533 [info] 'ToolFileUtils' Reading file: quickshell/modules/settings/AiConfig.qml
2025-06-24 19:07:58.533 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/settings/AiConfig.qml (24155 bytes)
2025-06-24 19:08:02.499 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 19:08:17.614 [info] 'ToolFileUtils' Reading file: quickshell/services/Ai.qml
2025-06-24 19:08:17.614 [info] 'ToolFileUtils' Successfully read file: quickshell/services/Ai.qml (35377 bytes)
2025-06-24 19:08:18.654 [info] 'ToolFileUtils' Reading file: quickshell/services/Ai.qml
2025-06-24 19:08:18.654 [info] 'ToolFileUtils' Successfully read file: quickshell/services/Ai.qml (35585 bytes)
2025-06-24 19:08:19.543 [info] 'WorkspaceManager[.config]' Directory removed: microsoft-edge/Crash Reports/temp/d7a480f9-514a-428c-affe-07f2d366363d
2025-06-24 19:08:22.619 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 19:08:47.407 [info] 'ToolFileUtils' Reading file: quickshell/modules/common/ConfigOptions.qml
2025-06-24 19:08:47.410 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/common/ConfigOptions.qml (6887 bytes)
2025-06-24 19:08:48.505 [info] 'ToolFileUtils' Reading file: quickshell/modules/common/ConfigOptions.qml
2025-06-24 19:08:48.506 [info] 'ToolFileUtils' Successfully read file: quickshell/modules/common/ConfigOptions.qml (6927 bytes)
2025-06-24 19:08:52.413 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 19:08:57.589 [info] 'ViewTool' Tool called with path: quickshell/services/Ai.qml and view_range: [1,50]
2025-06-24 19:09:46.711 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1345.682146,"timestamp":"2025-06-24T11:09:46.682Z"}]
2025-06-24 19:10:50.094 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 43c8625a3fc3eeb9af8504f9d530f05df8f3302fa60251487804bd80c5167380: deleted
2025-06-24 19:12:31.448 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 08ea7ad3b2192a9ec62b523cd61686078e65c548e50055c5a358b7392c4f1e2e: deleted
2025-06-24 19:12:31.454 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 19:12:41.576 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 08ea7ad3b2192a9ec62b523cd61686078e65c548e50055c5a358b7392c4f1e2e: deleted
2025-06-24 19:12:46.575 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 08ea7ad3b2192a9ec62b523cd61686078e65c548e50055c5a358b7392c4f1e2e: deleted
2025-06-24 19:12:46.580 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 19:12:46.715 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1324.059516,"timestamp":"2025-06-24T11:12:46.662Z"}]
2025-06-24 19:13:35.354 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 79ac60df4bce9e4e6dc66b27d4002a30f945683119f4899b3eb22294271ddefd: deleted
2025-06-24 19:13:39.580 [error] 'AugmentExtension' API request ca4c9d66-e016-4c81-b123-2ce3c4972281 to https://i0.api.augmentcode.com/find-missing failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 19:13:40.090 [error] 'AugmentExtension' API request 8d40a2ce-4c27-483c-a3f8-6c761302f0b3 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 19:13:40.090 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 19:13:40.090 [info] 'DiskFileManager[.config]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 19:13:40.355 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 79ac60df4bce9e4e6dc66b27d4002a30f945683119f4899b3eb22294271ddefd: deleted
2025-06-24 19:13:40.369 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 19:13:41.070 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
2025-06-24 19:13:46.674 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-24 19:14:12.862 [info] 'WorkspaceManager[.config]' Directory created: Code/WebStorage/5
2025-06-24 19:14:14.294 [info] 'WorkspaceManager[.config]' Directory created: Code/User/globalStorage/rooveterinaryinc.roo-cline/cache
2025-06-24 19:14:58.396 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
