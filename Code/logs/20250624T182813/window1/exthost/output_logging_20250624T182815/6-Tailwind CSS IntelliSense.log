Locating server…
Booting server...
Setting up server…
Listening for messages…
Searching for Tailwind CSS projects in the workspace's folders.
hoist-at-import: The file '/home/<USER>/.config/gtk-3.0-bk/gtk.css' contains @import rules after other at rules. This is invalid CSS and may cause problems with your build.
hoist-at-import: The file '/home/<USER>/.config/gtk-3.0/gtk.css' contains @import rules after other at rules. This is invalid CSS and may cause problems with your build.
hoist-at-import: The file '/home/<USER>/.config/gtk-4.0-bk/gtk.css' contains @import rules after other at rules. This is invalid CSS and may cause problems with your build.
hoist-at-import: The file '/home/<USER>/.config/gtk-4.0/gtk.css' contains @import rules after other at rules. This is invalid CSS and may cause problems with your build.
[Info  - 18:28:23] Unable to resolve imports for /home/<USER>/.config/microsoft-edge/Profile 2/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/main.css.
[Info  - 18:28:23] This may result in failure to locate Tailwind CSS projects.
[Error - 18:28:23] e [CssSyntaxError]: postcss-import: /home/<USER>/.config/microsoft-edge/Profile 2/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/fonts.css:4:52: Unknown word format
    at Zm.error (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:198:1445)
    at CR.unknownWord (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:203:10065)
    at CR.decl (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:203:5548)
    at CR.other (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:203:8032)
    at CR.parse (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:203:8787)
    at US (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:203:10392)
    at get root (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:206:8703)
    at H0.get [as root] (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:206:9111)
    at pae (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:253:1397)
    at async Promise.all (index 0) {
  reason: 'Unknown word format',
  file: '/home/<USER>/.config/microsoft-edge/Profile 2/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/fonts.css',
  source: '@charset "UTF-8";\n' +
    '@font-face {\n' +
    "    font-family: 'Open Sans';\n" +
    "    url('../fonts/opensans-regular-webfont.woff2') format('woff2'),\n" +
    '    font-weight: normal;\n' +
    '    font-style: normal;\n' +
    '}\n' +
    '\n' +
    '@font-face {\n' +
    "    font-family: 'Open Sans';\n" +
    "    url('../fonts/opensans-semibold-webfont.woff2') format('woff2'),\n" +
    '    font-weight: 600;\n' +
    '    font-style: normal;\n' +
    '}\n' +
    '\n' +
    '@font-face {\n' +
    "    font-family: 'Open Sans';\n" +
    "    url('../fonts/opensans-bold-webfont.woff2') format('woff2'),\n" +
    '    font-weight: bold;\n' +
    '    font-style: normal;\n' +
    '}\n' +
    '\n' +
    '@font-face {\n' +
    "    font-family: 'Roboto Flex';\n" +
    "    src: url('../fonts/Roboto-Flex-Regular.woff2') format('woff2 supports variations'),\n" +
    "       url('../fonts/Roboto-Flex-Regular.woff2') format('woff2-variations');\n" +
    '    font-weight: 100 1000;\n' +
    '    font-stretch: 25% 151%;\n' +
    '}\n',
  line: 4,
  column: 52,
  endLine: 4,
  endColumn: 58,
  input: {
    column: 52,
    endColumn: 58,
    endLine: 4,
    endOffset: 118,
    line: 4,
    offset: 112,
    source: '@charset "UTF-8";\n' +
      '@font-face {\n' +
      "    font-family: 'Open Sans';\n" +
      "    url('../fonts/opensans-regular-webfont.woff2') format('woff2'),\n" +
      '    font-weight: normal;\n' +
      '    font-style: normal;\n' +
      '}\n' +
      '\n' +
      '@font-face {\n' +
      "    font-family: 'Open Sans';\n" +
      "    url('../fonts/opensans-semibold-webfont.woff2') format('woff2'),\n" +
      '    font-weight: 600;\n' +
      '    font-style: normal;\n' +
      '}\n' +
      '\n' +
      '@font-face {\n' +
      "    font-family: 'Open Sans';\n" +
      "    url('../fonts/opensans-bold-webfont.woff2') format('woff2'),\n" +
      '    font-weight: bold;\n' +
      '    font-style: normal;\n' +
      '}\n' +
      '\n' +
      '@font-face {\n' +
      "    font-family: 'Roboto Flex';\n" +
      "    src: url('../fonts/Roboto-Flex-Regular.woff2') format('woff2 supports variations'),\n" +
      "       url('../fonts/Roboto-Flex-Regular.woff2') format('woff2-variations');\n" +
      '    font-weight: 100 1000;\n' +
      '    font-stretch: 25% 151%;\n' +
      '}\n',
    url: 'file:///home/<USER>/.config/microsoft-edge/Profile%202/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/fonts.css',
    file: '/home/<USER>/.config/microsoft-edge/Profile 2/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/fonts.css'
  },
  plugin: 'postcss-import'
}
[Info  - 18:28:23] Unable to resolve imports for /home/<USER>/.config/microsoft-edge/Default/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/main.css.
[Info  - 18:28:23] This may result in failure to locate Tailwind CSS projects.
[Error - 18:28:23] e [CssSyntaxError]: postcss-import: /home/<USER>/.config/microsoft-edge/Default/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/fonts.css:4:52: Unknown word format
    at Zm.error (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:198:1445)
    at CR.unknownWord (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:203:10065)
    at CR.decl (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:203:5548)
    at CR.other (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:203:8032)
    at CR.parse (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:203:8787)
    at US (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:203:10392)
    at get root (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:206:8703)
    at H0.get [as root] (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:206:9111)
    at pae (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:253:1397)
    at async Promise.all (index 0) {
  reason: 'Unknown word format',
  file: '/home/<USER>/.config/microsoft-edge/Default/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/fonts.css',
  source: '@charset "UTF-8";\n' +
    '@font-face {\n' +
    "    font-family: 'Open Sans';\n" +
    "    url('../fonts/opensans-regular-webfont.woff2') format('woff2'),\n" +
    '    font-weight: normal;\n' +
    '    font-style: normal;\n' +
    '}\n' +
    '\n' +
    '@font-face {\n' +
    "    font-family: 'Open Sans';\n" +
    "    url('../fonts/opensans-semibold-webfont.woff2') format('woff2'),\n" +
    '    font-weight: 600;\n' +
    '    font-style: normal;\n' +
    '}\n' +
    '\n' +
    '@font-face {\n' +
    "    font-family: 'Open Sans';\n" +
    "    url('../fonts/opensans-bold-webfont.woff2') format('woff2'),\n" +
    '    font-weight: bold;\n' +
    '    font-style: normal;\n' +
    '}\n' +
    '\n' +
    '@font-face {\n' +
    "    font-family: 'Roboto Flex';\n" +
    "    src: url('../fonts/Roboto-Flex-Regular.woff2') format('woff2 supports variations'),\n" +
    "       url('../fonts/Roboto-Flex-Regular.woff2') format('woff2-variations');\n" +
    '    font-weight: 100 1000;\n' +
    '    font-stretch: 25% 151%;\n' +
    '}\n',
  line: 4,
  column: 52,
  endLine: 4,
  endColumn: 58,
  input: {
    column: 52,
    endColumn: 58,
    endLine: 4,
    endOffset: 118,
    line: 4,
    offset: 112,
    source: '@charset "UTF-8";\n' +
      '@font-face {\n' +
      "    font-family: 'Open Sans';\n" +
      "    url('../fonts/opensans-regular-webfont.woff2') format('woff2'),\n" +
      '    font-weight: normal;\n' +
      '    font-style: normal;\n' +
      '}\n' +
      '\n' +
      '@font-face {\n' +
      "    font-family: 'Open Sans';\n" +
      "    url('../fonts/opensans-semibold-webfont.woff2') format('woff2'),\n" +
      '    font-weight: 600;\n' +
      '    font-style: normal;\n' +
      '}\n' +
      '\n' +
      '@font-face {\n' +
      "    font-family: 'Open Sans';\n" +
      "    url('../fonts/opensans-bold-webfont.woff2') format('woff2'),\n" +
      '    font-weight: bold;\n' +
      '    font-style: normal;\n' +
      '}\n' +
      '\n' +
      '@font-face {\n' +
      "    font-family: 'Roboto Flex';\n" +
      "    src: url('../fonts/Roboto-Flex-Regular.woff2') format('woff2 supports variations'),\n" +
      "       url('../fonts/Roboto-Flex-Regular.woff2') format('woff2-variations');\n" +
      '    font-weight: 100 1000;\n' +
      '    font-stretch: 25% 151%;\n' +
      '}\n',
    url: 'file:///home/<USER>/.config/microsoft-edge/Default/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/fonts.css',
    file: '/home/<USER>/.config/microsoft-edge/Default/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/fonts.css'
  },
  plugin: 'postcss-import'
}
{"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true},"path":"/home/<USER>/.config/gtk-3.0-bk/gtk.css"}
{"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true},"path":"/home/<USER>/.config/gtk-3.0/gtk.css"}
{"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true},"path":"/home/<USER>/.config/gtk-4.0-bk/gtk.css"}
{"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true},"path":"/home/<USER>/.config/gtk-4.0/gtk.css"}
{"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true},"path":"/home/<USER>/.config/gtk-3.0-bk/gtk-3.0/gtk.css"}
{"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true},"path":"/home/<USER>/.config/gtk-4.0-bk/gtk-4.0/gtk.css"}
{"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true},"path":"/home/<USER>/.config/microsoft-edge/Default/Extensions/elfaihghhjjoknimpccccmkioofjjfkf/2.2.11_0/assets/new-tab-5CJd5kwi.css"}
{"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true},"path":"/home/<USER>/.config/microsoft-edge/Default/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/main.css"}
{"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true},"path":"/home/<USER>/.config/microsoft-edge/Profile 2/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/main.css"}
[Global] Creating projects: [{"folder":"/home/<USER>/.config","config":"/home/<USER>/.config/gtk-3.0-bk/gtk-3.0/gtk.css","selectors":[{"pattern":"/home/<USER>/.config/gtk-3.0-bk/gtk-3.0/gtk.css","priority":0},{"pattern":"/home/<USER>/.config/gtk-3.0-bk/gtk-3.0/**","priority":2},{"pattern":"/home/<USER>/.config/**","priority":4}],"user":false,"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true}},{"folder":"/home/<USER>/.config","config":"/home/<USER>/.config/gtk-3.0-bk/gtk.css","selectors":[{"pattern":"/home/<USER>/.config/gtk-3.0-bk/gtk.css","priority":0},{"pattern":"/home/<USER>/.config/gtk-3.0-bk/**","priority":2},{"pattern":"/home/<USER>/.config/**","priority":4}],"user":false,"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true}},{"folder":"/home/<USER>/.config","config":"/home/<USER>/.config/gtk-3.0/gtk.css","selectors":[{"pattern":"/home/<USER>/.config/gtk-3.0/gtk.css","priority":0},{"pattern":"/home/<USER>/.config/gtk-3.0/**","priority":2},{"pattern":"/home/<USER>/.config/**","priority":4}],"user":false,"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true}},{"folder":"/home/<USER>/.config","config":"/home/<USER>/.config/gtk-4.0-bk/gtk-4.0/gtk.css","selectors":[{"pattern":"/home/<USER>/.config/gtk-4.0-bk/gtk-4.0/gtk.css","priority":0},{"pattern":"/home/<USER>/.config/gtk-4.0-bk/gtk-4.0/**","priority":2},{"pattern":"/home/<USER>/.config/**","priority":4}],"user":false,"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true}},{"folder":"/home/<USER>/.config","config":"/home/<USER>/.config/gtk-4.0-bk/gtk.css","selectors":[{"pattern":"/home/<USER>/.config/gtk-4.0-bk/gtk.css","priority":0},{"pattern":"/home/<USER>/.config/gtk-4.0-bk/**","priority":2},{"pattern":"/home/<USER>/.config/**","priority":4}],"user":false,"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true}},{"folder":"/home/<USER>/.config","config":"/home/<USER>/.config/gtk-4.0/gtk.css","selectors":[{"pattern":"/home/<USER>/.config/gtk-4.0/gtk.css","priority":0},{"pattern":"/home/<USER>/.config/gtk-4.0/**","priority":2},{"pattern":"/home/<USER>/.config/**","priority":4}],"user":false,"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true}},{"folder":"/home/<USER>/.config","config":"/home/<USER>/.config/microsoft-edge/Default/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/main.css","selectors":[{"pattern":"/home/<USER>/.config/microsoft-edge/Default/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/main.css","priority":0},{"pattern":"/home/<USER>/.config/microsoft-edge/Default/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/**","priority":2},{"pattern":"/home/<USER>/.config/**","priority":4}],"user":false,"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true}},{"folder":"/home/<USER>/.config","config":"/home/<USER>/.config/microsoft-edge/Profile 2/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/main.css","selectors":[{"pattern":"/home/<USER>/.config/microsoft-edge/Profile 2/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/main.css","priority":0},{"pattern":"/home/<USER>/.config/microsoft-edge/Profile 2/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/**","priority":2},{"pattern":"/home/<USER>/.config/**","priority":4}],"user":false,"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true}},{"folder":"/home/<USER>/.config","config":"/home/<USER>/.config/microsoft-edge/Default/Extensions/elfaihghhjjoknimpccccmkioofjjfkf/2.2.11_0/assets/new-tab-5CJd5kwi.css","selectors":[{"pattern":"/home/<USER>/.config/microsoft-edge/Default/Extensions/elfaihghhjjoknimpccccmkioofjjfkf/2.2.11_0/assets/new-tab-5CJd5kwi.css","priority":0},{"pattern":"/home/<USER>/.config/microsoft-edge/Default/Extensions/elfaihghhjjoknimpccccmkioofjjfkf/2.2.11_0/assets/**","priority":2},{"pattern":"/home/<USER>/.config/**","priority":4}],"user":false,"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true}}]
[Global] Preparing projects...
[Global] Initializing projects...
[Global] Initialized 0 projects
----------
RESTARTING
----------
Searching for Tailwind CSS projects in the workspace's folders.
hoist-at-import: The file '/home/<USER>/.config/gtk-3.0-bk/gtk.css' contains @import rules after other at rules. This is invalid CSS and may cause problems with your build.
hoist-at-import: The file '/home/<USER>/.config/gtk-3.0/gtk.css' contains @import rules after other at rules. This is invalid CSS and may cause problems with your build.
hoist-at-import: The file '/home/<USER>/.config/gtk-4.0-bk/gtk.css' contains @import rules after other at rules. This is invalid CSS and may cause problems with your build.
hoist-at-import: The file '/home/<USER>/.config/gtk-4.0/gtk.css' contains @import rules after other at rules. This is invalid CSS and may cause problems with your build.
[Info  - 18:29:03] Unable to resolve imports for /home/<USER>/.config/microsoft-edge/Profile 2/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/main.css.
[Info  - 18:29:03] This may result in failure to locate Tailwind CSS projects.
[Error - 18:29:03] e [CssSyntaxError]: postcss-import: /home/<USER>/.config/microsoft-edge/Profile 2/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/fonts.css:4:52: Unknown word format
    at Zm.error (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:198:1445)
    at CR.unknownWord (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:203:10065)
    at CR.decl (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:203:5548)
    at CR.other (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:203:8032)
    at CR.parse (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:203:8787)
    at US (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:203:10392)
    at get root (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:206:8703)
    at H0.get [as root] (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:206:9111)
    at pae (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:253:1397)
    at async Promise.all (index 0) {
  reason: 'Unknown word format',
  file: '/home/<USER>/.config/microsoft-edge/Profile 2/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/fonts.css',
  source: '@charset "UTF-8";\n' +
    '@font-face {\n' +
    "    font-family: 'Open Sans';\n" +
    "    url('../fonts/opensans-regular-webfont.woff2') format('woff2'),\n" +
    '    font-weight: normal;\n' +
    '    font-style: normal;\n' +
    '}\n' +
    '\n' +
    '@font-face {\n' +
    "    font-family: 'Open Sans';\n" +
    "    url('../fonts/opensans-semibold-webfont.woff2') format('woff2'),\n" +
    '    font-weight: 600;\n' +
    '    font-style: normal;\n' +
    '}\n' +
    '\n' +
    '@font-face {\n' +
    "    font-family: 'Open Sans';\n" +
    "    url('../fonts/opensans-bold-webfont.woff2') format('woff2'),\n" +
    '    font-weight: bold;\n' +
    '    font-style: normal;\n' +
    '}\n' +
    '\n' +
    '@font-face {\n' +
    "    font-family: 'Roboto Flex';\n" +
    "    src: url('../fonts/Roboto-Flex-Regular.woff2') format('woff2 supports variations'),\n" +
    "       url('../fonts/Roboto-Flex-Regular.woff2') format('woff2-variations');\n" +
    '    font-weight: 100 1000;\n' +
    '    font-stretch: 25% 151%;\n' +
    '}\n',
  line: 4,
  column: 52,
  endLine: 4,
  endColumn: 58,
  input: {
    column: 52,
    endColumn: 58,
    endLine: 4,
    endOffset: 118,
    line: 4,
    offset: 112,
    source: '@charset "UTF-8";\n' +
      '@font-face {\n' +
      "    font-family: 'Open Sans';\n" +
      "    url('../fonts/opensans-regular-webfont.woff2') format('woff2'),\n" +
      '    font-weight: normal;\n' +
      '    font-style: normal;\n' +
      '}\n' +
      '\n' +
      '@font-face {\n' +
      "    font-family: 'Open Sans';\n" +
      "    url('../fonts/opensans-semibold-webfont.woff2') format('woff2'),\n" +
      '    font-weight: 600;\n' +
      '    font-style: normal;\n' +
      '}\n' +
      '\n' +
      '@font-face {\n' +
      "    font-family: 'Open Sans';\n" +
      "    url('../fonts/opensans-bold-webfont.woff2') format('woff2'),\n" +
      '    font-weight: bold;\n' +
      '    font-style: normal;\n' +
      '}\n' +
      '\n' +
      '@font-face {\n' +
      "    font-family: 'Roboto Flex';\n" +
      "    src: url('../fonts/Roboto-Flex-Regular.woff2') format('woff2 supports variations'),\n" +
      "       url('../fonts/Roboto-Flex-Regular.woff2') format('woff2-variations');\n" +
      '    font-weight: 100 1000;\n' +
      '    font-stretch: 25% 151%;\n' +
      '}\n',
    url: 'file:///home/<USER>/.config/microsoft-edge/Profile%202/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/fonts.css',
    file: '/home/<USER>/.config/microsoft-edge/Profile 2/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/fonts.css'
  },
  plugin: 'postcss-import'
}
[Info  - 18:29:03] Unable to resolve imports for /home/<USER>/.config/microsoft-edge/Default/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/main.css.
[Info  - 18:29:03] This may result in failure to locate Tailwind CSS projects.
[Error - 18:29:03] e [CssSyntaxError]: postcss-import: /home/<USER>/.config/microsoft-edge/Default/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/fonts.css:4:52: Unknown word format
    at Zm.error (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:198:1445)
    at CR.unknownWord (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:203:10065)
    at CR.decl (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:203:5548)
    at CR.other (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:203:8032)
    at CR.parse (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:203:8787)
    at US (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:203:10392)
    at get root (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:206:8703)
    at H0.get [as root] (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:206:9111)
    at pae (/home/<USER>/.vscode/extensions/bradlc.vscode-tailwindcss-0.14.23/dist/tailwindServer.js:253:1397)
    at async Promise.all (index 0) {
  reason: 'Unknown word format',
  file: '/home/<USER>/.config/microsoft-edge/Default/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/fonts.css',
  source: '@charset "UTF-8";\n' +
    '@font-face {\n' +
    "    font-family: 'Open Sans';\n" +
    "    url('../fonts/opensans-regular-webfont.woff2') format('woff2'),\n" +
    '    font-weight: normal;\n' +
    '    font-style: normal;\n' +
    '}\n' +
    '\n' +
    '@font-face {\n' +
    "    font-family: 'Open Sans';\n" +
    "    url('../fonts/opensans-semibold-webfont.woff2') format('woff2'),\n" +
    '    font-weight: 600;\n' +
    '    font-style: normal;\n' +
    '}\n' +
    '\n' +
    '@font-face {\n' +
    "    font-family: 'Open Sans';\n" +
    "    url('../fonts/opensans-bold-webfont.woff2') format('woff2'),\n" +
    '    font-weight: bold;\n' +
    '    font-style: normal;\n' +
    '}\n' +
    '\n' +
    '@font-face {\n' +
    "    font-family: 'Roboto Flex';\n" +
    "    src: url('../fonts/Roboto-Flex-Regular.woff2') format('woff2 supports variations'),\n" +
    "       url('../fonts/Roboto-Flex-Regular.woff2') format('woff2-variations');\n" +
    '    font-weight: 100 1000;\n' +
    '    font-stretch: 25% 151%;\n' +
    '}\n',
  line: 4,
  column: 52,
  endLine: 4,
  endColumn: 58,
  input: {
    column: 52,
    endColumn: 58,
    endLine: 4,
    endOffset: 118,
    line: 4,
    offset: 112,
    source: '@charset "UTF-8";\n' +
      '@font-face {\n' +
      "    font-family: 'Open Sans';\n" +
      "    url('../fonts/opensans-regular-webfont.woff2') format('woff2'),\n" +
      '    font-weight: normal;\n' +
      '    font-style: normal;\n' +
      '}\n' +
      '\n' +
      '@font-face {\n' +
      "    font-family: 'Open Sans';\n" +
      "    url('../fonts/opensans-semibold-webfont.woff2') format('woff2'),\n" +
      '    font-weight: 600;\n' +
      '    font-style: normal;\n' +
      '}\n' +
      '\n' +
      '@font-face {\n' +
      "    font-family: 'Open Sans';\n" +
      "    url('../fonts/opensans-bold-webfont.woff2') format('woff2'),\n" +
      '    font-weight: bold;\n' +
      '    font-style: normal;\n' +
      '}\n' +
      '\n' +
      '@font-face {\n' +
      "    font-family: 'Roboto Flex';\n" +
      "    src: url('../fonts/Roboto-Flex-Regular.woff2') format('woff2 supports variations'),\n" +
      "       url('../fonts/Roboto-Flex-Regular.woff2') format('woff2-variations');\n" +
      '    font-weight: 100 1000;\n' +
      '    font-stretch: 25% 151%;\n' +
      '}\n',
    url: 'file:///home/<USER>/.config/microsoft-edge/Default/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/fonts.css',
    file: '/home/<USER>/.config/microsoft-edge/Default/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/fonts.css'
  },
  plugin: 'postcss-import'
}
{"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true},"path":"/home/<USER>/.config/gtk-3.0/gtk.css"}
{"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true},"path":"/home/<USER>/.config/gtk-3.0-bk/gtk.css"}
{"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true},"path":"/home/<USER>/.config/gtk-4.0-bk/gtk.css"}
{"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true},"path":"/home/<USER>/.config/gtk-4.0/gtk.css"}
{"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true},"path":"/home/<USER>/.config/gtk-3.0-bk/gtk-3.0/gtk.css"}
{"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true},"path":"/home/<USER>/.config/gtk-4.0-bk/gtk-4.0/gtk.css"}
{"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true},"path":"/home/<USER>/.config/microsoft-edge/Default/Extensions/elfaihghhjjoknimpccccmkioofjjfkf/2.2.11_0/assets/new-tab-5CJd5kwi.css"}
{"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true},"path":"/home/<USER>/.config/microsoft-edge/Default/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/main.css"}
{"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true},"path":"/home/<USER>/.config/microsoft-edge/Profile 2/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/main.css"}
[Global] Creating projects: [{"folder":"/home/<USER>/.config","config":"/home/<USER>/.config/gtk-3.0-bk/gtk-3.0/gtk.css","selectors":[{"pattern":"/home/<USER>/.config/gtk-3.0-bk/gtk-3.0/gtk.css","priority":0},{"pattern":"/home/<USER>/.config/gtk-3.0-bk/gtk-3.0/**","priority":2},{"pattern":"/home/<USER>/.config/**","priority":4}],"user":false,"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true}},{"folder":"/home/<USER>/.config","config":"/home/<USER>/.config/gtk-3.0-bk/gtk.css","selectors":[{"pattern":"/home/<USER>/.config/gtk-3.0-bk/gtk.css","priority":0},{"pattern":"/home/<USER>/.config/gtk-3.0-bk/**","priority":2},{"pattern":"/home/<USER>/.config/**","priority":4}],"user":false,"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true}},{"folder":"/home/<USER>/.config","config":"/home/<USER>/.config/gtk-3.0/gtk.css","selectors":[{"pattern":"/home/<USER>/.config/gtk-3.0/gtk.css","priority":0},{"pattern":"/home/<USER>/.config/gtk-3.0/**","priority":2},{"pattern":"/home/<USER>/.config/**","priority":4}],"user":false,"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true}},{"folder":"/home/<USER>/.config","config":"/home/<USER>/.config/gtk-4.0-bk/gtk-4.0/gtk.css","selectors":[{"pattern":"/home/<USER>/.config/gtk-4.0-bk/gtk-4.0/gtk.css","priority":0},{"pattern":"/home/<USER>/.config/gtk-4.0-bk/gtk-4.0/**","priority":2},{"pattern":"/home/<USER>/.config/**","priority":4}],"user":false,"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true}},{"folder":"/home/<USER>/.config","config":"/home/<USER>/.config/gtk-4.0-bk/gtk.css","selectors":[{"pattern":"/home/<USER>/.config/gtk-4.0-bk/gtk.css","priority":0},{"pattern":"/home/<USER>/.config/gtk-4.0-bk/**","priority":2},{"pattern":"/home/<USER>/.config/**","priority":4}],"user":false,"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true}},{"folder":"/home/<USER>/.config","config":"/home/<USER>/.config/gtk-4.0/gtk.css","selectors":[{"pattern":"/home/<USER>/.config/gtk-4.0/gtk.css","priority":0},{"pattern":"/home/<USER>/.config/gtk-4.0/**","priority":2},{"pattern":"/home/<USER>/.config/**","priority":4}],"user":false,"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true}},{"folder":"/home/<USER>/.config","config":"/home/<USER>/.config/microsoft-edge/Default/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/main.css","selectors":[{"pattern":"/home/<USER>/.config/microsoft-edge/Default/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/main.css","priority":0},{"pattern":"/home/<USER>/.config/microsoft-edge/Default/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/**","priority":2},{"pattern":"/home/<USER>/.config/**","priority":4}],"user":false,"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true}},{"folder":"/home/<USER>/.config","config":"/home/<USER>/.config/microsoft-edge/Profile 2/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/main.css","selectors":[{"pattern":"/home/<USER>/.config/microsoft-edge/Profile 2/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/main.css","priority":0},{"pattern":"/home/<USER>/.config/microsoft-edge/Profile 2/Extensions/pdffkfellgipmhklpdmokmckkkfcopbh/5.1.102_0/assets/css/**","priority":2},{"pattern":"/home/<USER>/.config/**","priority":4}],"user":false,"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true}},{"folder":"/home/<USER>/.config","config":"/home/<USER>/.config/microsoft-edge/Default/Extensions/elfaihghhjjoknimpccccmkioofjjfkf/2.2.11_0/assets/new-tab-5CJd5kwi.css","selectors":[{"pattern":"/home/<USER>/.config/microsoft-edge/Default/Extensions/elfaihghhjjoknimpccccmkioofjjfkf/2.2.11_0/assets/new-tab-5CJd5kwi.css","priority":0},{"pattern":"/home/<USER>/.config/microsoft-edge/Default/Extensions/elfaihghhjjoknimpccccmkioofjjfkf/2.2.11_0/assets/**","priority":2},{"pattern":"/home/<USER>/.config/**","priority":4}],"user":false,"tailwind":{"version":"4.1.1","features":["css-at-theme","layer:base","content-list","source-inline","source-not"],"isDefaultVersion":true}}]
[Global] Preparing projects...
[Global] Initializing projects...
[Global] Initialized 0 projects
