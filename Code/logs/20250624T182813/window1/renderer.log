2025-06-24 18:28:14.460 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vsliveshare.vsliveshare' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-24 18:28:14.470 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.gather' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-24 18:28:14.470 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-24 18:28:14.777 [info] Started local extension host with pid 2985.
2025-06-24 18:28:15.085 [info] ComputeTargetPlatform: linux-x64
2025-06-24 18:28:15.229 [error] Extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-24 18:28:15.307 [warning] [twxs.cmake]: 无法注册“cmake.cmakePath”。此属性已注册。
2025-06-24 18:28:16.901 [error] [Extension Host] (node:2985) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `exe --trace-deprecation ...` to show where the warning was created)
2025-06-24 18:28:17.547 [info] [perf] Render performance baseline is 13ms
2025-06-24 18:28:21.149 [info] Settings Sync: Token updated for <NAME_EMAIL>
2025-06-24 18:28:21.149 [info] Settings Sync: Account status changed from uninitialized to available
2025-06-24 18:28:25.370 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment Terminal Capability Check","processId":{},"creationOptions":{"name":"Augment Terminal Capability Check","hideFromUser":true,"shellPath":"bash","isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":80,"rows":30},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo 'Terminal capability test'","confidence":2,"isTrusted":true}}}
2025-06-24 18:28:25.371 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment Terminal Capability Check","processId":{},"creationOptions":{"name":"Augment Terminal Capability Check","hideFromUser":true,"shellPath":"bash","isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":80,"rows":30},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo 'Terminal capability test'","confidence":2,"isTrusted":true}},"exitCode":0}
2025-06-24 18:28:25.547 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment Terminal Capability Check","processId":{},"creationOptions":{"name":"Augment Terminal Capability Check","hideFromUser":true,"shellPath":"fish","isTransient":true},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":80,"rows":30},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo 'Terminal capability test'","confidence":2,"isTrusted":true}}}
2025-06-24 18:28:25.547 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment Terminal Capability Check","processId":{},"creationOptions":{"name":"Augment Terminal Capability Check","hideFromUser":true,"shellPath":"fish","isTransient":true},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":80,"rows":30},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo 'Terminal capability ","confidence":2,"isTrusted":false}},"exitCode":0}
2025-06-24 18:28:25.700 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment Terminal Capability Check","processId":{},"creationOptions":{"name":"Augment Terminal Capability Check","hideFromUser":true,"shellPath":"bash","shellArgs":["--init-file","/opt/visual-studio-code/resources/app/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh"],"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":80,"rows":30},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo 'Terminal capability test'","confidence":2,"isTrusted":true}}}
2025-06-24 18:28:25.705 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment Terminal Capability Check","processId":{},"creationOptions":{"name":"Augment Terminal Capability Check","hideFromUser":true,"shellPath":"bash","shellArgs":["--init-file","/opt/visual-studio-code/resources/app/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh"],"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":80,"rows":30},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo 'Terminal capability test'","confidence":2,"isTrusted":false}},"exitCode":0}
2025-06-24 18:28:41.516 [error] Illegal value for lineNumber: Error: Illegal value for lineNumber
    at hf.getLineMaxColumn (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:267:112)
    at Ma.getBottomForLineNumber (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:276:1635)
    at vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:409:123492
    at Vi (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:31:16518)
    at lGi.t (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:409:122917)
    at Noe._runFn (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:409:122551)
    at Noe.k (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:31:22268)
    at Noe.endUpdate (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:31:22682)
    at eH.finish (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:31:17243)
    at Vi (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:31:16533)
2025-06-24 18:28:41.519 [error] Illegal value for lineNumber: Error: Illegal value for lineNumber
    at hf.getLineMaxColumn (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:267:112)
    at Ma.getBottomForLineNumber (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:276:1635)
    at vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:409:123492
    at Vi (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:31:16518)
    at lGi.t (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:409:122917)
    at Noe._runFn (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:409:122551)
    at Noe.k (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:31:22268)
    at Noe.endUpdate (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:31:22682)
    at mw.endUpdate (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:31:26975)
    at mw.endUpdate (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:31:26975)
2025-06-24 18:30:15.471 [error] [Extension Host] Failed to get remote url: Error: Failed to get remote url, no remote found
    at eN.handleGetRemoteUrlRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1574:492)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:3966
    at e.runTimed (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1864:38780)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:3924
2025-06-24 18:30:15.927 [error] [Extension Host] Failed to start remote agent overviews stream: Error: This operation was aborted
    at e.transientIssue (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:13968)
    at wM.callApiStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:518:4279)
    at wM.callApiStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:58439)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23027)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:32:15.417 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:34:15.437 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:36:15.456 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:38:15.482 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:40:15.507 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:42:15.519 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:44:15.535 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:45:25.039 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"fish","processId":{},"creationOptions":{"shellPath":"/usr/bin/fish"},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":132,"rows":17}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"quickshell","confidence":0},"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}}}
2025-06-24 18:45:25.524 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"quickshell","processId":{},"creationOptions":{"shellPath":"/usr/bin/fish"},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":132,"rows":17}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"󰪢 0s 󰜥 󰉋   /.config     quickshell","confidence":2,"isTrusted":false},"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"exitCode":255}
