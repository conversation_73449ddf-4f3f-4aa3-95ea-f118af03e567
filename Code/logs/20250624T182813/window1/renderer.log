2025-06-24 18:28:14.460 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vsliveshare.vsliveshare' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-24 18:28:14.470 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.gather' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-24 18:28:14.470 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-24 18:28:14.777 [info] Started local extension host with pid 2985.
2025-06-24 18:28:15.085 [info] ComputeTargetPlatform: linux-x64
2025-06-24 18:28:15.229 [error] Extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-24 18:28:15.307 [warning] [twxs.cmake]: 无法注册“cmake.cmakePath”。此属性已注册。
2025-06-24 18:28:16.901 [error] [Extension Host] (node:2985) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `exe --trace-deprecation ...` to show where the warning was created)
2025-06-24 18:28:17.547 [info] [perf] Render performance baseline is 13ms
2025-06-24 18:28:21.149 [info] Settings Sync: Token updated for <NAME_EMAIL>
2025-06-24 18:28:21.149 [info] Settings Sync: Account status changed from uninitialized to available
2025-06-24 18:28:25.370 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment Terminal Capability Check","processId":{},"creationOptions":{"name":"Augment Terminal Capability Check","hideFromUser":true,"shellPath":"bash","isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":80,"rows":30},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo 'Terminal capability test'","confidence":2,"isTrusted":true}}}
2025-06-24 18:28:25.371 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment Terminal Capability Check","processId":{},"creationOptions":{"name":"Augment Terminal Capability Check","hideFromUser":true,"shellPath":"bash","isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":80,"rows":30},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo 'Terminal capability test'","confidence":2,"isTrusted":true}},"exitCode":0}
2025-06-24 18:28:25.547 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment Terminal Capability Check","processId":{},"creationOptions":{"name":"Augment Terminal Capability Check","hideFromUser":true,"shellPath":"fish","isTransient":true},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":80,"rows":30},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo 'Terminal capability test'","confidence":2,"isTrusted":true}}}
2025-06-24 18:28:25.547 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment Terminal Capability Check","processId":{},"creationOptions":{"name":"Augment Terminal Capability Check","hideFromUser":true,"shellPath":"fish","isTransient":true},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":80,"rows":30},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo 'Terminal capability ","confidence":2,"isTrusted":false}},"exitCode":0}
2025-06-24 18:28:25.700 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment Terminal Capability Check","processId":{},"creationOptions":{"name":"Augment Terminal Capability Check","hideFromUser":true,"shellPath":"bash","shellArgs":["--init-file","/opt/visual-studio-code/resources/app/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh"],"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":80,"rows":30},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo 'Terminal capability test'","confidence":2,"isTrusted":true}}}
2025-06-24 18:28:25.705 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment Terminal Capability Check","processId":{},"creationOptions":{"name":"Augment Terminal Capability Check","hideFromUser":true,"shellPath":"bash","shellArgs":["--init-file","/opt/visual-studio-code/resources/app/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh"],"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":80,"rows":30},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo 'Terminal capability test'","confidence":2,"isTrusted":false}},"exitCode":0}
2025-06-24 18:28:41.516 [error] Illegal value for lineNumber: Error: Illegal value for lineNumber
    at hf.getLineMaxColumn (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:267:112)
    at Ma.getBottomForLineNumber (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:276:1635)
    at vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:409:123492
    at Vi (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:31:16518)
    at lGi.t (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:409:122917)
    at Noe._runFn (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:409:122551)
    at Noe.k (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:31:22268)
    at Noe.endUpdate (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:31:22682)
    at eH.finish (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:31:17243)
    at Vi (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:31:16533)
2025-06-24 18:28:41.519 [error] Illegal value for lineNumber: Error: Illegal value for lineNumber
    at hf.getLineMaxColumn (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:267:112)
    at Ma.getBottomForLineNumber (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:276:1635)
    at vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:409:123492
    at Vi (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:31:16518)
    at lGi.t (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:409:122917)
    at Noe._runFn (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:409:122551)
    at Noe.k (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:31:22268)
    at Noe.endUpdate (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:31:22682)
    at mw.endUpdate (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:31:26975)
    at mw.endUpdate (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:31:26975)
2025-06-24 18:30:15.471 [error] [Extension Host] Failed to get remote url: Error: Failed to get remote url, no remote found
    at eN.handleGetRemoteUrlRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1574:492)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:3966
    at e.runTimed (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1864:38780)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:3924
2025-06-24 18:30:15.927 [error] [Extension Host] Failed to start remote agent overviews stream: Error: This operation was aborted
    at e.transientIssue (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:13968)
    at wM.callApiStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:518:4279)
    at wM.callApiStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:58439)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23027)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:32:15.417 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:34:15.437 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:36:15.456 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:38:15.482 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:40:15.507 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:42:15.519 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:44:15.535 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:45:25.039 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"fish","processId":{},"creationOptions":{"shellPath":"/usr/bin/fish"},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":132,"rows":17}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"quickshell","confidence":0},"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}}}
2025-06-24 18:45:25.524 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"quickshell","processId":{},"creationOptions":{"shellPath":"/usr/bin/fish"},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":132,"rows":17}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"󰪢 0s 󰜥 󰉋   /.config     quickshell","confidence":2,"isTrusted":false},"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"exitCode":255}
2025-06-24 18:46:15.550 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:48:15.562 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:48:30.669 [error] 出现未知错误。有关详细信息，请参阅日志。
2025-06-24 18:49:22.651 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"fish","processId":{},"creationOptions":{"shellPath":"/usr/bin/fish"},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":132,"rows":17}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"quickshell","confidence":0},"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}}}
2025-06-24 18:49:28.658 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"quickshell","processId":{},"creationOptions":{"shellPath":"/usr/bin/fish"},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":132,"rows":17}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"󰪢 0s 󰜥 󰉋   /.config     quickshell  INFO: Launching config: \"/home/<USER>/.config/quickshell/shell.qml\"  INFO: Shell ID: \"29e90fb69f5fbfd9384ddcac00dd6458\" Path ID \"29e90fb69f5fbfd9384ddcac00dd6458\"  INFO: Saving logs to \"/run/user/1000/quickshell/by-id/7nxpjwcys/log.qslog\"  WARN: QSettings::value: Empty key passed ERROR: Failed to load configuration ERROR:   caused by **/shell.qml[70:56]: Type SidebarLeft unavailable ERROR:   caused by **/modules/sidebarLeft/SidebarLeft.qml[20:42]: Type SidebarLeftContent unavailable ERROR:   caused by **/modules/sidebarLeft/SidebarLeftContent.qml[99:13]: Type AiChat unavailable ERROR:   caused by **/modules/sidebarLeft/AiChat.qml[25:5]: Type ModelSelector unavailable ERROR:   caused by **/modules/sidebarLeft/aiChat/ModelSelector.qml[4:1]: module \"QtGraphicalEffects\" is not installed󰪢 0s 󰜥 󰉋   /.config     quickshell","confidence":2,"isTrusted":false},"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"exitCode":130}
2025-06-24 18:49:30.607 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"fish","processId":{},"creationOptions":{"shellPath":"/usr/bin/fish"},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":132,"rows":17}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"quickshell","confidence":0},"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}}}
2025-06-24 18:50:15.578 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:52:09.428 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"fish","cwd":"/home/<USER>/.config","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":120,"rows":17},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"mkdir -p quickshell/state/user/generated/wallpaper","confidence":2,"isTrusted":true}}}
2025-06-24 18:52:09.429 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"fish","cwd":"/home/<USER>/.config","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":120,"rows":17},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"mkdir -p quickshell/state/user/generated/wallpaper","confidence":2,"isTrusted":false}},"exitCode":0}
2025-06-24 18:52:15.594 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:53:33.353 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment - qs -p quickshell/settings.qml","processId":{},"creationOptions":{"name":"Augment - qs -p quickshell/settings.qml","shellPath":"fish","cwd":"/home/<USER>/.config","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":120,"rows":17},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"qs -p quickshell/settings.qml","confidence":2,"isTrusted":true}}}
2025-06-24 18:54:06.440 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment - qs -p quickshell/settings.qml","processId":{},"creationOptions":{"name":"Augment - qs -p quickshell/settings.qml","shellPath":"fish","cwd":"/home/<USER>/.config","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":120,"rows":17},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":false}},"exitCode":0}
2025-06-24 18:54:15.606 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:55:10.471 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment - qs -p quickshell/settings.qml","processId":{},"creationOptions":{"name":"Augment - qs -p quickshell/settings.qml","shellPath":"fish","cwd":"/home/<USER>/.config","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":120,"rows":61},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"qs -p quickshell/settings.qml","confidence":2,"isTrusted":true}}}
2025-06-24 18:56:03.481 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment - qs -p quickshell/settings.qml","processId":{},"creationOptions":{"name":"Augment - qs -p quickshell/settings.qml","shellPath":"fish","cwd":"/home/<USER>/.config","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":120,"rows":61},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":false}},"exitCode":0}
2025-06-24 18:56:15.618 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:57:02.327 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment - qs -p quickshell/settings.qml","processId":{},"creationOptions":{"name":"Augment - qs -p quickshell/settings.qml","shellPath":"fish","cwd":"/home/<USER>/.config","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":120,"rows":17},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"qs -p quickshell/settings.qml","confidence":2,"isTrusted":true}}}
2025-06-24 18:58:08.023 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment - qs -p quickshell/settings.qml","processId":{},"creationOptions":{"name":"Augment - qs -p quickshell/settings.qml","shellPath":"fish","cwd":"/home/<USER>/.config","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":120,"rows":17},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"qs -p quickshell/settings.qml","confidence":2,"isTrusted":true}}}
2025-06-24 18:58:15.631 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 18:59:17.344 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment - qs -p quickshell/settings.qml","processId":{},"creationOptions":{"name":"Augment - qs -p quickshell/settings.qml","shellPath":"fish","cwd":"/home/<USER>/.config","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":120,"rows":61},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"󰪢 0s 󰜥 󰉋   /.config    ","confidence":2,"isTrusted":false}},"exitCode":0}
2025-06-24 18:59:22.053 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"fish","cwd":"/home/<USER>/.config","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/.config","path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":120,"rows":61},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"󰪢 0s 󰜥 󰉋   /.config     mkdir -p quickshell/state/user/generated/wallpaper󰪢 0s 󰜥 󰉋   /.config    ","confidence":2,"isTrusted":false},"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}}}
2025-06-24 18:59:22.062 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"fish","cwd":"/home/<USER>/.config","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/.config","path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":120,"rows":61},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"󰪢 0s 󰜥 󰉋   /.config     mkdir -p quickshell/state/user/generated/wallpaper󰪢 0s 󰜥 󰉋   /.config    ","confidence":2,"isTrusted":false},"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}}}
2025-06-24 19:00:15.643 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 19:01:16.913 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment - qs -p quickshell/settings.qml","processId":{},"creationOptions":{"name":"Augment - qs -p quickshell/settings.qml","shellPath":"fish","cwd":"/home/<USER>/.config","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":120,"rows":17},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"qs -p quickshell/settings.qml","confidence":2,"isTrusted":true}}}
2025-06-24 19:02:15.657 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 19:03:22.315 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment - qs -p quickshell/settings.qml","processId":{},"creationOptions":{"name":"Augment - qs -p quickshell/settings.qml","shellPath":"fish","cwd":"/home/<USER>/.config","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":120,"rows":17},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"qs -p quickshell/settings.qml","confidence":2,"isTrusted":true}}}
2025-06-24 19:04:15.671 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 19:06:15.703 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 19:08:15.716 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 19:09:11.232 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment - qs -p quickshell/settings.qml","processId":{},"creationOptions":{"name":"Augment - qs -p quickshell/settings.qml","shellPath":"fish","cwd":"/home/<USER>/.config","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":120,"rows":17},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"qs -p quickshell/settings.qml","confidence":2,"isTrusted":true}}}
2025-06-24 19:10:15.732 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 19:12:15.750 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-24 19:13:06.484 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment - qs -p quickshell/settings.qml","processId":{},"creationOptions":{"name":"Augment - qs -p quickshell/settings.qml","shellPath":"fish","cwd":"/home/<USER>/.config","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"fish"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home/<USER>/.config","scheme":"file"}},"dimensions":{"columns":120,"rows":17},"isPatched":true},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"󰪢 0s 󰜥 󰉋   /.config    ","confidence":2,"isTrusted":false}},"exitCode":0}
2025-06-24 19:14:15.819 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
