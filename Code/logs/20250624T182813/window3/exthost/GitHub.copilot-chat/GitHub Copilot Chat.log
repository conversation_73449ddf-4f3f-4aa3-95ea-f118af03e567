2025-06-24 19:05:19.622 [info] Using the Electron fetcher.
2025-06-24 19:05:19.622 [info] [GitExtensionServiceImpl] Initializing Git extension service.
2025-06-24 19:05:19.622 [info] [GitExtensionServiceImpl] Successfully activated the vscode.git extension.
2025-06-24 19:05:19.622 [info] [GitExtensionServiceImpl] Enablement state of the vscode.git extension: true.
2025-06-24 19:05:19.622 [info] [GitExtensionServiceImpl] Successfully registered Git commit message provider.
2025-06-24 19:05:20.040 [info] Logged in as stevessr
2025-06-24 19:05:21.800 [info] Got Copilot token for stevessr
2025-06-24 19:05:21.809 [info] activationBlocker from 'languageModelAccess' took for 1639ms
2025-06-24 19:05:21.839 [info] copilot token chat_enabled: true, sku: free_educational_quota
2025-06-24 19:05:21.854 [info] Registering default platform agent...
2025-06-24 19:05:21.854 [info] activationBlocker from 'conversationFeature' took for 1686ms
2025-06-24 19:05:21.854 [info] Successfully activated the GitHub.vscode-pull-request-github extension.
2025-06-24 19:05:21.854 [info] [githubTitleAndDescriptionProvider] Initializing GitHub PR title and description provider provider.
2025-06-24 19:05:21.854 [info] Successfully registered GitHub PR title and description provider.
2025-06-24 19:05:21.854 [info] Successfully registered GitHub PR reviewer comments provider.
2025-06-24 19:05:22.146 [info] BYOK: Copilot Chat known models list fetched successfully.
2025-06-24 19:05:22.526 [info] Fetched model metadata in 717ms b7adcbf1-730f-4347-9b2b-5df401cdd476
