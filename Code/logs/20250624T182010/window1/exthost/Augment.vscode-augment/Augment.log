2025-06-24 18:20:18.282 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-24 18:20:18.282 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"disableFocusOnAugmentPanel":false,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-24 18:20:18.282 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"maxLinesTerminalProcessOutput":0,"vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryMaxChars":0,"historySummaryLowerChars":0,"historySummaryPrompt":"","enableSpawnSubAgentTool":false,"enableCommitIndexing":false,"maxCommitsToIndex":0}
2025-06-24 18:20:18.282 [info] 'AugmentExtension' Retrieving model config
2025-06-24 18:20:19.536 [info] 'AugmentExtension' Retrieved model config
2025-06-24 18:20:19.536 [info] 'AugmentExtension' Returning model config
2025-06-24 18:20:19.549 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - enableNewThreadsList: false to true
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - vscodeTaskListMinVersion: "" to "0.482.0"
  - vscodeSupportToolUseStartMinVersion: "" to "0.485.0"
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryMinVersion: "" to "0.0.0"
  - historySummaryMaxChars: 0 to 200000
  - historySummaryLowerChars: 0 to 80000
  - historySummaryPrompt: "" to "Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\n\nYour summary should be structured as follows:\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\n\nExample summary structure:\n1. Previous Conversation:\n[Detailed description]\n2. Current Work:\n[Detailed description]\n3. Key Technical Concepts:\n- [Concept 1]\n- [Concept 2]\n- [...]\n4. Relevant Files and Code:\n- [File Name 1]\n    - [Summary of why this file is important]\n    - [Summary of the changes made to this file, if any]\n    - [Important Code Snippet]\n- [File Name 2]\n    - [Important Code Snippet]\n- [...]\n5. Problem Solving:\n[Detailed description]\n6. Pending Tasks and Next Steps:\n- [Task 1 details & next steps]\n- [Task 2 details & next steps]\n- [...]\n\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\n"
2025-06-24 18:20:19.549 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/.config (explicit) at 2025/6/24 16:02:55
2025-06-24 18:20:19.549 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-24 18:20:19.549 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-24 18:20:19.549 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/.config granted at 2025/6/24 16:02:55; type = explicit
2025-06-24 18:20:19.549 [info] 'WorkspaceManager' Adding workspace folder .config; folderRoot = /home/<USER>/.config; syncingPermission = granted
2025-06-24 18:20:19.549 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/.config (explicit) at 2025/6/24 16:02:55
2025-06-24 18:20:19.589 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-24 18:20:19.589 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-24 18:20:19.600 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-06-24 18:20:19.613 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-24 18:20:19.614 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-24 18:20:20.174 [info] 'WorkspaceManager[.config]' Start tracking
2025-06-24 18:20:20.251 [info] 'PathMap' Opened source folder /home/<USER>/.config with id 100
2025-06-24 18:20:20.251 [info] 'OpenFileManager' Opened source folder 100
2025-06-24 18:20:20.313 [info] 'MtimeCache[.config]' reading blob name cache from /home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/053bfff36d55a35354aa78fe5c63eac5cf43c0b9b251966b5e57a559210d4a3e/mtime-cache.json
2025-06-24 18:20:20.409 [info] 'MtimeCache[.config]' read 12596 entries from /home/<USER>/.config/Code/User/workspaceStorage/8754aae2d4683f6516140b1ea7f03205/Augment.vscode-augment/053bfff36d55a35354aa78fe5c63eac5cf43c0b9b251966b5e57a559210d4a3e/mtime-cache.json
2025-06-24 18:20:21.243 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-24 18:20:21.243 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-24 18:20:21.625 [info] 'ToolsModel' Tools Mode: AGENT (6 hosts)
2025-06-24 18:20:22.977 [info] 'WorkspaceManager[.config]' Directory removed: Code/logs/20250624T101412
2025-06-24 18:20:22.977 [info] 'WorkspaceManager[.config]' Directory removed: Code/logs/20250624T101405/window2/exthost/vscode.git
2025-06-24 18:20:22.977 [info] 'WorkspaceManager[.config]' Directory removed: Code/logs/20250624T101405/window2/exthost/vscode.github
2025-06-24 18:20:22.977 [info] 'WorkspaceManager[.config]' Directory removed: Code/logs/20250624T101405/window1/output_20250624T101406
2025-06-24 18:20:22.977 [info] 'WorkspaceManager[.config]' Directory removed: Code/logs/20250624T101405/window2/exthost/GitHub.copilot
2025-06-24 18:20:22.977 [info] 'WorkspaceManager[.config]' Directory removed: Code/logs/20250624T101405/window2/output_20250624T101413
2025-06-24 18:20:22.977 [info] 'WorkspaceManager[.config]' Directory removed: Code/logs/20250624T101405/window2/output_20250624T101427
2025-06-24 18:20:22.977 [info] 'WorkspaceManager[.config]' Directory removed: Code/logs/20250624T101405/window2/exthost/GitHub.copilot-chat
2025-06-24 18:20:22.977 [info] 'WorkspaceManager[.config]' Directory removed: Code/logs/20250624T101405/window2/exthost/Augment.vscode-augment
2025-06-24 18:20:22.977 [info] 'WorkspaceManager[.config]' Directory removed: Code/logs/20250624T101405/window2/exthost/vscode.css-language-features
2025-06-24 18:20:22.977 [info] 'WorkspaceManager[.config]' Directory removed: Code/logs/20250624T101405/window2/exthost/vscode.json-language-features
2025-06-24 18:20:22.977 [info] 'WorkspaceManager[.config]' Directory removed: Code/logs/20250624T101405/window2/exthost/output_logging_20250624T101413
2025-06-24 18:20:22.977 [info] 'WorkspaceManager[.config]' Directory removed: Code/logs/20250624T101405/window2/exthost/output_logging_20250624T105752
2025-06-24 18:20:22.977 [info] 'WorkspaceManager[.config]' Directory removed: Code/logs/20250624T101405/window2/exthost/GitHub.vscode-pull-request-github
2025-06-24 18:20:22.977 [info] 'WorkspaceManager[.config]' Directory removed: Code/logs/20250624T101405/window1/exthost/ms-dotnettools.vscode-dotnet-runtime
2025-06-24 18:20:23.029 [info] 'WorkspaceManager[.config]' Directory removed: Code/logs/20250624T101405
2025-06-24 18:20:23.709 [info] 'WorkspaceManager[.config]' Directory created: Code/blob_storage/f2174c8b-02ad-4e45-b9cf-278d18582c9d
2025-06-24 18:20:23.709 [info] 'WorkspaceManager[.config]' Directory removed: Code/blob_storage/90269bf6-f210-480b-b68a-d914aeaca4cc
2025-06-24 18:20:23.711 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182023
2025-06-24 18:20:23.874 [info] 'WorkspaceManager[.config]' Directory created: Code/User/workspaceStorage/1750760423647
2025-06-24 18:20:23.875 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182010/window2
2025-06-24 18:20:23.991 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-06-24 18:20:24.551 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182010/window2/exthost
2025-06-24 18:20:24.553 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182010/window2/output_20250624T182024
2025-06-24 18:20:25.231 [info] 'ToolsModel' Host: mcpHost (1 tools: 18 enabled, 0 disabled})
 + search_g-search

2025-06-24 18:20:26.149 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182010/window2/exthost/output_logging_20250624T182024
2025-06-24 18:20:26.153 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182010/window2/exthost/vscode.github-authentication
2025-06-24 18:20:26.153 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182010/window2/exthost/vscode.microsoft-authentication
2025-06-24 18:20:26.662 [info] 'WorkspaceManager[.config]' Directory created: Code/logs/20250624T182010/window2/exthost/ms-python.python
2025-06-24 18:20:27.344 [info] 'WorkspaceManager[.config]' Directory created: microsoft-edge/Default/blob_storage/390ae70a-7fdb-4abd-be5d-3c1816b11967
2025-06-24 18:20:29.762 [info] 'ToolsModel' Host: mcpHost (1 tools: 48 enabled, 0 disabled})
 + interactive_feedback_interactive-feedback-mcp

2025-06-24 18:20:29.762 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-24 18:20:29.762 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-24 18:20:29.762 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-24 18:20:30.861 [info] 'WorkspaceManager[.config]' Directory created: sparkle/blob_storage/9bf7b702-9785-4a70-b372-1be3c36e9f55
2025-06-24 18:20:30.861 [info] 'WorkspaceManager[.config]' Directory removed: sparkle/blob_storage/b82beafd-77cb-4536-804e-e93dd4a14744
2025-06-24 18:20:42.428 [info] 'WorkspaceManager[.config]' Directory removed: Code/User/workspaceStorage/1750758875366
2025-06-24 18:21:23.271 [error] 'AugmentExtension' API request 87badc4f-b64c-4e1b-a9dc-c85edd087b12 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-24 18:21:23.784 [error] 'AugmentExtension' API request 2c288ad7-9bd4-4278-aea5-84d78a855706 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:21:23.784 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:21:23.784 [info] 'DiskFileManager[.config]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-24 18:21:24.775 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
2025-06-24 18:21:29.949 [info] 'WorkspaceManager[.config]' Tracking enabled
2025-06-24 18:21:29.949 [info] 'WorkspaceManager[.config]' Path metrics:
  - directories emitted: 3981
  - files emitted: 18685
  - other paths emitted: 16
  - total paths emitted: 22682
  - timing stats:
    - readDir: 30 ms
    - filter: 174 ms
    - yield: 31 ms
    - total: 263 ms
2025-06-24 18:21:29.949 [info] 'WorkspaceManager[.config]' File metrics:
  - paths accepted: 12769
  - paths not accessible: 1
  - not plain files: 0
  - large files: 549
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 12381
  - mtime cache misses: 394
  - probe batches: 23
  - blob names probed: 12924
  - files read: 6151
  - blobs uploaded: 156
  - timing stats:
    - ingestPath: 9 ms
    - probe: 36051 ms
    - stat: 49 ms
    - read: 10538 ms
    - upload: 7539 ms
2025-06-24 18:21:29.949 [info] 'WorkspaceManager[.config]' Startup metrics:
  - create SourceFolder: 139 ms
  - read MtimeCache: 96 ms
  - pre-populate PathMap: 99 ms
  - create PathFilter: 121 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 266 ms
  - purge stale PathMap entries: 2 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 69034 ms
  - enable persist: 17 ms
  - total: 69774 ms
2025-06-24 18:21:29.949 [info] 'WorkspaceManager' Workspace startup complete in 70408 ms
2025-06-24 18:22:03.429 [info] 'WorkspaceManager[.config]' Directory removed: quickshell
2025-06-24 18:22:14.763 [info] 'WorkspaceManager[.config]' Directory created: quickshell
2025-06-24 18:22:14.918 [info] 'WorkspaceManager[.config]' Directory created: quickshell/services
2025-06-24 18:22:32.801 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-24 18:22:39.313 [info] 'WorkspaceManager[.config]' Directory created: microsoft-edge/Default/blob_storage/64bb20fa-26f3-46e5-8a1a-e6878dfd1bbc
2025-06-24 18:22:41.267 [error] 'AugmentExtension' API request f8c07c9d-325b-4371-b05a-a0cce217fbc6 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-24 18:22:41.798 [error] 'AugmentExtension' API request ecb4f62a-59a1-4920-ad85-6b37243bfce4 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:22:41.798 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:22:41.798 [info] 'DiskFileManager[.config]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-24 18:22:42.613 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
2025-06-24 18:22:47.140 [info] 'WorkspaceManager[.config]' Directory created: microsoft-edge/Default/blob_storage/a5989774-e0bd-4f57-9123-cffa670085b2
2025-06-24 18:23:09.820 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-24 18:23:48.362 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 37a409dc04bf7e4f580d4de9d67eff516532fabc44a518d1c570aa08f0a6316c: deleted
2025-06-24 18:24:37.349 [info] 'WorkspaceManager[.config]' Directory removed: hypr/shaders
2025-06-24 18:24:37.349 [info] 'WorkspaceManager[.config]' Directory removed: hypr/hyprland
2025-06-24 18:24:37.349 [info] 'WorkspaceManager[.config]' Directory removed: hypr/hyprlock
2025-06-24 18:24:40.721 [info] 'WorkspaceManager[.config]' Directory created: hypr/hyprland
2025-06-24 18:24:40.725 [info] 'WorkspaceManager[.config]' Directory created: hypr/hyprland/scripts
2025-06-24 18:24:40.725 [info] 'WorkspaceManager[.config]' Directory created: hypr/hyprland/scripts/ai
2025-06-24 18:24:40.725 [info] 'WorkspaceManager[.config]' Directory created: hypr/hyprlock
2025-06-24 18:24:40.725 [info] 'WorkspaceManager[.config]' Directory created: hypr/shaders
2025-06-24 18:24:58.360 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 0aa9f4ad1d04ce789044f815f1a467de28d1bf363c2fdf3629cacd1e791bd3f8: deleted
2025-06-24 18:24:59.440 [info] 'WorkspaceManager[.config]' Directory created: Code/Backups/4d0cbb2192c21da9de180d51ba3eaaca
2025-06-24 18:24:59.441 [info] 'WorkspaceManager[.config]' Directory created: Code/Backups/4d0cbb2192c21da9de180d51ba3eaaca/file
2025-06-24 18:25:24.607 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 570e9a2d62b63203ef0bd3dc26cded7e6da60662cf431cc6e6bc814ce355456c: deleted
2025-06-24 18:25:24.607 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 570e9a2d62b63203ef0bd3dc26cded7e6da60662cf431cc6e6bc814ce355456c: deleted
2025-06-24 18:25:28.632 [error] 'AugmentExtension' API request 48446e4f-e3c7-496a-bf8d-a84b1cc56612 to https://i0.api.augmentcode.com/batch-upload failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:25:29.134 [error] 'AugmentExtension' API request 073fad47-f813-4f9a-88d2-d0e63d562e11 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:25:29.134 [error] 'AugmentExtension' Dropping error report "batch-upload call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:25:29.134 [info] 'DiskFileManager[.config]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 18:25:29.984 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
2025-06-24 18:26:08.083 [info] 'WorkspaceManager[.config]' Directory created: microsoft-edge/Edge Designer
2025-06-24 18:26:08.479 [info] 'WorkspaceManager[.config]' Directory created: microsoft-edge/Default/blob_storage/6b5f5c7e-abfa-40c2-9fd2-e1f0084ac504
2025-06-24 18:26:08.479 [info] 'WorkspaceManager[.config]' Directory removed: microsoft-edge/Edge Designer
2025-06-24 18:26:11.390 [info] 'WorkspaceManager[.config]' Directory removed: microsoft-edge/Default/blob_storage/390ae70a-7fdb-4abd-be5d-3c1816b11967
2025-06-24 18:26:11.390 [info] 'WorkspaceManager[.config]' Directory removed: microsoft-edge/Default/blob_storage/64bb20fa-26f3-46e5-8a1a-e6878dfd1bbc
2025-06-24 18:26:11.390 [info] 'WorkspaceManager[.config]' Directory removed: microsoft-edge/Default/blob_storage/a5989774-e0bd-4f57-9123-cffa670085b2
2025-06-24 18:26:11.390 [info] 'WorkspaceManager[.config]' Directory removed: microsoft-edge/Default/blob_storage/c97293fe-4699-46fc-aaed-cc453186c080
2025-06-24 18:26:54.920 [error] 'AugmentExtension' API request f9b3d383-61fb-4f4d-85ea-3ecf3d10b7b0 to https://i0.api.augmentcode.com/find-missing failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-06-24 18:26:55.422 [error] 'AugmentExtension' API request f9992d5d-b147-4d82-8813-3f710fb77a31 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-06-24 18:26:55.422 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-06-24 18:26:55.422 [info] 'DiskFileManager[.config]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-06-24 18:26:56.175 [info] 'DiskFileManager[.config]' Operation succeeded after 1 transient failures
