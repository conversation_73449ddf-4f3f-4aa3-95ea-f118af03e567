2025-06-24 18:20:16.134 [info] [main] 日志级别: Info
2025-06-24 18:20:16.134 [info] [main] 正在验证在以下位置找到的 git: "git"
2025-06-24 18:20:16.134 [info] [main] 使用来自 "git" 的 git "2.50.0"
2025-06-24 18:20:16.134 [info] [Model][doInitialScan] Initial repository scan started
2025-06-24 18:20:16.134 [info] > git rev-parse --show-toplevel [17ms]
2025-06-24 18:20:16.134 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:16.134 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:20:16.134 [info] > git rev-parse --git-dir --git-common-dir [1ms]
2025-06-24 18:20:16.134 [info] [Model][openRepository] Opened repository (path): /home/<USER>/.config/quickshell
2025-06-24 18:20:16.134 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/.config/quickshell
2025-06-24 18:20:16.134 [info] > git config --get commit.template [1ms]
2025-06-24 18:20:16.134 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:20:16.134 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [24ms]
2025-06-24 18:20:17.639 [info] > git status -z -uall [1512ms]
2025-06-24 18:20:17.640 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1506ms]
2025-06-24 18:20:18.019 [info] > git check-ignore -v -z --stdin [346ms]
2025-06-24 18:20:18.020 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [364ms]
2025-06-24 18:20:18.075 [info] > git config --get --local branch.master.vscode-merge-base [46ms]
2025-06-24 18:20:18.075 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:20:18.084 [info] > git config --get commit.template [69ms]
2025-06-24 18:20:18.110 [info] > git reflog master --grep-reflog=branch: Created from *. [26ms]
2025-06-24 18:20:18.120 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:20:18.254 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [134ms]
2025-06-24 18:20:18.279 [info] > git status -z -uall [16ms]
2025-06-24 18:20:18.279 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-24 18:20:18.743 [info] > git rev-parse --show-toplevel [12ms]
2025-06-24 18:20:18.743 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.752 [info] > git rev-parse --show-toplevel [0ms]
2025-06-24 18:20:18.752 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.764 [info] > git rev-parse --show-toplevel [3ms]
2025-06-24 18:20:18.764 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.774 [info] > git rev-parse --show-toplevel [0ms]
2025-06-24 18:20:18.774 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.783 [info] > git rev-parse --show-toplevel [0ms]
2025-06-24 18:20:18.783 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.796 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:20:18.796 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.805 [info] > git rev-parse --show-toplevel [0ms]
2025-06-24 18:20:18.805 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.816 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:20:18.816 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.826 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:20:18.826 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.836 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:20:18.836 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.846 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:20:18.846 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.855 [info] > git rev-parse --show-toplevel [0ms]
2025-06-24 18:20:18.855 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.865 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:20:18.865 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.875 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:20:18.875 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.887 [info] > git rev-parse --show-toplevel [0ms]
2025-06-24 18:20:18.887 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.897 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:20:18.897 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.905 [info] > git rev-parse --show-toplevel [0ms]
2025-06-24 18:20:18.905 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.915 [info] > git rev-parse --show-toplevel [0ms]
2025-06-24 18:20:18.915 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.926 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:20:18.926 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.937 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:20:18.937 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.951 [info] > git rev-parse --show-toplevel [6ms]
2025-06-24 18:20:18.951 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.961 [info] > git rev-parse --show-toplevel [0ms]
2025-06-24 18:20:18.961 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.972 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:20:18.972 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.982 [info] > git rev-parse --show-toplevel [0ms]
2025-06-24 18:20:18.982 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:18.993 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:20:18.993 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:19.003 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:20:19.017 [info] > git rev-parse --git-dir --git-common-dir [3ms]
2025-06-24 18:20:19.021 [info] [Model][openRepository] Opened repository (path): /home/<USER>/.config/hypr
2025-06-24 18:20:19.021 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/.config/hypr
2025-06-24 18:20:19.049 [info] > git rev-parse --show-toplevel [8ms]
2025-06-24 18:20:19.049 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:19.049 [info] > git config --get commit.template [18ms]
2025-06-24 18:20:19.098 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:20:19.108 [info] > git rev-parse --show-toplevel [29ms]
2025-06-24 18:20:19.108 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:19.130 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [32ms]
2025-06-24 18:20:19.161 [info] > git status -z -uall [20ms]
2025-06-24 18:20:19.161 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-24 18:20:19.175 [info] > git rev-parse --show-toplevel [56ms]
2025-06-24 18:20:19.175 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:19.205 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [31ms]
2025-06-24 18:20:19.221 [info] > git config --get --local branch.master.vscode-merge-base [7ms]
2025-06-24 18:20:19.221 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:20:19.232 [info] > git rev-parse --show-toplevel [37ms]
2025-06-24 18:20:19.232 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:19.232 [info] > git config --get commit.template [46ms]
2025-06-24 18:20:19.259 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:20:19.264 [info] > git rev-parse --show-toplevel [20ms]
2025-06-24 18:20:19.264 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:19.264 [info] > git reflog master --grep-reflog=branch: Created from *. [32ms]
2025-06-24 18:20:19.289 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [30ms]
2025-06-24 18:20:19.310 [info] > git status -z -uall [12ms]
2025-06-24 18:20:19.310 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-24 18:20:19.311 [info] > git rev-parse --show-toplevel [37ms]
2025-06-24 18:20:19.311 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:19.323 [info] > git rev-parse --show-toplevel [2ms]
2025-06-24 18:20:19.323 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:19.333 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:20:19.333 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:19.346 [info] > git rev-parse --show-toplevel [3ms]
2025-06-24 18:20:19.346 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:19.732 [info] > git rev-parse --show-toplevel [375ms]
2025-06-24 18:20:19.732 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:19.770 [info] > git check-ignore -v -z --stdin [22ms]
2025-06-24 18:20:19.771 [info] > git check-ignore -v -z --stdin [14ms]
2025-06-24 18:20:20.097 [info] > git rev-parse --show-toplevel [328ms]
2025-06-24 18:20:20.097 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:20.184 [info] > git rev-parse --show-toplevel [39ms]
2025-06-24 18:20:20.184 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:20.205 [info] > git rev-parse --show-toplevel [8ms]
2025-06-24 18:20:20.205 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:20.238 [info] > git rev-parse --show-toplevel [2ms]
2025-06-24 18:20:20.238 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:20.251 [info] > git rev-parse --show-toplevel [2ms]
2025-06-24 18:20:20.251 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:20.288 [info] > git rev-parse --show-toplevel [2ms]
2025-06-24 18:20:20.289 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:20.300 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:20:20.300 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:20.343 [info] > git rev-parse --show-toplevel [17ms]
2025-06-24 18:20:20.343 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:20.360 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:20:20.360 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:20.374 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:20:20.374 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:20.387 [info] > git rev-parse --show-toplevel [1ms]
2025-06-24 18:20:20.387 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:20.540 [info] > git rev-parse --show-toplevel [141ms]
2025-06-24 18:20:20.540 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:20.565 [info] > git rev-parse --show-toplevel [2ms]
2025-06-24 18:20:20.565 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:20.921 [info] > git rev-parse --show-toplevel [80ms]
2025-06-24 18:20:20.921 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:20.941 [info] > git rev-parse --show-toplevel [2ms]
2025-06-24 18:20:20.941 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:20.995 [info] > git rev-parse --show-toplevel [2ms]
2025-06-24 18:20:20.995 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:20:20.996 [info] [Model][doInitialScan] Initial repository scan completed - repositories (2), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-24 18:20:21.066 [info] > git config --get --local branch.master.github-pr-owner-number [42ms]
2025-06-24 18:20:21.066 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:20:21.079 [info] > git config --get --local branch.master.github-pr-owner-number [67ms]
2025-06-24 18:20:21.079 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:20:21.105 [info] > git config --get --local branch.master.remote [14ms]
2025-06-24 18:20:21.105 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:20:21.119 [info] > git config --get --local branch.master.remote [40ms]
2025-06-24 18:20:21.119 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:20:21.133 [info] > git config --get commit.template [67ms]
2025-06-24 18:20:21.133 [info] > git config --get commit.template [80ms]
2025-06-24 18:20:21.150 [info] > git config --get --local branch.master.merge [18ms]
2025-06-24 18:20:21.150 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:20:21.152 [info] > git config --get --local branch.master.merge [33ms]
2025-06-24 18:20:21.152 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:20:21.170 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:20:21.185 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:20:21.189 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [4ms]
2025-06-24 18:20:21.217 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [47ms]
2025-06-24 18:20:21.245 [info] > git status -z -uall [41ms]
2025-06-24 18:20:21.245 [info] > git status -z -uall [16ms]
2025-06-24 18:20:21.245 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-24 18:20:21.261 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [44ms]
2025-06-24 18:22:31.535 [warning] [Git][getRemotes] Error: ENOENT: no such file or directory, open '/home/<USER>/.config/quickshell/.git/config'
2025-06-24 18:22:31.549 [warning] [Git][getHEAD] Failed to parse HEAD file: ENOENT: no such file or directory, open '/home/<USER>/.config/quickshell/.git/HEAD'
2025-06-24 18:22:31.562 [info] > git symbolic-ref --short HEAD [13ms]
2025-06-24 18:22:31.562 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:22:31.575 [info] > git remote --verbose [40ms]
2025-06-24 18:22:31.575 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:22:31.597 [warning] [Git][getHEAD] Failed to parse HEAD file: ENOENT: no such file or directory, open '/home/<USER>/.config/quickshell/.git/HEAD'
2025-06-24 18:22:31.610 [warning] [Git][getRemotes] Error: ENOENT: no such file or directory, open '/home/<USER>/.config/quickshell/.git/config'
2025-06-24 18:22:31.625 [info] > git rev-parse HEAD [50ms]
2025-06-24 18:22:31.625 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:22:31.625 [info] > git config --get commit.template [63ms]
2025-06-24 18:22:31.628 [info] > git config --get commit.template [3ms]
2025-06-24 18:22:31.628 [info] > git remote --verbose [18ms]
2025-06-24 18:22:31.628 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:22:31.628 [info] > git symbolic-ref --short HEAD [31ms]
2025-06-24 18:22:31.628 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:22:31.652 [info] > git rev-parse HEAD [12ms]
2025-06-24 18:22:31.652 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:22:32.166 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-24 18:22:32.818 [info] > git rev-parse --show-toplevel [3ms]
2025-06-24 18:22:32.818 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:22:33.343 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-24 18:23:09.767 [info] > git rev-parse --show-toplevel [0ms]
2025-06-24 18:23:09.780 [info] > git rev-parse --git-dir --git-common-dir [1ms]
2025-06-24 18:23:09.783 [info] [Model][openRepository] Opened repository (path): /home/<USER>/.config/quickshell
2025-06-24 18:23:09.783 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/.config/quickshell
2025-06-24 18:23:09.798 [info] > git config --get commit.template [2ms]
2025-06-24 18:23:09.798 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:23:09.835 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [24ms]
2025-06-24 18:23:09.835 [warning] [Git][getBranch] No such branch: master
2025-06-24 18:23:09.865 [info] > git status -z -uall [16ms]
2025-06-24 18:23:09.865 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-24 18:23:09.882 [info] > git rev-parse --show-toplevel [47ms]
2025-06-24 18:23:09.882 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-24 18:23:09.912 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [31ms]
2025-06-24 18:23:09.912 [warning] [Git][getBranch] No such branch: master
2025-06-24 18:23:09.913 [error] [GitHistoryProvider][resolveHEADMergeBase] Failed to resolve merge base for master: Error: No such branch: master.
2025-06-24 18:23:09.917 [info] > git config --get commit.template [5ms]
2025-06-24 18:23:09.931 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:23:09.932 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:23:09.932 [warning] [Git][getBranch] No such branch: master
2025-06-24 18:23:09.961 [info] > git status -z -uall [16ms]
2025-06-24 18:23:09.961 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:23:10.017 [info] > git config --get --local branch.master.github-pr-owner-number [1ms]
2025-06-24 18:23:10.017 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:23:10.031 [info] > git config --get --local branch.master.remote [0ms]
2025-06-24 18:23:10.031 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:23:10.044 [info] > git config --get --local branch.master.merge [0ms]
2025-06-24 18:23:10.044 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:23:10.509 [info] > git check-ignore -v -z --stdin [14ms]
2025-06-24 18:23:10.509 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-24 18:23:25.099 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:23:25.112 [info] > git config --get commit.template [14ms]
2025-06-24 18:23:25.112 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [0ms]
2025-06-24 18:23:25.112 [warning] [Git][getBranch] No such branch: master
2025-06-24 18:23:25.143 [info] > git status -z -uall [16ms]
2025-06-24 18:23:25.143 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:23:29.695 [info] > git -c user.useConfigOnly=true commit --quiet --allow-empty-message --file - [3ms]
2025-06-24 18:23:29.709 [info] > git config --get commit.template [0ms]
2025-06-24 18:23:29.724 [info] > git config --get commit.template [1ms]
2025-06-24 18:23:29.737 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:23:29.737 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [0ms]
2025-06-24 18:23:29.765 [info] > git status -z -uall [15ms]
2025-06-24 18:23:29.766 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-24 18:23:29.784 [info] > git config --get --local branch.master.github-pr-owner-number [1ms]
2025-06-24 18:23:29.784 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:23:29.799 [info] > git config --get --local branch.master.remote [2ms]
2025-06-24 18:23:29.799 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:23:29.831 [info] > git config --get --local branch.master.merge [15ms]
2025-06-24 18:23:29.831 [warning] [Git][config] git config failed: Failed to execute git
2025-06-24 18:23:29.833 [info] > git config --get commit.template [2ms]
2025-06-24 18:23:29.849 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:23:29.850 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:23:29.875 [info] > git status -z -uall [12ms]
2025-06-24 18:23:29.875 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:23:30.173 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:23:30.173 [info] > git config --get commit.template [13ms]
2025-06-24 18:23:30.193 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [20ms]
2025-06-24 18:23:30.221 [info] > git status -z -uall [13ms]
2025-06-24 18:23:30.221 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:23:35.236 [info] > git config --get commit.template [0ms]
2025-06-24 18:23:35.250 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:23:35.251 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:23:35.277 [info] > git status -z -uall [14ms]
2025-06-24 18:23:35.277 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [0ms]
2025-06-24 18:23:37.740 [info] > git show --textconv :modules/onScreenDisplay/OnScreenDisplayBrightness.qml [13ms]
2025-06-24 18:23:37.741 [info] > git ls-files --stage -- modules/onScreenDisplay/OnScreenDisplayBrightness.qml [1ms]
2025-06-24 18:23:37.755 [info] > git cat-file -s 765386bc87d518aaa2a10787fc221a3b9ca9c515 [2ms]
2025-06-24 18:23:37.790 [info] > git blame --root --incremental 46897bcd5144bf5e8efc934c0e1af4410b34a0d3 -- modules/onScreenDisplay/OnScreenDisplayBrightness.qml [1ms]
2025-06-24 18:23:38.016 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-24 18:23:42.235 [info] > git show --textconv HEAD:hyprland.conf [50ms]
2025-06-24 18:23:42.235 [info] > git show --textconv :hyprland.conf [32ms]
2025-06-24 18:23:42.235 [info] > git ls-files --stage -- hyprland.conf [2ms]
2025-06-24 18:23:42.248 [info] > git ls-tree -l HEAD -- hyprland.conf [30ms]
2025-06-24 18:23:42.249 [info] > git cat-file -s c80e1e807469a3a9c66ecad53de6b0730b2ca543 [1ms]
2025-06-24 18:23:42.302 [info] > git blame --root --incremental 9f8b79d3dd8dded0caf2a24b7ffc11625c4f1962 -- hyprland.conf [1ms]
2025-06-24 18:23:42.750 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-24 18:23:43.355 [info] > git show --textconv HEAD:custom/env.conf [15ms]
2025-06-24 18:23:43.355 [info] > git ls-tree -l HEAD -- custom/env.conf [3ms]
2025-06-24 18:23:43.384 [info] > git blame --root --incremental 9f8b79d3dd8dded0caf2a24b7ffc11625c4f1962 -- custom/env.conf [1ms]
2025-06-24 18:23:43.867 [info] > git check-ignore -v -z --stdin [0ms]
2025-06-24 18:23:44.137 [info] > git show --textconv HEAD:custom/execs.conf [17ms]
2025-06-24 18:23:44.137 [info] > git ls-tree -l HEAD -- custom/execs.conf [3ms]
2025-06-24 18:23:44.164 [info] > git blame --root --incremental 9f8b79d3dd8dded0caf2a24b7ffc11625c4f1962 -- custom/execs.conf [1ms]
2025-06-24 18:23:46.970 [info] > git add -A -- custom/env.conf custom/execs.conf [1ms]
2025-06-24 18:23:46.989 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:23:47.010 [info] > git config --get commit.template [22ms]
2025-06-24 18:23:47.010 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:23:47.042 [info] > git status -z -uall [16ms]
2025-06-24 18:23:47.042 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:23:47.395 [info] > git show --textconv HEAD:custom/execs.conf [19ms]
2025-06-24 18:23:47.396 [info] > git ls-tree -l HEAD -- custom/execs.conf [2ms]
2025-06-24 18:23:48.198 [info] > git config --get commit.template [13ms]
2025-06-24 18:23:48.198 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:23:48.199 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:23:48.227 [info] > git status -z -uall [15ms]
2025-06-24 18:23:48.228 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:23:48.312 [info] > git ls-files --stage -- custom/execs.conf [13ms]
2025-06-24 18:23:48.327 [info] > git ls-tree -l HEAD -- custom/execs.conf [15ms]
2025-06-24 18:23:48.329 [info] > git cat-file -s 15d6fb7b14bf37a0929799dc11e21d059025a8de [3ms]
2025-06-24 18:23:48.357 [info] > git show --textconv HEAD:custom/execs.conf [14ms]
2025-06-24 18:23:48.358 [info] > git show --textconv :custom/execs.conf [1ms]
2025-06-24 18:24:02.966 [info] > git config --get commit.template [15ms]
2025-06-24 18:24:02.966 [info] > git ls-files --stage -- custom/execs.conf [1ms]
2025-06-24 18:24:02.992 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:24:02.993 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:24:03.019 [info] > git cat-file -s 15d6fb7b14bf37a0929799dc11e21d059025a8de [40ms]
2025-06-24 18:24:03.020 [info] > git status -z -uall [15ms]
2025-06-24 18:24:03.020 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:24:32.024 [info] > git config --get commit.template [14ms]
2025-06-24 18:24:32.025 [info] > git ls-files --stage -- custom/execs.conf [0ms]
2025-06-24 18:24:32.038 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:24:32.051 [info] > git cat-file -s 15d6fb7b14bf37a0929799dc11e21d059025a8de [13ms]
2025-06-24 18:24:32.052 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:24:32.079 [info] > git status -z -uall [15ms]
2025-06-24 18:24:32.080 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-24 18:24:33.075 [info] > git show --textconv HEAD:hyprland.conf [37ms]
2025-06-24 18:24:33.075 [info] > git show --textconv :hyprland.conf [25ms]
2025-06-24 18:24:33.075 [info] > git ls-files --stage -- hyprland.conf [1ms]
2025-06-24 18:24:33.086 [info] > git ls-tree -l HEAD -- hyprland.conf [24ms]
2025-06-24 18:24:33.087 [info] > git cat-file -s c80e1e807469a3a9c66ecad53de6b0730b2ca543 [1ms]
2025-06-24 18:24:42.260 [info] > git ls-tree -l HEAD -- hyprland.conf [12ms]
2025-06-24 18:24:42.261 [info] > git config --get commit.template [26ms]
2025-06-24 18:24:42.261 [info] > git ls-files --stage -- hyprland.conf [1ms]
2025-06-24 18:24:42.288 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:24:42.289 [info] > git cat-file -s c80e1e807469a3a9c66ecad53de6b0730b2ca543 [13ms]
2025-06-24 18:24:42.290 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [2ms]
2025-06-24 18:24:42.318 [info] > git status -z -uall [14ms]
2025-06-24 18:24:42.318 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:24:42.955 [info] > git check-ignore -v -z --stdin [0ms]
2025-06-24 18:24:44.408 [info] > git show --textconv :hyprland.conf [15ms]
2025-06-24 18:24:44.408 [info] > git ls-files --stage -- hyprland.conf [1ms]
2025-06-24 18:24:44.423 [info] > git cat-file -s c80e1e807469a3a9c66ecad53de6b0730b2ca543 [1ms]
2025-06-24 18:24:44.662 [info] > git show --textconv HEAD:hyprland.conf [12ms]
2025-06-24 18:24:44.663 [info] > git ls-tree -l HEAD -- hyprland.conf [1ms]
2025-06-24 18:24:47.336 [info] > git config --get commit.template [0ms]
2025-06-24 18:24:47.349 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:24:47.350 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:24:47.374 [info] > git status -z -uall [12ms]
2025-06-24 18:24:47.374 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:24:49.083 [info] > git ls-files --stage -- hyprland.conf [0ms]
2025-06-24 18:24:49.095 [info] > git cat-file -s c80e1e807469a3a9c66ecad53de6b0730b2ca543 [0ms]
2025-06-24 18:24:50.471 [info] > git checkout -q -- hyprland.conf [13ms]
2025-06-24 18:24:50.486 [info] > git ls-files --stage -- hyprland.conf [15ms]
2025-06-24 18:24:50.498 [info] > git config --get commit.template [13ms]
2025-06-24 18:24:50.499 [info] > git cat-file -s c80e1e807469a3a9c66ecad53de6b0730b2ca543 [1ms]
2025-06-24 18:24:50.511 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:24:50.511 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [0ms]
2025-06-24 18:24:50.540 [info] > git status -z -uall [15ms]
2025-06-24 18:24:50.541 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-24 18:24:51.709 [info] > git show --textconv :hyprlock.conf [13ms]
2025-06-24 18:24:51.733 [info] > git ls-files --stage -- hyprlock.conf [24ms]
2025-06-24 18:24:51.747 [info] > git cat-file -s 149f58ddb432eff8680c9160d1e08f8595ae02b7 [1ms]
2025-06-24 18:24:51.831 [info] > git ls-files --stage -- hyprlock.conf [1ms]
2025-06-24 18:24:51.845 [info] > git cat-file -s 149f58ddb432eff8680c9160d1e08f8595ae02b7 [0ms]
2025-06-24 18:24:51.869 [info] > git show --textconv :hyprlock.conf [1ms]
2025-06-24 18:24:51.994 [info] > git show --textconv HEAD:hyprlock.conf [13ms]
2025-06-24 18:24:51.994 [info] > git ls-tree -l HEAD -- hyprlock.conf [0ms]
2025-06-24 18:24:52.389 [info] > git config --get commit.template [1ms]
2025-06-24 18:24:52.402 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:24:52.403 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:24:52.427 [info] > git status -z -uall [13ms]
2025-06-24 18:24:52.427 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:24:57.442 [info] > git config --get commit.template [1ms]
2025-06-24 18:24:57.455 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:24:57.456 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:24:57.481 [info] > git status -z -uall [13ms]
2025-06-24 18:24:57.482 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:25:00.527 [info] > git show --textconv :hyprland/colors.conf [38ms]
2025-06-24 18:25:00.527 [info] > git ls-files --stage -- hyprland/colors.conf [25ms]
2025-06-24 18:25:00.540 [info] > git cat-file -s 83fcee1f6b052edff5e0e4d4c7b9ea85671ba0c0 [1ms]
2025-06-24 18:25:00.787 [info] > git show --textconv HEAD:hyprland/colors.conf [16ms]
2025-06-24 18:25:00.787 [info] > git ls-tree -l HEAD -- hyprland/colors.conf [1ms]
2025-06-24 18:25:01.019 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-24 18:25:03.297 [info] > git ls-files --stage -- hyprlock.conf [4ms]
2025-06-24 18:25:03.311 [info] > git cat-file -s 149f58ddb432eff8680c9160d1e08f8595ae02b7 [1ms]
2025-06-24 18:25:03.520 [info] > git show --textconv HEAD:hyprlock.conf [14ms]
2025-06-24 18:25:03.521 [info] > git ls-tree -l HEAD -- hyprlock.conf [1ms]
2025-06-24 18:25:03.871 [info] > git blame --root --incremental 9f8b79d3dd8dded0caf2a24b7ffc11625c4f1962 -- hyprlock.conf [1ms]
2025-06-24 18:25:07.420 [info] > git ls-files --stage -- hyprlock.conf [1ms]
2025-06-24 18:25:07.433 [info] > git cat-file -s 149f58ddb432eff8680c9160d1e08f8595ae02b7 [0ms]
2025-06-24 18:25:08.779 [info] > git checkout -q -- hyprlock.conf [15ms]
2025-06-24 18:25:08.793 [info] > git ls-files --stage -- hyprlock.conf [15ms]
2025-06-24 18:25:08.807 [info] > git config --get commit.template [14ms]
2025-06-24 18:25:08.807 [info] > git cat-file -s 149f58ddb432eff8680c9160d1e08f8595ae02b7 [1ms]
2025-06-24 18:25:08.821 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:25:08.822 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:25:08.852 [info] > git status -z -uall [16ms]
2025-06-24 18:25:08.852 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-24 18:25:09.858 [info] > git show --textconv HEAD:hyprland/keybinds.conf [14ms]
2025-06-24 18:25:09.860 [info] > git ls-tree -l HEAD -- hyprland/keybinds.conf [2ms]
2025-06-24 18:25:10.005 [info] > git config --get commit.template [1ms]
2025-06-24 18:25:10.020 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:25:10.020 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [0ms]
2025-06-24 18:25:10.047 [info] > git status -z -uall [13ms]
2025-06-24 18:25:10.047 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:25:10.120 [info] > git ls-files --stage -- hyprlock.conf [16ms]
2025-06-24 18:25:10.136 [info] > git cat-file -s 149f58ddb432eff8680c9160d1e08f8595ae02b7 [2ms]
2025-06-24 18:25:10.136 [info] > git ls-tree -l HEAD -- hyprland/keybinds.conf [17ms]
2025-06-24 18:25:10.167 [info] > git show --textconv :hyprlock.conf [16ms]
2025-06-24 18:25:10.168 [info] > git show --textconv HEAD:hyprland/keybinds.conf [1ms]
2025-06-24 18:25:13.729 [info] > git ls-tree -l HEAD -- hyprland/keybinds.conf [1ms]
2025-06-24 18:25:14.640 [info] > git show --textconv HEAD:hyprlock.conf [41ms]
2025-06-24 18:25:14.641 [info] > git show --textconv :hyprlock.conf [27ms]
2025-06-24 18:25:14.641 [info] > git ls-tree -l HEAD -- hyprlock.conf [14ms]
2025-06-24 18:25:14.642 [info] > git ls-files --stage -- hyprlock.conf [2ms]
2025-06-24 18:25:14.663 [info] > git cat-file -s 149f58ddb432eff8680c9160d1e08f8595ae02b7 [3ms]
2025-06-24 18:25:15.074 [info] > git show --textconv HEAD:hyprland.conf [42ms]
2025-06-24 18:25:15.074 [info] > git show --textconv :hyprland.conf [29ms]
2025-06-24 18:25:15.074 [info] > git ls-files --stage -- hyprland.conf [1ms]
2025-06-24 18:25:15.086 [info] > git ls-tree -l HEAD -- hyprland.conf [26ms]
2025-06-24 18:25:15.102 [info] > git cat-file -s c80e1e807469a3a9c66ecad53de6b0730b2ca543 [15ms]
2025-06-24 18:25:15.103 [info] > git config --get commit.template [2ms]
2025-06-24 18:25:15.119 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:25:15.122 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [4ms]
2025-06-24 18:25:15.167 [info] > git status -z -uall [31ms]
2025-06-24 18:25:15.167 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-24 18:25:15.167 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [15ms]
2025-06-24 18:25:17.864 [info] > git show --textconv HEAD:hyprland/keybinds.conf [16ms]
2025-06-24 18:25:17.864 [info] > git ls-tree -l HEAD -- hyprland/keybinds.conf [2ms]
2025-06-24 18:25:23.532 [info] > git ls-tree -l HEAD -- hyprland/keybinds.conf [1ms]
2025-06-24 18:25:24.461 [info] > git checkout -q -- hyprland/keybinds.conf [14ms]
2025-06-24 18:25:24.475 [info] > git ls-tree -l HEAD -- hyprland/keybinds.conf [15ms]
2025-06-24 18:25:24.476 [info] > git config --get commit.template [1ms]
2025-06-24 18:25:24.491 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:25:24.491 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [0ms]
2025-06-24 18:25:24.521 [info] > git status -z -uall [15ms]
2025-06-24 18:25:24.522 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:25:24.830 [info] > git blame --root --incremental 9f8b79d3dd8dded0caf2a24b7ffc11625c4f1962 -- hyprland/keybinds.conf [4ms]
2025-06-24 18:25:25.622 [info] > git config --get commit.template [11ms]
2025-06-24 18:25:25.636 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:25:25.637 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:25:25.666 [info] > git status -z -uall [14ms]
2025-06-24 18:25:25.666 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:25:25.730 [info] > git ls-tree -l HEAD -- hyprland/keybinds.conf [1ms]
2025-06-24 18:25:25.730 [info] > git ls-files --stage -- hyprlock.conf [15ms]
2025-06-24 18:25:25.760 [info] > git show --textconv HEAD:hyprland/keybinds.conf [1ms]
2025-06-24 18:25:25.760 [info] > git cat-file -s 149f58ddb432eff8680c9160d1e08f8595ae02b7 [17ms]
2025-06-24 18:25:25.775 [info] > git show --textconv :hyprlock.conf [1ms]
2025-06-24 18:25:26.040 [info] > git show --textconv :hyprland/colors.conf [14ms]
2025-06-24 18:25:26.052 [info] > git ls-files --stage -- hyprland/colors.conf [13ms]
2025-06-24 18:25:26.066 [info] > git cat-file -s 83fcee1f6b052edff5e0e4d4c7b9ea85671ba0c0 [1ms]
2025-06-24 18:25:26.313 [info] > git show --textconv HEAD:hyprland/colors.conf [13ms]
2025-06-24 18:25:26.314 [info] > git ls-tree -l HEAD -- hyprland/colors.conf [1ms]
2025-06-24 18:25:30.697 [info] > git config --get commit.template [15ms]
2025-06-24 18:25:30.698 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:25:30.698 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:25:30.729 [info] > git status -z -uall [15ms]
2025-06-24 18:25:30.730 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:26:03.367 [info] > git ls-files --stage -- hyprland/colors.conf [14ms]
2025-06-24 18:26:03.390 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:26:03.390 [info] > git config --get commit.template [24ms]
2025-06-24 18:26:03.391 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:26:03.415 [info] > git cat-file -s 83fcee1f6b052edff5e0e4d4c7b9ea85671ba0c0 [38ms]
2025-06-24 18:26:03.416 [info] > git status -z -uall [12ms]
2025-06-24 18:26:03.416 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:26:05.401 [info] > git ls-files --stage -- hyprland/colors.conf [1ms]
2025-06-24 18:26:05.414 [info] > git cat-file -s 83fcee1f6b052edff5e0e4d4c7b9ea85671ba0c0 [0ms]
2025-06-24 18:26:24.118 [info] > git config --get commit.template [17ms]
2025-06-24 18:26:24.118 [info] > git ls-files --stage -- hyprland/colors.conf [2ms]
2025-06-24 18:26:24.143 [info] [Git][getRemotes] No remotes found in the git config file
2025-06-24 18:26:24.144 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [1ms]
2025-06-24 18:26:24.174 [info] > git cat-file -s 83fcee1f6b052edff5e0e4d4c7b9ea85671ba0c0 [44ms]
2025-06-24 18:26:24.175 [info] > git status -z -uall [16ms]
2025-06-24 18:26:24.175 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-24 18:26:25.870 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-24 18:26:28.515 [info] > git rev-parse --show-toplevel [2ms]
2025-06-24 18:26:28.515 [info] fatal: not a git repository (or any of the parent directories): .git
