[ActionPlugins][0]
MiddleButton;NoModifier=org.kde.paste
RightButton;NoModifier=org.kde.contextmenu

[ActionPlugins][1]
RightButton;NoModifier=org.kde.contextmenu

[Containments][57]
ItemGeometries-1638x1229=
ItemGeometries-2163x1352=
ItemGeometriesHorizontal=
activityId=42caa52b-c291-45a0-b1ac-3fdaa052cc3a
formfactor=0
immutability=1
lastScreen=1
location=0
plugin=org.kde.plasma.folder
wallpaperplugin=org.kde.image

[Containments][57][General]
positions={"1638x1229":[],"2163x1352":[]}

[Containments][58]
ItemGeometries-1269x1548=
ItemGeometries-1638x1229=
ItemGeometries-2163x1352=
ItemGeometriesHorizontal=
ItemGeometriesVertical=
activityId=42caa52b-c291-45a0-b1ac-3fdaa052cc3a
formfactor=0
immutability=1
lastScreen=0
location=0
plugin=org.kde.plasma.folder
wallpaperplugin=org.kde.image

[Containments][58][General]
positions={"1269x1548":[],"1638x1229":[],"2163x1352":[]}

[Containments][59]
activityId=
formfactor=2
immutability=1
lastScreen=1
location=4
plugin=org.kde.panel
wallpaperplugin=org.kde.image

[Containments][59][Applets][60]
immutability=1
plugin=org.kde.plasma.kickoff

[Containments][59][Applets][60][Configuration]
PreloadWeight=100
popupHeight=508
popupWidth=671

[Containments][59][Applets][60][Configuration][General]
favoritesPortedToKAstats=true

[Containments][59][Applets][61]
immutability=1
plugin=org.kde.plasma.pager

[Containments][59][Applets][62]
immutability=1
plugin=org.kde.plasma.icontasks

[Containments][59][Applets][62][Configuration][ConfigDialog]
DialogHeight=540
DialogWidth=720

[Containments][59][Applets][63]
immutability=1
plugin=org.kde.plasma.marginsseparator

[Containments][59][Applets][64]
activityId=
formfactor=0
immutability=1
lastScreen=-1
location=0
plugin=org.kde.plasma.systemtray
popupHeight=438
popupWidth=438
wallpaperplugin=org.kde.image

[Containments][59][Applets][64][Applets][114]
immutability=1
plugin=org.kde.plasma.mediacontroller

[Containments][59][Applets][64][Applets][65]
immutability=1
plugin=org.kde.kdeconnect

[Containments][59][Applets][64][Applets][66]
immutability=1
plugin=org.kde.merkuro.contact.applet

[Containments][59][Applets][64][Applets][67]
immutability=1
plugin=org.kde.plasma.cameraindicator

[Containments][59][Applets][64][Applets][68]
immutability=1
plugin=org.kde.plasma.clipboard

[Containments][59][Applets][64][Applets][69]
immutability=1
plugin=org.kde.plasma.devicenotifier

[Containments][59][Applets][64][Applets][70]
immutability=1
plugin=org.kde.plasma.manage-inputmethod

[Containments][59][Applets][64][Applets][71]
immutability=1
plugin=org.kde.plasma.notifications

[Containments][59][Applets][64][Applets][72]
immutability=1
plugin=org.kde.plasma.keyboardindicator

[Containments][59][Applets][64][Applets][73]
immutability=1
plugin=org.kde.plasma.weather

[Containments][59][Applets][64][Applets][74]
immutability=1
plugin=org.kde.plasma.keyboardlayout

[Containments][59][Applets][64][Applets][75]
immutability=1
plugin=org.kde.plasma.vault

[Containments][59][Applets][64][Applets][76]
immutability=1
plugin=org.kde.plasma.printmanager

[Containments][59][Applets][64][Applets][77]
immutability=1
plugin=org.kde.kscreen

[Containments][59][Applets][64][Applets][78]
immutability=1
plugin=org.kde.plasma.volume

[Containments][59][Applets][64][Applets][78][Configuration][General]
migrated=true

[Containments][59][Applets][64][Applets][81]
immutability=1
plugin=org.kde.plasma.bluetooth

[Containments][59][Applets][64][Applets][81][Configuration]
PreloadWeight=26

[Containments][59][Applets][64][Applets][82]
immutability=1
plugin=org.kde.plasma.networkmanagement

[Containments][59][Applets][64][Applets][83]
immutability=1
plugin=org.kde.plasma.battery

[Containments][59][Applets][64][Applets][83][Configuration]
PreloadWeight=10

[Containments][59][Applets][64][Applets][84]
immutability=1
plugin=org.kde.plasma.brightness

[Containments][59][Applets][64][Applets][84][Configuration]
PreloadWeight=10

[Containments][59][Applets][64][General]
extraItems=org.kde.kdeconnect,org.kde.merkuro.contact.applet,org.kde.plasma.bluetooth,org.kde.plasma.cameraindicator,org.kde.plasma.clipboard,org.kde.plasma.devicenotifier,org.kde.plasma.manage-inputmethod,org.kde.plasma.notifications,org.kde.plasma.keyboardindicator,org.kde.plasma.weather,org.kde.plasma.battery,org.kde.plasma.brightness,org.kde.plasma.keyboardlayout,org.kde.plasma.networkmanagement,org.kde.plasma.vault,org.kde.plasma.printmanager,org.kde.plasma.mediacontroller,org.kde.kscreen,org.kde.plasma.volume
knownItems=org.kde.kdeconnect,org.kde.merkuro.contact.applet,org.kde.plasma.bluetooth,org.kde.plasma.cameraindicator,org.kde.plasma.clipboard,org.kde.plasma.devicenotifier,org.kde.plasma.manage-inputmethod,org.kde.plasma.notifications,org.kde.plasma.keyboardindicator,org.kde.plasma.weather,org.kde.plasma.battery,org.kde.plasma.brightness,org.kde.plasma.keyboardlayout,org.kde.plasma.networkmanagement,org.kde.plasma.vault,org.kde.plasma.printmanager,org.kde.plasma.mediacontroller,org.kde.kscreen,org.kde.plasma.volume

[Containments][59][Applets][79]
immutability=1
plugin=org.kde.plasma.digitalclock

[Containments][59][Applets][79][Configuration]
popupHeight=400
popupWidth=560

[Containments][59][Applets][80]
immutability=1
plugin=org.kde.plasma.showdesktop

[Containments][59][General]
AppletOrder=60;61;62;63;64;79;80

[Containments][87]
activityId=
formfactor=2
immutability=1
lastScreen=0
location=4
plugin=org.kde.panel
wallpaperplugin=org.kde.image

[Containments][87][Applets][111]
immutability=1
plugin=org.kde.plasma.digitalclock

[Containments][87][Applets][111][Configuration]
popupHeight=400
popupWidth=560

[Containments][87][Applets][112]
immutability=1
plugin=org.kde.plasma.showdesktop

[Containments][87][Applets][88]
immutability=1
plugin=org.kde.plasma.kickoff

[Containments][87][Applets][88][Configuration]
PreloadWeight=100
popupHeight=508
popupWidth=671

[Containments][87][Applets][88][Configuration][General]
favoritesPortedToKAstats=true

[Containments][87][Applets][89]
immutability=1
plugin=org.kde.plasma.pager

[Containments][87][Applets][90]
immutability=1
plugin=org.kde.plasma.icontasks

[Containments][87][Applets][90][Configuration][ConfigDialog]
DialogHeight=540
DialogWidth=720

[Containments][87][Applets][91]
immutability=1
plugin=org.kde.plasma.marginsseparator

[Containments][87][Applets][92]
activityId=
formfactor=0
immutability=1
lastScreen=-1
location=0
plugin=org.kde.plasma.systemtray
popupHeight=438
popupWidth=438
wallpaperplugin=org.kde.image

[Containments][87][Applets][92][Applets][100]
immutability=1
plugin=org.kde.plasma.keyboardindicator

[Containments][87][Applets][92][Applets][101]
immutability=1
plugin=org.kde.plasma.weather

[Containments][87][Applets][92][Applets][102]
immutability=1
plugin=org.kde.plasma.keyboardlayout

[Containments][87][Applets][92][Applets][103]
immutability=1
plugin=org.kde.plasma.vault

[Containments][87][Applets][92][Applets][104]
immutability=1
plugin=org.kde.plasma.printmanager

[Containments][87][Applets][92][Applets][105]
immutability=1
plugin=org.kde.kscreen

[Containments][87][Applets][92][Applets][106]
immutability=1
plugin=org.kde.plasma.volume

[Containments][87][Applets][92][Applets][106][Configuration]
PreloadWeight=55

[Containments][87][Applets][92][Applets][106][Configuration][General]
migrated=true

[Containments][87][Applets][92][Applets][107]
immutability=1
plugin=org.kde.plasma.bluetooth

[Containments][87][Applets][92][Applets][107][Configuration]
PreloadWeight=31

[Containments][87][Applets][92][Applets][108]
immutability=1
plugin=org.kde.plasma.networkmanagement

[Containments][87][Applets][92][Applets][108][Configuration]
PreloadWeight=65

[Containments][87][Applets][92][Applets][109]
immutability=1
plugin=org.kde.plasma.battery

[Containments][87][Applets][92][Applets][109][Configuration]
PreloadWeight=15

[Containments][87][Applets][92][Applets][110]
immutability=1
plugin=org.kde.plasma.brightness

[Containments][87][Applets][92][Applets][110][Configuration]
PreloadWeight=20

[Containments][87][Applets][92][Applets][113]
immutability=1
plugin=org.kde.plasma.mediacontroller

[Containments][87][Applets][92][Applets][93]
immutability=1
plugin=org.kde.kdeconnect

[Containments][87][Applets][92][Applets][94]
immutability=1
plugin=org.kde.merkuro.contact.applet

[Containments][87][Applets][92][Applets][95]
immutability=1
plugin=org.kde.plasma.cameraindicator

[Containments][87][Applets][92][Applets][96]
immutability=1
plugin=org.kde.plasma.clipboard

[Containments][87][Applets][92][Applets][96][Configuration]
PreloadWeight=55

[Containments][87][Applets][92][Applets][97]
immutability=1
plugin=org.kde.plasma.devicenotifier

[Containments][87][Applets][92][Applets][97][Configuration]
PreloadWeight=60

[Containments][87][Applets][92][Applets][98]
immutability=1
plugin=org.kde.plasma.manage-inputmethod

[Containments][87][Applets][92][Applets][99]
immutability=1
plugin=org.kde.plasma.notifications

[Containments][87][Applets][92][General]
extraItems=org.kde.kdeconnect,org.kde.merkuro.contact.applet,org.kde.plasma.bluetooth,org.kde.plasma.cameraindicator,org.kde.plasma.clipboard,org.kde.plasma.devicenotifier,org.kde.plasma.manage-inputmethod,org.kde.plasma.notifications,org.kde.plasma.keyboardindicator,org.kde.plasma.weather,org.kde.plasma.battery,org.kde.plasma.brightness,org.kde.plasma.keyboardlayout,org.kde.plasma.networkmanagement,org.kde.plasma.vault,org.kde.plasma.printmanager,org.kde.plasma.mediacontroller,org.kde.kscreen,org.kde.plasma.volume
knownItems=org.kde.kdeconnect,org.kde.merkuro.contact.applet,org.kde.plasma.bluetooth,org.kde.plasma.cameraindicator,org.kde.plasma.clipboard,org.kde.plasma.devicenotifier,org.kde.plasma.manage-inputmethod,org.kde.plasma.notifications,org.kde.plasma.keyboardindicator,org.kde.plasma.weather,org.kde.plasma.battery,org.kde.plasma.brightness,org.kde.plasma.keyboardlayout,org.kde.plasma.networkmanagement,org.kde.plasma.vault,org.kde.plasma.printmanager,org.kde.plasma.mediacontroller,org.kde.kscreen,org.kde.plasma.volume

[Containments][87][General]
AppletOrder=88;89;90;91;92;111;112

[ScreenMapping]
itemsOnDisabledScreens=
