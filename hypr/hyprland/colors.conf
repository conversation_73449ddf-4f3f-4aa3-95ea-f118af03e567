general {
    col.active_border = rgba(22191b39)
    col.inactive_border = rgba(84737530)
}

misc {
    background_color = rgba(191113FF)
}

plugin {
    hyprbars {
        # Honestly idk if it works like css, but well, why not
        bar_text_font = <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> One Sans, Reddit Sans, Inter, Roboto, Ubuntu, Noto Sans, sans-serif
        bar_height = 30
        bar_padding = 10
        bar_button_padding = 5
        bar_precedence_over_border = true
        bar_part_of_window = true

        bar_color = rgba(fff8f7FF)
        col.text = rgba(22191bFF)


        # example buttons (R -> L)
        # hyprbars-button = color, size, on-click
        hyprbars-button = rgb(22191b), 13, 󰖭, hyprctl dispatch killactive
        hyprbars-button = rgb(22191b), 13, 󰖯, hyprctl dispatch fullscreen 1
        hyprbars-button = rgb(22191b), 13, 󰖰, hyprctl dispatch movetoworkspacesilent special
    }
}

windowrulev2 = bordercolor rgba(8e4958AA) rgba(8e495877),pinned:1
