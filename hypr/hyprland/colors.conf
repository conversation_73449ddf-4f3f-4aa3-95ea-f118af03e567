general {
    col.active_border = rgba(efdee039)
    col.inactive_border = rgba(9f8c8e30)
}

misc {
    background_color = rgba(191113FF)
}

plugin {
    hyprbars {
        # Honestly idk if it works like css, but well, why not
        bar_text_font = <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, AR One Sans, Reddit Sans, Inter, Roboto, Ubuntu, Noto Sans, sans-serif
        bar_height = 30
        bar_padding = 10
        bar_button_padding = 5
        bar_precedence_over_border = true
        bar_part_of_window = true

        bar_color = rgba(191113FF)
        col.text = rgba(efdee0FF)


        # example buttons (R -> L)
        # hyprbars-button = color, size, on-click
        hyprbars-button = rgb(efdee0), 13, 󰖭, hyprctl dispatch killactive
        hyprbars-button = rgb(efdee0), 13, 󰖯, hyprctl dispatch fullscreen 1
        hyprbars-button = rgb(efdee0), 13, 󰖰, hyprctl dispatch movetoworkspacesilent special
    }
}

windowrulev2 = bordercolor rgba(ffb1c0AA) rgba(ffb1c077),pinned:1
